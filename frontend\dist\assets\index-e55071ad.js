var Oy=Object.defineProperty;var Dy=(t,e,n)=>e in t?Oy(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var F=(t,e,n)=>(Dy(t,typeof e!="symbol"?e+"":e,n),n);function Ay(t,e){for(var n=0;n<e.length;n++){const s=e[n];if(typeof s!="string"&&!Array.isArray(s)){for(const i in s)if(i!=="default"&&!(i in t)){const r=Object.getOwnPropertyDescriptor(s,i);r&&Object.defineProperty(t,i,r.get?r:{enumerable:!0,get:()=>s[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const r of i)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const r={};return i.integrity&&(r.integrity=i.integrity),i.referrerPolicy&&(r.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?r.credentials="include":i.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(i){if(i.ep)return;i.ep=!0;const r=n(i);fetch(i.href,r)}})();function Ly(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Vf={exports:{}},ea={},Yf={exports:{}},q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ir=Symbol.for("react.element"),Fy=Symbol.for("react.portal"),By=Symbol.for("react.fragment"),Iy=Symbol.for("react.strict_mode"),zy=Symbol.for("react.profiler"),Wy=Symbol.for("react.provider"),$y=Symbol.for("react.context"),Hy=Symbol.for("react.forward_ref"),Uy=Symbol.for("react.suspense"),Vy=Symbol.for("react.memo"),Yy=Symbol.for("react.lazy"),Qu=Symbol.iterator;function Xy(t){return t===null||typeof t!="object"?null:(t=Qu&&t[Qu]||t["@@iterator"],typeof t=="function"?t:null)}var Xf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Kf=Object.assign,qf={};function $s(t,e,n){this.props=t,this.context=e,this.refs=qf,this.updater=n||Xf}$s.prototype.isReactComponent={};$s.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};$s.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function Qf(){}Qf.prototype=$s.prototype;function Tc(t,e,n){this.props=t,this.context=e,this.refs=qf,this.updater=n||Xf}var Rc=Tc.prototype=new Qf;Rc.constructor=Tc;Kf(Rc,$s.prototype);Rc.isPureReactComponent=!0;var Gu=Array.isArray,Gf=Object.prototype.hasOwnProperty,Oc={current:null},Jf={key:!0,ref:!0,__self:!0,__source:!0};function Zf(t,e,n){var s,i={},r=null,o=null;if(e!=null)for(s in e.ref!==void 0&&(o=e.ref),e.key!==void 0&&(r=""+e.key),e)Gf.call(e,s)&&!Jf.hasOwnProperty(s)&&(i[s]=e[s]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),c=0;c<a;c++)l[c]=arguments[c+2];i.children=l}if(t&&t.defaultProps)for(s in a=t.defaultProps,a)i[s]===void 0&&(i[s]=a[s]);return{$$typeof:ir,type:t,key:r,ref:o,props:i,_owner:Oc.current}}function Ky(t,e){return{$$typeof:ir,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function Dc(t){return typeof t=="object"&&t!==null&&t.$$typeof===ir}function qy(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var Ju=/\/+/g;function Ra(t,e){return typeof t=="object"&&t!==null&&t.key!=null?qy(""+t.key):e.toString(36)}function Xr(t,e,n,s,i){var r=typeof t;(r==="undefined"||r==="boolean")&&(t=null);var o=!1;if(t===null)o=!0;else switch(r){case"string":case"number":o=!0;break;case"object":switch(t.$$typeof){case ir:case Fy:o=!0}}if(o)return o=t,i=i(o),t=s===""?"."+Ra(o,0):s,Gu(i)?(n="",t!=null&&(n=t.replace(Ju,"$&/")+"/"),Xr(i,e,n,"",function(c){return c})):i!=null&&(Dc(i)&&(i=Ky(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Ju,"$&/")+"/")+t)),e.push(i)),1;if(o=0,s=s===""?".":s+":",Gu(t))for(var a=0;a<t.length;a++){r=t[a];var l=s+Ra(r,a);o+=Xr(r,e,n,l,i)}else if(l=Xy(t),typeof l=="function")for(t=l.call(t),a=0;!(r=t.next()).done;)r=r.value,l=s+Ra(r,a++),o+=Xr(r,e,n,l,i);else if(r==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return o}function mr(t,e,n){if(t==null)return t;var s=[],i=0;return Xr(t,s,"","",function(r){return e.call(n,r,i++)}),s}function Qy(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var Ue={current:null},Kr={transition:null},Gy={ReactCurrentDispatcher:Ue,ReactCurrentBatchConfig:Kr,ReactCurrentOwner:Oc};function ep(){throw Error("act(...) is not supported in production builds of React.")}q.Children={map:mr,forEach:function(t,e,n){mr(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return mr(t,function(){e++}),e},toArray:function(t){return mr(t,function(e){return e})||[]},only:function(t){if(!Dc(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};q.Component=$s;q.Fragment=By;q.Profiler=zy;q.PureComponent=Tc;q.StrictMode=Iy;q.Suspense=Uy;q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Gy;q.act=ep;q.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var s=Kf({},t.props),i=t.key,r=t.ref,o=t._owner;if(e!=null){if(e.ref!==void 0&&(r=e.ref,o=Oc.current),e.key!==void 0&&(i=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)Gf.call(e,l)&&!Jf.hasOwnProperty(l)&&(s[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)s.children=n;else if(1<l){a=Array(l);for(var c=0;c<l;c++)a[c]=arguments[c+2];s.children=a}return{$$typeof:ir,type:t.type,key:i,ref:r,props:s,_owner:o}};q.createContext=function(t){return t={$$typeof:$y,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:Wy,_context:t},t.Consumer=t};q.createElement=Zf;q.createFactory=function(t){var e=Zf.bind(null,t);return e.type=t,e};q.createRef=function(){return{current:null}};q.forwardRef=function(t){return{$$typeof:Hy,render:t}};q.isValidElement=Dc;q.lazy=function(t){return{$$typeof:Yy,_payload:{_status:-1,_result:t},_init:Qy}};q.memo=function(t,e){return{$$typeof:Vy,type:t,compare:e===void 0?null:e}};q.startTransition=function(t){var e=Kr.transition;Kr.transition={};try{t()}finally{Kr.transition=e}};q.unstable_act=ep;q.useCallback=function(t,e){return Ue.current.useCallback(t,e)};q.useContext=function(t){return Ue.current.useContext(t)};q.useDebugValue=function(){};q.useDeferredValue=function(t){return Ue.current.useDeferredValue(t)};q.useEffect=function(t,e){return Ue.current.useEffect(t,e)};q.useId=function(){return Ue.current.useId()};q.useImperativeHandle=function(t,e,n){return Ue.current.useImperativeHandle(t,e,n)};q.useInsertionEffect=function(t,e){return Ue.current.useInsertionEffect(t,e)};q.useLayoutEffect=function(t,e){return Ue.current.useLayoutEffect(t,e)};q.useMemo=function(t,e){return Ue.current.useMemo(t,e)};q.useReducer=function(t,e,n){return Ue.current.useReducer(t,e,n)};q.useRef=function(t){return Ue.current.useRef(t)};q.useState=function(t){return Ue.current.useState(t)};q.useSyncExternalStore=function(t,e,n){return Ue.current.useSyncExternalStore(t,e,n)};q.useTransition=function(){return Ue.current.useTransition()};q.version="18.3.1";Yf.exports=q;var P=Yf.exports;const ta=Ly(P),Jy=Ay({__proto__:null,default:ta},[P]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zy=P,e0=Symbol.for("react.element"),t0=Symbol.for("react.fragment"),n0=Object.prototype.hasOwnProperty,s0=Zy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i0={key:!0,ref:!0,__self:!0,__source:!0};function tp(t,e,n){var s,i={},r=null,o=null;n!==void 0&&(r=""+n),e.key!==void 0&&(r=""+e.key),e.ref!==void 0&&(o=e.ref);for(s in e)n0.call(e,s)&&!i0.hasOwnProperty(s)&&(i[s]=e[s]);if(t&&t.defaultProps)for(s in e=t.defaultProps,e)i[s]===void 0&&(i[s]=e[s]);return{$$typeof:e0,type:t,key:r,ref:o,props:i,_owner:s0.current}}ea.Fragment=t0;ea.jsx=tp;ea.jsxs=tp;Vf.exports=ea;var u=Vf.exports,bl={},np={exports:{}},ot={},sp={exports:{}},ip={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(D,j){var O=D.length;D.push(j);e:for(;0<O;){var W=O-1>>>1,$=D[W];if(0<i($,j))D[W]=j,D[O]=$,O=W;else break e}}function n(D){return D.length===0?null:D[0]}function s(D){if(D.length===0)return null;var j=D[0],O=D.pop();if(O!==j){D[0]=O;e:for(var W=0,$=D.length,Q=$>>>1;W<Q;){var re=2*(W+1)-1,ve=D[re],w=re+1,A=D[w];if(0>i(ve,O))w<$&&0>i(A,ve)?(D[W]=A,D[w]=O,W=w):(D[W]=ve,D[re]=O,W=re);else if(w<$&&0>i(A,O))D[W]=A,D[w]=O,W=w;else break e}}return j}function i(D,j){var O=D.sortIndex-j.sortIndex;return O!==0?O:D.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var r=performance;t.unstable_now=function(){return r.now()}}else{var o=Date,a=o.now();t.unstable_now=function(){return o.now()-a}}var l=[],c=[],d=1,h=null,f=3,p=!1,m=!1,y=!1,v=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,x=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function b(D){for(var j=n(c);j!==null;){if(j.callback===null)s(c);else if(j.startTime<=D)s(c),j.sortIndex=j.expirationTime,e(l,j);else break;j=n(c)}}function k(D){if(y=!1,b(D),!m)if(n(l)!==null)m=!0,K(_);else{var j=n(c);j!==null&&ee(k,j.startTime-D)}}function _(D,j){m=!1,y&&(y=!1,g(S),S=-1),p=!0;var O=f;try{for(b(j),h=n(l);h!==null&&(!(h.expirationTime>j)||D&&!I());){var W=h.callback;if(typeof W=="function"){h.callback=null,f=h.priorityLevel;var $=W(h.expirationTime<=j);j=t.unstable_now(),typeof $=="function"?h.callback=$:h===n(l)&&s(l),b(j)}else s(l);h=n(l)}if(h!==null)var Q=!0;else{var re=n(c);re!==null&&ee(k,re.startTime-j),Q=!1}return Q}finally{h=null,f=O,p=!1}}var C=!1,N=null,S=-1,L=5,R=-1;function I(){return!(t.unstable_now()-R<L)}function M(){if(N!==null){var D=t.unstable_now();R=D;var j=!0;try{j=N(!0,D)}finally{j?H():(C=!1,N=null)}}else C=!1}var H;if(typeof x=="function")H=function(){x(M)};else if(typeof MessageChannel<"u"){var U=new MessageChannel,z=U.port2;U.port1.onmessage=M,H=function(){z.postMessage(null)}}else H=function(){v(M,0)};function K(D){N=D,C||(C=!0,H())}function ee(D,j){S=v(function(){D(t.unstable_now())},j)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(D){D.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,K(_))},t.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<D?Math.floor(1e3/D):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(D){switch(f){case 1:case 2:case 3:var j=3;break;default:j=f}var O=f;f=j;try{return D()}finally{f=O}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(D,j){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var O=f;f=D;try{return j()}finally{f=O}},t.unstable_scheduleCallback=function(D,j,O){var W=t.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?W+O:W):O=W,D){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=O+$,D={id:d++,callback:j,priorityLevel:D,startTime:O,expirationTime:$,sortIndex:-1},O>W?(D.sortIndex=O,e(c,D),n(l)===null&&D===n(c)&&(y?(g(S),S=-1):y=!0,ee(k,O-W))):(D.sortIndex=$,e(l,D),m||p||(m=!0,K(_))),D},t.unstable_shouldYield=I,t.unstable_wrapCallback=function(D){var j=f;return function(){var O=f;f=j;try{return D.apply(this,arguments)}finally{f=O}}}})(ip);sp.exports=ip;var r0=sp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o0=P,rt=r0;function T(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var rp=new Set,Mi={};function is(t,e){Os(t,e),Os(t+"Capture",e)}function Os(t,e){for(Mi[t]=e,t=0;t<e.length;t++)rp.add(e[t])}var Xt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),_l=Object.prototype.hasOwnProperty,a0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Zu={},ed={};function l0(t){return _l.call(ed,t)?!0:_l.call(Zu,t)?!1:a0.test(t)?ed[t]=!0:(Zu[t]=!0,!1)}function c0(t,e,n,s){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return s?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function u0(t,e,n,s){if(e===null||typeof e>"u"||c0(t,e,n,s))return!0;if(s)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Ve(t,e,n,s,i,r,o){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=s,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=r,this.removeEmptyString=o}var Oe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){Oe[t]=new Ve(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];Oe[e]=new Ve(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){Oe[t]=new Ve(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){Oe[t]=new Ve(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){Oe[t]=new Ve(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){Oe[t]=new Ve(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){Oe[t]=new Ve(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){Oe[t]=new Ve(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){Oe[t]=new Ve(t,5,!1,t.toLowerCase(),null,!1,!1)});var Ac=/[\-:]([a-z])/g;function Lc(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(Ac,Lc);Oe[e]=new Ve(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(Ac,Lc);Oe[e]=new Ve(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(Ac,Lc);Oe[e]=new Ve(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){Oe[t]=new Ve(t,1,!1,t.toLowerCase(),null,!1,!1)});Oe.xlinkHref=new Ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){Oe[t]=new Ve(t,1,!1,t.toLowerCase(),null,!0,!0)});function Fc(t,e,n,s){var i=Oe.hasOwnProperty(e)?Oe[e]:null;(i!==null?i.type!==0:s||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(u0(e,n,i,s)&&(n=null),s||i===null?l0(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):i.mustUseProperty?t[i.propertyName]=n===null?i.type===3?!1:"":n:(e=i.attributeName,s=i.attributeNamespace,n===null?t.removeAttribute(e):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,s?t.setAttributeNS(s,e,n):t.setAttribute(e,n))))}var Gt=o0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,gr=Symbol.for("react.element"),hs=Symbol.for("react.portal"),fs=Symbol.for("react.fragment"),Bc=Symbol.for("react.strict_mode"),kl=Symbol.for("react.profiler"),op=Symbol.for("react.provider"),ap=Symbol.for("react.context"),Ic=Symbol.for("react.forward_ref"),wl=Symbol.for("react.suspense"),Sl=Symbol.for("react.suspense_list"),zc=Symbol.for("react.memo"),tn=Symbol.for("react.lazy"),lp=Symbol.for("react.offscreen"),td=Symbol.iterator;function Ks(t){return t===null||typeof t!="object"?null:(t=td&&t[td]||t["@@iterator"],typeof t=="function"?t:null)}var ge=Object.assign,Oa;function li(t){if(Oa===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Oa=e&&e[1]||""}return`
`+Oa+t}var Da=!1;function Aa(t,e){if(!t||Da)return"";Da=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(c){var s=c}Reflect.construct(t,[],e)}else{try{e.call()}catch(c){s=c}t.call(e.prototype)}else{try{throw Error()}catch(c){s=c}t()}}catch(c){if(c&&s&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),r=s.stack.split(`
`),o=i.length-1,a=r.length-1;1<=o&&0<=a&&i[o]!==r[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==r[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==r[a]){var l=`
`+i[o].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=o&&0<=a);break}}}finally{Da=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?li(t):""}function d0(t){switch(t.tag){case 5:return li(t.type);case 16:return li("Lazy");case 13:return li("Suspense");case 19:return li("SuspenseList");case 0:case 2:case 15:return t=Aa(t.type,!1),t;case 11:return t=Aa(t.type.render,!1),t;case 1:return t=Aa(t.type,!0),t;default:return""}}function jl(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case fs:return"Fragment";case hs:return"Portal";case kl:return"Profiler";case Bc:return"StrictMode";case wl:return"Suspense";case Sl:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case ap:return(t.displayName||"Context")+".Consumer";case op:return(t._context.displayName||"Context")+".Provider";case Ic:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case zc:return e=t.displayName||null,e!==null?e:jl(t.type)||"Memo";case tn:e=t._payload,t=t._init;try{return jl(t(e))}catch{}}return null}function h0(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return jl(e);case 8:return e===Bc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function kn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function cp(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function f0(t){var e=cp(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),s=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(o){s=""+o,r.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(o){s=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function yr(t){t._valueTracker||(t._valueTracker=f0(t))}function up(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),s="";return t&&(s=cp(t)?t.checked?"true":"false":t.value),t=s,t!==n?(e.setValue(t),!0):!1}function mo(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function Cl(t,e){var n=e.checked;return ge({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function nd(t,e){var n=e.defaultValue==null?"":e.defaultValue,s=e.checked!=null?e.checked:e.defaultChecked;n=kn(e.value!=null?e.value:n),t._wrapperState={initialChecked:s,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function dp(t,e){e=e.checked,e!=null&&Fc(t,"checked",e,!1)}function Nl(t,e){dp(t,e);var n=kn(e.value),s=e.type;if(n!=null)s==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(s==="submit"||s==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?El(t,e.type,n):e.hasOwnProperty("defaultValue")&&El(t,e.type,kn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function sd(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var s=e.type;if(!(s!=="submit"&&s!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function El(t,e,n){(e!=="number"||mo(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var ci=Array.isArray;function Cs(t,e,n,s){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&s&&(t[n].defaultSelected=!0)}else{for(n=""+kn(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,s&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function Pl(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(T(91));return ge({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function id(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(T(92));if(ci(n)){if(1<n.length)throw Error(T(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:kn(n)}}function hp(t,e){var n=kn(e.value),s=kn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),s!=null&&(t.defaultValue=""+s)}function rd(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function fp(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ml(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?fp(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var xr,pp=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,s,i){MSApp.execUnsafeLocalFunction(function(){return t(e,n,s,i)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(xr=xr||document.createElement("div"),xr.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=xr.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function Ti(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var gi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},p0=["Webkit","ms","Moz","O"];Object.keys(gi).forEach(function(t){p0.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),gi[e]=gi[t]})});function mp(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||gi.hasOwnProperty(t)&&gi[t]?(""+e).trim():e+"px"}function gp(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var s=n.indexOf("--")===0,i=mp(n,e[n],s);n==="float"&&(n="cssFloat"),s?t.setProperty(n,i):t[n]=i}}var m0=ge({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Tl(t,e){if(e){if(m0[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(T(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(T(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(T(61))}if(e.style!=null&&typeof e.style!="object")throw Error(T(62))}}function Rl(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ol=null;function Wc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Dl=null,Ns=null,Es=null;function od(t){if(t=ar(t)){if(typeof Dl!="function")throw Error(T(280));var e=t.stateNode;e&&(e=oa(e),Dl(t.stateNode,t.type,e))}}function yp(t){Ns?Es?Es.push(t):Es=[t]:Ns=t}function xp(){if(Ns){var t=Ns,e=Es;if(Es=Ns=null,od(t),e)for(t=0;t<e.length;t++)od(e[t])}}function vp(t,e){return t(e)}function bp(){}var La=!1;function _p(t,e,n){if(La)return t(e,n);La=!0;try{return vp(t,e,n)}finally{La=!1,(Ns!==null||Es!==null)&&(bp(),xp())}}function Ri(t,e){var n=t.stateNode;if(n===null)return null;var s=oa(n);if(s===null)return null;n=s[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(t=t.type,s=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!s;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(T(231,e,typeof n));return n}var Al=!1;if(Xt)try{var qs={};Object.defineProperty(qs,"passive",{get:function(){Al=!0}}),window.addEventListener("test",qs,qs),window.removeEventListener("test",qs,qs)}catch{Al=!1}function g0(t,e,n,s,i,r,o,a,l){var c=Array.prototype.slice.call(arguments,3);try{e.apply(n,c)}catch(d){this.onError(d)}}var yi=!1,go=null,yo=!1,Ll=null,y0={onError:function(t){yi=!0,go=t}};function x0(t,e,n,s,i,r,o,a,l){yi=!1,go=null,g0.apply(y0,arguments)}function v0(t,e,n,s,i,r,o,a,l){if(x0.apply(this,arguments),yi){if(yi){var c=go;yi=!1,go=null}else throw Error(T(198));yo||(yo=!0,Ll=c)}}function rs(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function kp(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function ad(t){if(rs(t)!==t)throw Error(T(188))}function b0(t){var e=t.alternate;if(!e){if(e=rs(t),e===null)throw Error(T(188));return e!==t?null:t}for(var n=t,s=e;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(s=i.return,s!==null){n=s;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return ad(i),t;if(r===s)return ad(i),e;r=r.sibling}throw Error(T(188))}if(n.return!==s.return)n=i,s=r;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,s=r;break}if(a===s){o=!0,s=i,n=r;break}a=a.sibling}if(!o){for(a=r.child;a;){if(a===n){o=!0,n=r,s=i;break}if(a===s){o=!0,s=r,n=i;break}a=a.sibling}if(!o)throw Error(T(189))}}if(n.alternate!==s)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?t:e}function wp(t){return t=b0(t),t!==null?Sp(t):null}function Sp(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=Sp(t);if(e!==null)return e;t=t.sibling}return null}var jp=rt.unstable_scheduleCallback,ld=rt.unstable_cancelCallback,_0=rt.unstable_shouldYield,k0=rt.unstable_requestPaint,be=rt.unstable_now,w0=rt.unstable_getCurrentPriorityLevel,$c=rt.unstable_ImmediatePriority,Cp=rt.unstable_UserBlockingPriority,xo=rt.unstable_NormalPriority,S0=rt.unstable_LowPriority,Np=rt.unstable_IdlePriority,na=null,Dt=null;function j0(t){if(Dt&&typeof Dt.onCommitFiberRoot=="function")try{Dt.onCommitFiberRoot(na,t,void 0,(t.current.flags&128)===128)}catch{}}var bt=Math.clz32?Math.clz32:E0,C0=Math.log,N0=Math.LN2;function E0(t){return t>>>=0,t===0?32:31-(C0(t)/N0|0)|0}var vr=64,br=4194304;function ui(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function vo(t,e){var n=t.pendingLanes;if(n===0)return 0;var s=0,i=t.suspendedLanes,r=t.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?s=ui(a):(r&=o,r!==0&&(s=ui(r)))}else o=n&~i,o!==0?s=ui(o):r!==0&&(s=ui(r));if(s===0)return 0;if(e!==0&&e!==s&&!(e&i)&&(i=s&-s,r=e&-e,i>=r||i===16&&(r&4194240)!==0))return e;if(s&4&&(s|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=s;0<e;)n=31-bt(e),i=1<<n,s|=t[n],e&=~i;return s}function P0(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function M0(t,e){for(var n=t.suspendedLanes,s=t.pingedLanes,i=t.expirationTimes,r=t.pendingLanes;0<r;){var o=31-bt(r),a=1<<o,l=i[o];l===-1?(!(a&n)||a&s)&&(i[o]=P0(a,e)):l<=e&&(t.expiredLanes|=a),r&=~a}}function Fl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Ep(){var t=vr;return vr<<=1,!(vr&4194240)&&(vr=64),t}function Fa(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function rr(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-bt(e),t[e]=n}function T0(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var s=t.eventTimes;for(t=t.expirationTimes;0<n;){var i=31-bt(n),r=1<<i;e[i]=0,s[i]=-1,t[i]=-1,n&=~r}}function Hc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var s=31-bt(n),i=1<<s;i&e|t[s]&e&&(t[s]|=e),n&=~i}}var ie=0;function Pp(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var Mp,Uc,Tp,Rp,Op,Bl=!1,_r=[],fn=null,pn=null,mn=null,Oi=new Map,Di=new Map,sn=[],R0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function cd(t,e){switch(t){case"focusin":case"focusout":fn=null;break;case"dragenter":case"dragleave":pn=null;break;case"mouseover":case"mouseout":mn=null;break;case"pointerover":case"pointerout":Oi.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Di.delete(e.pointerId)}}function Qs(t,e,n,s,i,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:s,nativeEvent:r,targetContainers:[i]},e!==null&&(e=ar(e),e!==null&&Uc(e)),t):(t.eventSystemFlags|=s,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function O0(t,e,n,s,i){switch(e){case"focusin":return fn=Qs(fn,t,e,n,s,i),!0;case"dragenter":return pn=Qs(pn,t,e,n,s,i),!0;case"mouseover":return mn=Qs(mn,t,e,n,s,i),!0;case"pointerover":var r=i.pointerId;return Oi.set(r,Qs(Oi.get(r)||null,t,e,n,s,i)),!0;case"gotpointercapture":return r=i.pointerId,Di.set(r,Qs(Di.get(r)||null,t,e,n,s,i)),!0}return!1}function Dp(t){var e=Bn(t.target);if(e!==null){var n=rs(e);if(n!==null){if(e=n.tag,e===13){if(e=kp(n),e!==null){t.blockedOn=e,Op(t.priority,function(){Tp(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function qr(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Il(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var s=new n.constructor(n.type,n);Ol=s,n.target.dispatchEvent(s),Ol=null}else return e=ar(n),e!==null&&Uc(e),t.blockedOn=n,!1;e.shift()}return!0}function ud(t,e,n){qr(t)&&n.delete(e)}function D0(){Bl=!1,fn!==null&&qr(fn)&&(fn=null),pn!==null&&qr(pn)&&(pn=null),mn!==null&&qr(mn)&&(mn=null),Oi.forEach(ud),Di.forEach(ud)}function Gs(t,e){t.blockedOn===e&&(t.blockedOn=null,Bl||(Bl=!0,rt.unstable_scheduleCallback(rt.unstable_NormalPriority,D0)))}function Ai(t){function e(i){return Gs(i,t)}if(0<_r.length){Gs(_r[0],t);for(var n=1;n<_r.length;n++){var s=_r[n];s.blockedOn===t&&(s.blockedOn=null)}}for(fn!==null&&Gs(fn,t),pn!==null&&Gs(pn,t),mn!==null&&Gs(mn,t),Oi.forEach(e),Di.forEach(e),n=0;n<sn.length;n++)s=sn[n],s.blockedOn===t&&(s.blockedOn=null);for(;0<sn.length&&(n=sn[0],n.blockedOn===null);)Dp(n),n.blockedOn===null&&sn.shift()}var Ps=Gt.ReactCurrentBatchConfig,bo=!0;function A0(t,e,n,s){var i=ie,r=Ps.transition;Ps.transition=null;try{ie=1,Vc(t,e,n,s)}finally{ie=i,Ps.transition=r}}function L0(t,e,n,s){var i=ie,r=Ps.transition;Ps.transition=null;try{ie=4,Vc(t,e,n,s)}finally{ie=i,Ps.transition=r}}function Vc(t,e,n,s){if(bo){var i=Il(t,e,n,s);if(i===null)Xa(t,e,s,_o,n),cd(t,s);else if(O0(i,t,e,n,s))s.stopPropagation();else if(cd(t,s),e&4&&-1<R0.indexOf(t)){for(;i!==null;){var r=ar(i);if(r!==null&&Mp(r),r=Il(t,e,n,s),r===null&&Xa(t,e,s,_o,n),r===i)break;i=r}i!==null&&s.stopPropagation()}else Xa(t,e,s,null,n)}}var _o=null;function Il(t,e,n,s){if(_o=null,t=Wc(s),t=Bn(t),t!==null)if(e=rs(t),e===null)t=null;else if(n=e.tag,n===13){if(t=kp(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return _o=t,null}function Ap(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(w0()){case $c:return 1;case Cp:return 4;case xo:case S0:return 16;case Np:return 536870912;default:return 16}default:return 16}}var on=null,Yc=null,Qr=null;function Lp(){if(Qr)return Qr;var t,e=Yc,n=e.length,s,i="value"in on?on.value:on.textContent,r=i.length;for(t=0;t<n&&e[t]===i[t];t++);var o=n-t;for(s=1;s<=o&&e[n-s]===i[r-s];s++);return Qr=i.slice(t,1<s?1-s:void 0)}function Gr(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function kr(){return!0}function dd(){return!1}function at(t){function e(n,s,i,r,o){this._reactName=n,this._targetInst=i,this.type=s,this.nativeEvent=r,this.target=o,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(r):r[a]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?kr:dd,this.isPropagationStopped=dd,this}return ge(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=kr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=kr)},persist:function(){},isPersistent:kr}),e}var Hs={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xc=at(Hs),or=ge({},Hs,{view:0,detail:0}),F0=at(or),Ba,Ia,Js,sa=ge({},or,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Kc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Js&&(Js&&t.type==="mousemove"?(Ba=t.screenX-Js.screenX,Ia=t.screenY-Js.screenY):Ia=Ba=0,Js=t),Ba)},movementY:function(t){return"movementY"in t?t.movementY:Ia}}),hd=at(sa),B0=ge({},sa,{dataTransfer:0}),I0=at(B0),z0=ge({},or,{relatedTarget:0}),za=at(z0),W0=ge({},Hs,{animationName:0,elapsedTime:0,pseudoElement:0}),$0=at(W0),H0=ge({},Hs,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),U0=at(H0),V0=ge({},Hs,{data:0}),fd=at(V0),Y0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},X0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},K0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function q0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=K0[t])?!!e[t]:!1}function Kc(){return q0}var Q0=ge({},or,{key:function(t){if(t.key){var e=Y0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Gr(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?X0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Kc,charCode:function(t){return t.type==="keypress"?Gr(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Gr(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),G0=at(Q0),J0=ge({},sa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pd=at(J0),Z0=ge({},or,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Kc}),ex=at(Z0),tx=ge({},Hs,{propertyName:0,elapsedTime:0,pseudoElement:0}),nx=at(tx),sx=ge({},sa,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),ix=at(sx),rx=[9,13,27,32],qc=Xt&&"CompositionEvent"in window,xi=null;Xt&&"documentMode"in document&&(xi=document.documentMode);var ox=Xt&&"TextEvent"in window&&!xi,Fp=Xt&&(!qc||xi&&8<xi&&11>=xi),md=String.fromCharCode(32),gd=!1;function Bp(t,e){switch(t){case"keyup":return rx.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ip(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ps=!1;function ax(t,e){switch(t){case"compositionend":return Ip(e);case"keypress":return e.which!==32?null:(gd=!0,md);case"textInput":return t=e.data,t===md&&gd?null:t;default:return null}}function lx(t,e){if(ps)return t==="compositionend"||!qc&&Bp(t,e)?(t=Lp(),Qr=Yc=on=null,ps=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Fp&&e.locale!=="ko"?null:e.data;default:return null}}var cx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yd(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!cx[t.type]:e==="textarea"}function zp(t,e,n,s){yp(s),e=ko(e,"onChange"),0<e.length&&(n=new Xc("onChange","change",null,n,s),t.push({event:n,listeners:e}))}var vi=null,Li=null;function ux(t){Gp(t,0)}function ia(t){var e=ys(t);if(up(e))return t}function dx(t,e){if(t==="change")return e}var Wp=!1;if(Xt){var Wa;if(Xt){var $a="oninput"in document;if(!$a){var xd=document.createElement("div");xd.setAttribute("oninput","return;"),$a=typeof xd.oninput=="function"}Wa=$a}else Wa=!1;Wp=Wa&&(!document.documentMode||9<document.documentMode)}function vd(){vi&&(vi.detachEvent("onpropertychange",$p),Li=vi=null)}function $p(t){if(t.propertyName==="value"&&ia(Li)){var e=[];zp(e,Li,t,Wc(t)),_p(ux,e)}}function hx(t,e,n){t==="focusin"?(vd(),vi=e,Li=n,vi.attachEvent("onpropertychange",$p)):t==="focusout"&&vd()}function fx(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ia(Li)}function px(t,e){if(t==="click")return ia(e)}function mx(t,e){if(t==="input"||t==="change")return ia(e)}function gx(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var wt=typeof Object.is=="function"?Object.is:gx;function Fi(t,e){if(wt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),s=Object.keys(e);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var i=n[s];if(!_l.call(e,i)||!wt(t[i],e[i]))return!1}return!0}function bd(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function _d(t,e){var n=bd(t);t=0;for(var s;n;){if(n.nodeType===3){if(s=t+n.textContent.length,t<=e&&s>=e)return{node:n,offset:e-t};t=s}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=bd(n)}}function Hp(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Hp(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Up(){for(var t=window,e=mo();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=mo(t.document)}return e}function Qc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function yx(t){var e=Up(),n=t.focusedElem,s=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&Hp(n.ownerDocument.documentElement,n)){if(s!==null&&Qc(n)){if(e=s.start,t=s.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var i=n.textContent.length,r=Math.min(s.start,i);s=s.end===void 0?r:Math.min(s.end,i),!t.extend&&r>s&&(i=s,s=r,r=i),i=_d(n,r);var o=_d(n,s);i&&o&&(t.rangeCount!==1||t.anchorNode!==i.node||t.anchorOffset!==i.offset||t.focusNode!==o.node||t.focusOffset!==o.offset)&&(e=e.createRange(),e.setStart(i.node,i.offset),t.removeAllRanges(),r>s?(t.addRange(e),t.extend(o.node,o.offset)):(e.setEnd(o.node,o.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var xx=Xt&&"documentMode"in document&&11>=document.documentMode,ms=null,zl=null,bi=null,Wl=!1;function kd(t,e,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Wl||ms==null||ms!==mo(s)||(s=ms,"selectionStart"in s&&Qc(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),bi&&Fi(bi,s)||(bi=s,s=ko(zl,"onSelect"),0<s.length&&(e=new Xc("onSelect","select",null,e,n),t.push({event:e,listeners:s}),e.target=ms)))}function wr(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var gs={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Ha={},Vp={};Xt&&(Vp=document.createElement("div").style,"AnimationEvent"in window||(delete gs.animationend.animation,delete gs.animationiteration.animation,delete gs.animationstart.animation),"TransitionEvent"in window||delete gs.transitionend.transition);function ra(t){if(Ha[t])return Ha[t];if(!gs[t])return t;var e=gs[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Vp)return Ha[t]=e[n];return t}var Yp=ra("animationend"),Xp=ra("animationiteration"),Kp=ra("animationstart"),qp=ra("transitionend"),Qp=new Map,wd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function jn(t,e){Qp.set(t,e),is(e,[t])}for(var Ua=0;Ua<wd.length;Ua++){var Va=wd[Ua],vx=Va.toLowerCase(),bx=Va[0].toUpperCase()+Va.slice(1);jn(vx,"on"+bx)}jn(Yp,"onAnimationEnd");jn(Xp,"onAnimationIteration");jn(Kp,"onAnimationStart");jn("dblclick","onDoubleClick");jn("focusin","onFocus");jn("focusout","onBlur");jn(qp,"onTransitionEnd");Os("onMouseEnter",["mouseout","mouseover"]);Os("onMouseLeave",["mouseout","mouseover"]);Os("onPointerEnter",["pointerout","pointerover"]);Os("onPointerLeave",["pointerout","pointerover"]);is("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));is("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));is("onBeforeInput",["compositionend","keypress","textInput","paste"]);is("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));is("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));is("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var di="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_x=new Set("cancel close invalid load scroll toggle".split(" ").concat(di));function Sd(t,e,n){var s=t.type||"unknown-event";t.currentTarget=n,v0(s,e,void 0,t),t.currentTarget=null}function Gp(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var s=t[n],i=s.event;s=s.listeners;e:{var r=void 0;if(e)for(var o=s.length-1;0<=o;o--){var a=s[o],l=a.instance,c=a.currentTarget;if(a=a.listener,l!==r&&i.isPropagationStopped())break e;Sd(i,a,c),r=l}else for(o=0;o<s.length;o++){if(a=s[o],l=a.instance,c=a.currentTarget,a=a.listener,l!==r&&i.isPropagationStopped())break e;Sd(i,a,c),r=l}}}if(yo)throw t=Ll,yo=!1,Ll=null,t}function ce(t,e){var n=e[Yl];n===void 0&&(n=e[Yl]=new Set);var s=t+"__bubble";n.has(s)||(Jp(e,t,2,!1),n.add(s))}function Ya(t,e,n){var s=0;e&&(s|=4),Jp(n,t,s,e)}var Sr="_reactListening"+Math.random().toString(36).slice(2);function Bi(t){if(!t[Sr]){t[Sr]=!0,rp.forEach(function(n){n!=="selectionchange"&&(_x.has(n)||Ya(n,!1,t),Ya(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Sr]||(e[Sr]=!0,Ya("selectionchange",!1,e))}}function Jp(t,e,n,s){switch(Ap(e)){case 1:var i=A0;break;case 4:i=L0;break;default:i=Vc}n=i.bind(null,e,n,t),i=void 0,!Al||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),s?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function Xa(t,e,n,s,i){var r=s;if(!(e&1)&&!(e&2)&&s!==null)e:for(;;){if(s===null)return;var o=s.tag;if(o===3||o===4){var a=s.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=s.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=Bn(a),o===null)return;if(l=o.tag,l===5||l===6){s=r=o;continue e}a=a.parentNode}}s=s.return}_p(function(){var c=r,d=Wc(n),h=[];e:{var f=Qp.get(t);if(f!==void 0){var p=Xc,m=t;switch(t){case"keypress":if(Gr(n)===0)break e;case"keydown":case"keyup":p=G0;break;case"focusin":m="focus",p=za;break;case"focusout":m="blur",p=za;break;case"beforeblur":case"afterblur":p=za;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=hd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=I0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=ex;break;case Yp:case Xp:case Kp:p=$0;break;case qp:p=nx;break;case"scroll":p=F0;break;case"wheel":p=ix;break;case"copy":case"cut":case"paste":p=U0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=pd}var y=(e&4)!==0,v=!y&&t==="scroll",g=y?f!==null?f+"Capture":null:f;y=[];for(var x=c,b;x!==null;){b=x;var k=b.stateNode;if(b.tag===5&&k!==null&&(b=k,g!==null&&(k=Ri(x,g),k!=null&&y.push(Ii(x,k,b)))),v)break;x=x.return}0<y.length&&(f=new p(f,m,null,n,d),h.push({event:f,listeners:y}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",p=t==="mouseout"||t==="pointerout",f&&n!==Ol&&(m=n.relatedTarget||n.fromElement)&&(Bn(m)||m[Kt]))break e;if((p||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,p?(m=n.relatedTarget||n.toElement,p=c,m=m?Bn(m):null,m!==null&&(v=rs(m),m!==v||m.tag!==5&&m.tag!==6)&&(m=null)):(p=null,m=c),p!==m)){if(y=hd,k="onMouseLeave",g="onMouseEnter",x="mouse",(t==="pointerout"||t==="pointerover")&&(y=pd,k="onPointerLeave",g="onPointerEnter",x="pointer"),v=p==null?f:ys(p),b=m==null?f:ys(m),f=new y(k,x+"leave",p,n,d),f.target=v,f.relatedTarget=b,k=null,Bn(d)===c&&(y=new y(g,x+"enter",m,n,d),y.target=b,y.relatedTarget=v,k=y),v=k,p&&m)t:{for(y=p,g=m,x=0,b=y;b;b=ls(b))x++;for(b=0,k=g;k;k=ls(k))b++;for(;0<x-b;)y=ls(y),x--;for(;0<b-x;)g=ls(g),b--;for(;x--;){if(y===g||g!==null&&y===g.alternate)break t;y=ls(y),g=ls(g)}y=null}else y=null;p!==null&&jd(h,f,p,y,!1),m!==null&&v!==null&&jd(h,v,m,y,!0)}}e:{if(f=c?ys(c):window,p=f.nodeName&&f.nodeName.toLowerCase(),p==="select"||p==="input"&&f.type==="file")var _=dx;else if(yd(f))if(Wp)_=mx;else{_=fx;var C=hx}else(p=f.nodeName)&&p.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(_=px);if(_&&(_=_(t,c))){zp(h,_,n,d);break e}C&&C(t,f,c),t==="focusout"&&(C=f._wrapperState)&&C.controlled&&f.type==="number"&&El(f,"number",f.value)}switch(C=c?ys(c):window,t){case"focusin":(yd(C)||C.contentEditable==="true")&&(ms=C,zl=c,bi=null);break;case"focusout":bi=zl=ms=null;break;case"mousedown":Wl=!0;break;case"contextmenu":case"mouseup":case"dragend":Wl=!1,kd(h,n,d);break;case"selectionchange":if(xx)break;case"keydown":case"keyup":kd(h,n,d)}var N;if(qc)e:{switch(t){case"compositionstart":var S="onCompositionStart";break e;case"compositionend":S="onCompositionEnd";break e;case"compositionupdate":S="onCompositionUpdate";break e}S=void 0}else ps?Bp(t,n)&&(S="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(S="onCompositionStart");S&&(Fp&&n.locale!=="ko"&&(ps||S!=="onCompositionStart"?S==="onCompositionEnd"&&ps&&(N=Lp()):(on=d,Yc="value"in on?on.value:on.textContent,ps=!0)),C=ko(c,S),0<C.length&&(S=new fd(S,t,null,n,d),h.push({event:S,listeners:C}),N?S.data=N:(N=Ip(n),N!==null&&(S.data=N)))),(N=ox?ax(t,n):lx(t,n))&&(c=ko(c,"onBeforeInput"),0<c.length&&(d=new fd("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:c}),d.data=N))}Gp(h,e)})}function Ii(t,e,n){return{instance:t,listener:e,currentTarget:n}}function ko(t,e){for(var n=e+"Capture",s=[];t!==null;){var i=t,r=i.stateNode;i.tag===5&&r!==null&&(i=r,r=Ri(t,n),r!=null&&s.unshift(Ii(t,r,i)),r=Ri(t,e),r!=null&&s.push(Ii(t,r,i))),t=t.return}return s}function ls(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function jd(t,e,n,s,i){for(var r=e._reactName,o=[];n!==null&&n!==s;){var a=n,l=a.alternate,c=a.stateNode;if(l!==null&&l===s)break;a.tag===5&&c!==null&&(a=c,i?(l=Ri(n,r),l!=null&&o.unshift(Ii(n,l,a))):i||(l=Ri(n,r),l!=null&&o.push(Ii(n,l,a)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var kx=/\r\n?/g,wx=/\u0000|\uFFFD/g;function Cd(t){return(typeof t=="string"?t:""+t).replace(kx,`
`).replace(wx,"")}function jr(t,e,n){if(e=Cd(e),Cd(t)!==e&&n)throw Error(T(425))}function wo(){}var $l=null,Hl=null;function Ul(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Vl=typeof setTimeout=="function"?setTimeout:void 0,Sx=typeof clearTimeout=="function"?clearTimeout:void 0,Nd=typeof Promise=="function"?Promise:void 0,jx=typeof queueMicrotask=="function"?queueMicrotask:typeof Nd<"u"?function(t){return Nd.resolve(null).then(t).catch(Cx)}:Vl;function Cx(t){setTimeout(function(){throw t})}function Ka(t,e){var n=e,s=0;do{var i=n.nextSibling;if(t.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(s===0){t.removeChild(i),Ai(e);return}s--}else n!=="$"&&n!=="$?"&&n!=="$!"||s++;n=i}while(n);Ai(e)}function gn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function Ed(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var Us=Math.random().toString(36).slice(2),Rt="__reactFiber$"+Us,zi="__reactProps$"+Us,Kt="__reactContainer$"+Us,Yl="__reactEvents$"+Us,Nx="__reactListeners$"+Us,Ex="__reactHandles$"+Us;function Bn(t){var e=t[Rt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Kt]||n[Rt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Ed(t);t!==null;){if(n=t[Rt])return n;t=Ed(t)}return e}t=n,n=t.parentNode}return null}function ar(t){return t=t[Rt]||t[Kt],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function ys(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(T(33))}function oa(t){return t[zi]||null}var Xl=[],xs=-1;function Cn(t){return{current:t}}function ue(t){0>xs||(t.current=Xl[xs],Xl[xs]=null,xs--)}function ae(t,e){xs++,Xl[xs]=t.current,t.current=e}var wn={},ze=Cn(wn),Ge=Cn(!1),qn=wn;function Ds(t,e){var n=t.type.contextTypes;if(!n)return wn;var s=t.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===e)return s.__reactInternalMemoizedMaskedChildContext;var i={},r;for(r in n)i[r]=e[r];return s&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=i),i}function Je(t){return t=t.childContextTypes,t!=null}function So(){ue(Ge),ue(ze)}function Pd(t,e,n){if(ze.current!==wn)throw Error(T(168));ae(ze,e),ae(Ge,n)}function Zp(t,e,n){var s=t.stateNode;if(e=e.childContextTypes,typeof s.getChildContext!="function")return n;s=s.getChildContext();for(var i in s)if(!(i in e))throw Error(T(108,h0(t)||"Unknown",i));return ge({},n,s)}function jo(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||wn,qn=ze.current,ae(ze,t),ae(Ge,Ge.current),!0}function Md(t,e,n){var s=t.stateNode;if(!s)throw Error(T(169));n?(t=Zp(t,e,qn),s.__reactInternalMemoizedMergedChildContext=t,ue(Ge),ue(ze),ae(ze,t)):ue(Ge),ae(Ge,n)}var Wt=null,aa=!1,qa=!1;function em(t){Wt===null?Wt=[t]:Wt.push(t)}function Px(t){aa=!0,em(t)}function Nn(){if(!qa&&Wt!==null){qa=!0;var t=0,e=ie;try{var n=Wt;for(ie=1;t<n.length;t++){var s=n[t];do s=s(!0);while(s!==null)}Wt=null,aa=!1}catch(i){throw Wt!==null&&(Wt=Wt.slice(t+1)),jp($c,Nn),i}finally{ie=e,qa=!1}}return null}var vs=[],bs=0,Co=null,No=0,ct=[],ut=0,Qn=null,Ht=1,Ut="";function On(t,e){vs[bs++]=No,vs[bs++]=Co,Co=t,No=e}function tm(t,e,n){ct[ut++]=Ht,ct[ut++]=Ut,ct[ut++]=Qn,Qn=t;var s=Ht;t=Ut;var i=32-bt(s)-1;s&=~(1<<i),n+=1;var r=32-bt(e)+i;if(30<r){var o=i-i%5;r=(s&(1<<o)-1).toString(32),s>>=o,i-=o,Ht=1<<32-bt(e)+i|n<<i|s,Ut=r+t}else Ht=1<<r|n<<i|s,Ut=t}function Gc(t){t.return!==null&&(On(t,1),tm(t,1,0))}function Jc(t){for(;t===Co;)Co=vs[--bs],vs[bs]=null,No=vs[--bs],vs[bs]=null;for(;t===Qn;)Qn=ct[--ut],ct[ut]=null,Ut=ct[--ut],ct[ut]=null,Ht=ct[--ut],ct[ut]=null}var it=null,st=null,de=!1,vt=null;function nm(t,e){var n=dt(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function Td(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,it=t,st=gn(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,it=t,st=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Qn!==null?{id:Ht,overflow:Ut}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=dt(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,it=t,st=null,!0):!1;default:return!1}}function Kl(t){return(t.mode&1)!==0&&(t.flags&128)===0}function ql(t){if(de){var e=st;if(e){var n=e;if(!Td(t,e)){if(Kl(t))throw Error(T(418));e=gn(n.nextSibling);var s=it;e&&Td(t,e)?nm(s,n):(t.flags=t.flags&-4097|2,de=!1,it=t)}}else{if(Kl(t))throw Error(T(418));t.flags=t.flags&-4097|2,de=!1,it=t}}}function Rd(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;it=t}function Cr(t){if(t!==it)return!1;if(!de)return Rd(t),de=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!Ul(t.type,t.memoizedProps)),e&&(e=st)){if(Kl(t))throw sm(),Error(T(418));for(;e;)nm(t,e),e=gn(e.nextSibling)}if(Rd(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(T(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){st=gn(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}st=null}}else st=it?gn(t.stateNode.nextSibling):null;return!0}function sm(){for(var t=st;t;)t=gn(t.nextSibling)}function As(){st=it=null,de=!1}function Zc(t){vt===null?vt=[t]:vt.push(t)}var Mx=Gt.ReactCurrentBatchConfig;function Zs(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var s=n.stateNode}if(!s)throw Error(T(147,t));var i=s,r=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===r?e.ref:(e=function(o){var a=i.refs;o===null?delete a[r]:a[r]=o},e._stringRef=r,e)}if(typeof t!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,t))}return t}function Nr(t,e){throw t=Object.prototype.toString.call(e),Error(T(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function Od(t){var e=t._init;return e(t._payload)}function im(t){function e(g,x){if(t){var b=g.deletions;b===null?(g.deletions=[x],g.flags|=16):b.push(x)}}function n(g,x){if(!t)return null;for(;x!==null;)e(g,x),x=x.sibling;return null}function s(g,x){for(g=new Map;x!==null;)x.key!==null?g.set(x.key,x):g.set(x.index,x),x=x.sibling;return g}function i(g,x){return g=bn(g,x),g.index=0,g.sibling=null,g}function r(g,x,b){return g.index=b,t?(b=g.alternate,b!==null?(b=b.index,b<x?(g.flags|=2,x):b):(g.flags|=2,x)):(g.flags|=1048576,x)}function o(g){return t&&g.alternate===null&&(g.flags|=2),g}function a(g,x,b,k){return x===null||x.tag!==6?(x=nl(b,g.mode,k),x.return=g,x):(x=i(x,b),x.return=g,x)}function l(g,x,b,k){var _=b.type;return _===fs?d(g,x,b.props.children,k,b.key):x!==null&&(x.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===tn&&Od(_)===x.type)?(k=i(x,b.props),k.ref=Zs(g,x,b),k.return=g,k):(k=io(b.type,b.key,b.props,null,g.mode,k),k.ref=Zs(g,x,b),k.return=g,k)}function c(g,x,b,k){return x===null||x.tag!==4||x.stateNode.containerInfo!==b.containerInfo||x.stateNode.implementation!==b.implementation?(x=sl(b,g.mode,k),x.return=g,x):(x=i(x,b.children||[]),x.return=g,x)}function d(g,x,b,k,_){return x===null||x.tag!==7?(x=Un(b,g.mode,k,_),x.return=g,x):(x=i(x,b),x.return=g,x)}function h(g,x,b){if(typeof x=="string"&&x!==""||typeof x=="number")return x=nl(""+x,g.mode,b),x.return=g,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case gr:return b=io(x.type,x.key,x.props,null,g.mode,b),b.ref=Zs(g,null,x),b.return=g,b;case hs:return x=sl(x,g.mode,b),x.return=g,x;case tn:var k=x._init;return h(g,k(x._payload),b)}if(ci(x)||Ks(x))return x=Un(x,g.mode,b,null),x.return=g,x;Nr(g,x)}return null}function f(g,x,b,k){var _=x!==null?x.key:null;if(typeof b=="string"&&b!==""||typeof b=="number")return _!==null?null:a(g,x,""+b,k);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case gr:return b.key===_?l(g,x,b,k):null;case hs:return b.key===_?c(g,x,b,k):null;case tn:return _=b._init,f(g,x,_(b._payload),k)}if(ci(b)||Ks(b))return _!==null?null:d(g,x,b,k,null);Nr(g,b)}return null}function p(g,x,b,k,_){if(typeof k=="string"&&k!==""||typeof k=="number")return g=g.get(b)||null,a(x,g,""+k,_);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case gr:return g=g.get(k.key===null?b:k.key)||null,l(x,g,k,_);case hs:return g=g.get(k.key===null?b:k.key)||null,c(x,g,k,_);case tn:var C=k._init;return p(g,x,b,C(k._payload),_)}if(ci(k)||Ks(k))return g=g.get(b)||null,d(x,g,k,_,null);Nr(x,k)}return null}function m(g,x,b,k){for(var _=null,C=null,N=x,S=x=0,L=null;N!==null&&S<b.length;S++){N.index>S?(L=N,N=null):L=N.sibling;var R=f(g,N,b[S],k);if(R===null){N===null&&(N=L);break}t&&N&&R.alternate===null&&e(g,N),x=r(R,x,S),C===null?_=R:C.sibling=R,C=R,N=L}if(S===b.length)return n(g,N),de&&On(g,S),_;if(N===null){for(;S<b.length;S++)N=h(g,b[S],k),N!==null&&(x=r(N,x,S),C===null?_=N:C.sibling=N,C=N);return de&&On(g,S),_}for(N=s(g,N);S<b.length;S++)L=p(N,g,S,b[S],k),L!==null&&(t&&L.alternate!==null&&N.delete(L.key===null?S:L.key),x=r(L,x,S),C===null?_=L:C.sibling=L,C=L);return t&&N.forEach(function(I){return e(g,I)}),de&&On(g,S),_}function y(g,x,b,k){var _=Ks(b);if(typeof _!="function")throw Error(T(150));if(b=_.call(b),b==null)throw Error(T(151));for(var C=_=null,N=x,S=x=0,L=null,R=b.next();N!==null&&!R.done;S++,R=b.next()){N.index>S?(L=N,N=null):L=N.sibling;var I=f(g,N,R.value,k);if(I===null){N===null&&(N=L);break}t&&N&&I.alternate===null&&e(g,N),x=r(I,x,S),C===null?_=I:C.sibling=I,C=I,N=L}if(R.done)return n(g,N),de&&On(g,S),_;if(N===null){for(;!R.done;S++,R=b.next())R=h(g,R.value,k),R!==null&&(x=r(R,x,S),C===null?_=R:C.sibling=R,C=R);return de&&On(g,S),_}for(N=s(g,N);!R.done;S++,R=b.next())R=p(N,g,S,R.value,k),R!==null&&(t&&R.alternate!==null&&N.delete(R.key===null?S:R.key),x=r(R,x,S),C===null?_=R:C.sibling=R,C=R);return t&&N.forEach(function(M){return e(g,M)}),de&&On(g,S),_}function v(g,x,b,k){if(typeof b=="object"&&b!==null&&b.type===fs&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case gr:e:{for(var _=b.key,C=x;C!==null;){if(C.key===_){if(_=b.type,_===fs){if(C.tag===7){n(g,C.sibling),x=i(C,b.props.children),x.return=g,g=x;break e}}else if(C.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===tn&&Od(_)===C.type){n(g,C.sibling),x=i(C,b.props),x.ref=Zs(g,C,b),x.return=g,g=x;break e}n(g,C);break}else e(g,C);C=C.sibling}b.type===fs?(x=Un(b.props.children,g.mode,k,b.key),x.return=g,g=x):(k=io(b.type,b.key,b.props,null,g.mode,k),k.ref=Zs(g,x,b),k.return=g,g=k)}return o(g);case hs:e:{for(C=b.key;x!==null;){if(x.key===C)if(x.tag===4&&x.stateNode.containerInfo===b.containerInfo&&x.stateNode.implementation===b.implementation){n(g,x.sibling),x=i(x,b.children||[]),x.return=g,g=x;break e}else{n(g,x);break}else e(g,x);x=x.sibling}x=sl(b,g.mode,k),x.return=g,g=x}return o(g);case tn:return C=b._init,v(g,x,C(b._payload),k)}if(ci(b))return m(g,x,b,k);if(Ks(b))return y(g,x,b,k);Nr(g,b)}return typeof b=="string"&&b!==""||typeof b=="number"?(b=""+b,x!==null&&x.tag===6?(n(g,x.sibling),x=i(x,b),x.return=g,g=x):(n(g,x),x=nl(b,g.mode,k),x.return=g,g=x),o(g)):n(g,x)}return v}var Ls=im(!0),rm=im(!1),Eo=Cn(null),Po=null,_s=null,eu=null;function tu(){eu=_s=Po=null}function nu(t){var e=Eo.current;ue(Eo),t._currentValue=e}function Ql(t,e,n){for(;t!==null;){var s=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,s!==null&&(s.childLanes|=e)):s!==null&&(s.childLanes&e)!==e&&(s.childLanes|=e),t===n)break;t=t.return}}function Ms(t,e){Po=t,eu=_s=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Qe=!0),t.firstContext=null)}function pt(t){var e=t._currentValue;if(eu!==t)if(t={context:t,memoizedValue:e,next:null},_s===null){if(Po===null)throw Error(T(308));_s=t,Po.dependencies={lanes:0,firstContext:t}}else _s=_s.next=t;return e}var In=null;function su(t){In===null?In=[t]:In.push(t)}function om(t,e,n,s){var i=e.interleaved;return i===null?(n.next=n,su(e)):(n.next=i.next,i.next=n),e.interleaved=n,qt(t,s)}function qt(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var nn=!1;function iu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function am(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Yt(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function yn(t,e,n){var s=t.updateQueue;if(s===null)return null;if(s=s.shared,G&2){var i=s.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),s.pending=e,qt(t,n)}return i=s.interleaved,i===null?(e.next=e,su(s)):(e.next=i.next,i.next=e),s.interleaved=e,qt(t,n)}function Jr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var s=e.lanes;s&=t.pendingLanes,n|=s,e.lanes=n,Hc(t,n)}}function Dd(t,e){var n=t.updateQueue,s=t.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};r===null?i=r=o:r=r.next=o,n=n.next}while(n!==null);r===null?i=r=e:r=r.next=e}else i=r=e;n={baseState:s.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:s.shared,effects:s.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function Mo(t,e,n,s){var i=t.updateQueue;nn=!1;var r=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,c=l.next;l.next=null,o===null?r=c:o.next=c,o=l;var d=t.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=c:a.next=c,d.lastBaseUpdate=l))}if(r!==null){var h=i.baseState;o=0,d=c=l=null,a=r;do{var f=a.lane,p=a.eventTime;if((s&f)===f){d!==null&&(d=d.next={eventTime:p,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var m=t,y=a;switch(f=e,p=n,y.tag){case 1:if(m=y.payload,typeof m=="function"){h=m.call(p,h,f);break e}h=m;break e;case 3:m.flags=m.flags&-65537|128;case 0:if(m=y.payload,f=typeof m=="function"?m.call(p,h,f):m,f==null)break e;h=ge({},h,f);break e;case 2:nn=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else p={eventTime:p,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(c=d=p,l=h):d=d.next=p,o|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(1);if(d===null&&(l=h),i.baseState=l,i.firstBaseUpdate=c,i.lastBaseUpdate=d,e=i.shared.interleaved,e!==null){i=e;do o|=i.lane,i=i.next;while(i!==e)}else r===null&&(i.shared.lanes=0);Jn|=o,t.lanes=o,t.memoizedState=h}}function Ad(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var s=t[e],i=s.callback;if(i!==null){if(s.callback=null,s=n,typeof i!="function")throw Error(T(191,i));i.call(s)}}}var lr={},At=Cn(lr),Wi=Cn(lr),$i=Cn(lr);function zn(t){if(t===lr)throw Error(T(174));return t}function ru(t,e){switch(ae($i,e),ae(Wi,t),ae(At,lr),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:Ml(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=Ml(e,t)}ue(At),ae(At,e)}function Fs(){ue(At),ue(Wi),ue($i)}function lm(t){zn($i.current);var e=zn(At.current),n=Ml(e,t.type);e!==n&&(ae(Wi,t),ae(At,n))}function ou(t){Wi.current===t&&(ue(At),ue(Wi))}var pe=Cn(0);function To(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var Qa=[];function au(){for(var t=0;t<Qa.length;t++)Qa[t]._workInProgressVersionPrimary=null;Qa.length=0}var Zr=Gt.ReactCurrentDispatcher,Ga=Gt.ReactCurrentBatchConfig,Gn=0,me=null,je=null,Ee=null,Ro=!1,_i=!1,Hi=0,Tx=0;function De(){throw Error(T(321))}function lu(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!wt(t[n],e[n]))return!1;return!0}function cu(t,e,n,s,i,r){if(Gn=r,me=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Zr.current=t===null||t.memoizedState===null?Ax:Lx,t=n(s,i),_i){r=0;do{if(_i=!1,Hi=0,25<=r)throw Error(T(301));r+=1,Ee=je=null,e.updateQueue=null,Zr.current=Fx,t=n(s,i)}while(_i)}if(Zr.current=Oo,e=je!==null&&je.next!==null,Gn=0,Ee=je=me=null,Ro=!1,e)throw Error(T(300));return t}function uu(){var t=Hi!==0;return Hi=0,t}function Mt(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?me.memoizedState=Ee=t:Ee=Ee.next=t,Ee}function mt(){if(je===null){var t=me.alternate;t=t!==null?t.memoizedState:null}else t=je.next;var e=Ee===null?me.memoizedState:Ee.next;if(e!==null)Ee=e,je=t;else{if(t===null)throw Error(T(310));je=t,t={memoizedState:je.memoizedState,baseState:je.baseState,baseQueue:je.baseQueue,queue:je.queue,next:null},Ee===null?me.memoizedState=Ee=t:Ee=Ee.next=t}return Ee}function Ui(t,e){return typeof e=="function"?e(t):e}function Ja(t){var e=mt(),n=e.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=t;var s=je,i=s.baseQueue,r=n.pending;if(r!==null){if(i!==null){var o=i.next;i.next=r.next,r.next=o}s.baseQueue=i=r,n.pending=null}if(i!==null){r=i.next,s=s.baseState;var a=o=null,l=null,c=r;do{var d=c.lane;if((Gn&d)===d)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),s=c.hasEagerState?c.eagerState:t(s,c.action);else{var h={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(a=l=h,o=s):l=l.next=h,me.lanes|=d,Jn|=d}c=c.next}while(c!==null&&c!==r);l===null?o=s:l.next=a,wt(s,e.memoizedState)||(Qe=!0),e.memoizedState=s,e.baseState=o,e.baseQueue=l,n.lastRenderedState=s}if(t=n.interleaved,t!==null){i=t;do r=i.lane,me.lanes|=r,Jn|=r,i=i.next;while(i!==t)}else i===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function Za(t){var e=mt(),n=e.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=t;var s=n.dispatch,i=n.pending,r=e.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do r=t(r,o.action),o=o.next;while(o!==i);wt(r,e.memoizedState)||(Qe=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,s]}function cm(){}function um(t,e){var n=me,s=mt(),i=e(),r=!wt(s.memoizedState,i);if(r&&(s.memoizedState=i,Qe=!0),s=s.queue,du(fm.bind(null,n,s,t),[t]),s.getSnapshot!==e||r||Ee!==null&&Ee.memoizedState.tag&1){if(n.flags|=2048,Vi(9,hm.bind(null,n,s,i,e),void 0,null),Me===null)throw Error(T(349));Gn&30||dm(n,e,i)}return i}function dm(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=me.updateQueue,e===null?(e={lastEffect:null,stores:null},me.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function hm(t,e,n,s){e.value=n,e.getSnapshot=s,pm(e)&&mm(t)}function fm(t,e,n){return n(function(){pm(e)&&mm(t)})}function pm(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!wt(t,n)}catch{return!0}}function mm(t){var e=qt(t,1);e!==null&&_t(e,t,1,-1)}function Ld(t){var e=Mt();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ui,lastRenderedState:t},e.queue=t,t=t.dispatch=Dx.bind(null,me,t),[e.memoizedState,t]}function Vi(t,e,n,s){return t={tag:t,create:e,destroy:n,deps:s,next:null},e=me.updateQueue,e===null?(e={lastEffect:null,stores:null},me.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(s=n.next,n.next=t,t.next=s,e.lastEffect=t)),t}function gm(){return mt().memoizedState}function eo(t,e,n,s){var i=Mt();me.flags|=t,i.memoizedState=Vi(1|e,n,void 0,s===void 0?null:s)}function la(t,e,n,s){var i=mt();s=s===void 0?null:s;var r=void 0;if(je!==null){var o=je.memoizedState;if(r=o.destroy,s!==null&&lu(s,o.deps)){i.memoizedState=Vi(e,n,r,s);return}}me.flags|=t,i.memoizedState=Vi(1|e,n,r,s)}function Fd(t,e){return eo(8390656,8,t,e)}function du(t,e){return la(2048,8,t,e)}function ym(t,e){return la(4,2,t,e)}function xm(t,e){return la(4,4,t,e)}function vm(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function bm(t,e,n){return n=n!=null?n.concat([t]):null,la(4,4,vm.bind(null,e,t),n)}function hu(){}function _m(t,e){var n=mt();e=e===void 0?null:e;var s=n.memoizedState;return s!==null&&e!==null&&lu(e,s[1])?s[0]:(n.memoizedState=[t,e],t)}function km(t,e){var n=mt();e=e===void 0?null:e;var s=n.memoizedState;return s!==null&&e!==null&&lu(e,s[1])?s[0]:(t=t(),n.memoizedState=[t,e],t)}function wm(t,e,n){return Gn&21?(wt(n,e)||(n=Ep(),me.lanes|=n,Jn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Qe=!0),t.memoizedState=n)}function Rx(t,e){var n=ie;ie=n!==0&&4>n?n:4,t(!0);var s=Ga.transition;Ga.transition={};try{t(!1),e()}finally{ie=n,Ga.transition=s}}function Sm(){return mt().memoizedState}function Ox(t,e,n){var s=vn(t);if(n={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null},jm(t))Cm(e,n);else if(n=om(t,e,n,s),n!==null){var i=He();_t(n,t,s,i),Nm(n,e,s)}}function Dx(t,e,n){var s=vn(t),i={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null};if(jm(t))Cm(e,i);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var o=e.lastRenderedState,a=r(o,n);if(i.hasEagerState=!0,i.eagerState=a,wt(a,o)){var l=e.interleaved;l===null?(i.next=i,su(e)):(i.next=l.next,l.next=i),e.interleaved=i;return}}catch{}finally{}n=om(t,e,i,s),n!==null&&(i=He(),_t(n,t,s,i),Nm(n,e,s))}}function jm(t){var e=t.alternate;return t===me||e!==null&&e===me}function Cm(t,e){_i=Ro=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Nm(t,e,n){if(n&4194240){var s=e.lanes;s&=t.pendingLanes,n|=s,e.lanes=n,Hc(t,n)}}var Oo={readContext:pt,useCallback:De,useContext:De,useEffect:De,useImperativeHandle:De,useInsertionEffect:De,useLayoutEffect:De,useMemo:De,useReducer:De,useRef:De,useState:De,useDebugValue:De,useDeferredValue:De,useTransition:De,useMutableSource:De,useSyncExternalStore:De,useId:De,unstable_isNewReconciler:!1},Ax={readContext:pt,useCallback:function(t,e){return Mt().memoizedState=[t,e===void 0?null:e],t},useContext:pt,useEffect:Fd,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,eo(4194308,4,vm.bind(null,e,t),n)},useLayoutEffect:function(t,e){return eo(4194308,4,t,e)},useInsertionEffect:function(t,e){return eo(4,2,t,e)},useMemo:function(t,e){var n=Mt();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var s=Mt();return e=n!==void 0?n(e):e,s.memoizedState=s.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},s.queue=t,t=t.dispatch=Ox.bind(null,me,t),[s.memoizedState,t]},useRef:function(t){var e=Mt();return t={current:t},e.memoizedState=t},useState:Ld,useDebugValue:hu,useDeferredValue:function(t){return Mt().memoizedState=t},useTransition:function(){var t=Ld(!1),e=t[0];return t=Rx.bind(null,t[1]),Mt().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var s=me,i=Mt();if(de){if(n===void 0)throw Error(T(407));n=n()}else{if(n=e(),Me===null)throw Error(T(349));Gn&30||dm(s,e,n)}i.memoizedState=n;var r={value:n,getSnapshot:e};return i.queue=r,Fd(fm.bind(null,s,r,t),[t]),s.flags|=2048,Vi(9,hm.bind(null,s,r,n,e),void 0,null),n},useId:function(){var t=Mt(),e=Me.identifierPrefix;if(de){var n=Ut,s=Ht;n=(s&~(1<<32-bt(s)-1)).toString(32)+n,e=":"+e+"R"+n,n=Hi++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Tx++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Lx={readContext:pt,useCallback:_m,useContext:pt,useEffect:du,useImperativeHandle:bm,useInsertionEffect:ym,useLayoutEffect:xm,useMemo:km,useReducer:Ja,useRef:gm,useState:function(){return Ja(Ui)},useDebugValue:hu,useDeferredValue:function(t){var e=mt();return wm(e,je.memoizedState,t)},useTransition:function(){var t=Ja(Ui)[0],e=mt().memoizedState;return[t,e]},useMutableSource:cm,useSyncExternalStore:um,useId:Sm,unstable_isNewReconciler:!1},Fx={readContext:pt,useCallback:_m,useContext:pt,useEffect:du,useImperativeHandle:bm,useInsertionEffect:ym,useLayoutEffect:xm,useMemo:km,useReducer:Za,useRef:gm,useState:function(){return Za(Ui)},useDebugValue:hu,useDeferredValue:function(t){var e=mt();return je===null?e.memoizedState=t:wm(e,je.memoizedState,t)},useTransition:function(){var t=Za(Ui)[0],e=mt().memoizedState;return[t,e]},useMutableSource:cm,useSyncExternalStore:um,useId:Sm,unstable_isNewReconciler:!1};function yt(t,e){if(t&&t.defaultProps){e=ge({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function Gl(t,e,n,s){e=t.memoizedState,n=n(s,e),n=n==null?e:ge({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var ca={isMounted:function(t){return(t=t._reactInternals)?rs(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var s=He(),i=vn(t),r=Yt(s,i);r.payload=e,n!=null&&(r.callback=n),e=yn(t,r,i),e!==null&&(_t(e,t,i,s),Jr(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var s=He(),i=vn(t),r=Yt(s,i);r.tag=1,r.payload=e,n!=null&&(r.callback=n),e=yn(t,r,i),e!==null&&(_t(e,t,i,s),Jr(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=He(),s=vn(t),i=Yt(n,s);i.tag=2,e!=null&&(i.callback=e),e=yn(t,i,s),e!==null&&(_t(e,t,s,n),Jr(e,t,s))}};function Bd(t,e,n,s,i,r,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(s,r,o):e.prototype&&e.prototype.isPureReactComponent?!Fi(n,s)||!Fi(i,r):!0}function Em(t,e,n){var s=!1,i=wn,r=e.contextType;return typeof r=="object"&&r!==null?r=pt(r):(i=Je(e)?qn:ze.current,s=e.contextTypes,r=(s=s!=null)?Ds(t,i):wn),e=new e(n,r),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=ca,t.stateNode=e,e._reactInternals=t,s&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=i,t.__reactInternalMemoizedMaskedChildContext=r),e}function Id(t,e,n,s){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,s),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,s),e.state!==t&&ca.enqueueReplaceState(e,e.state,null)}function Jl(t,e,n,s){var i=t.stateNode;i.props=n,i.state=t.memoizedState,i.refs={},iu(t);var r=e.contextType;typeof r=="object"&&r!==null?i.context=pt(r):(r=Je(e)?qn:ze.current,i.context=Ds(t,r)),i.state=t.memoizedState,r=e.getDerivedStateFromProps,typeof r=="function"&&(Gl(t,e,r,n),i.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(e=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),e!==i.state&&ca.enqueueReplaceState(i,i.state,null),Mo(t,n,i,s),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308)}function Bs(t,e){try{var n="",s=e;do n+=d0(s),s=s.return;while(s);var i=n}catch(r){i=`
Error generating stack: `+r.message+`
`+r.stack}return{value:t,source:e,stack:i,digest:null}}function el(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function Zl(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Bx=typeof WeakMap=="function"?WeakMap:Map;function Pm(t,e,n){n=Yt(-1,n),n.tag=3,n.payload={element:null};var s=e.value;return n.callback=function(){Ao||(Ao=!0,cc=s),Zl(t,e)},n}function Mm(t,e,n){n=Yt(-1,n),n.tag=3;var s=t.type.getDerivedStateFromError;if(typeof s=="function"){var i=e.value;n.payload=function(){return s(i)},n.callback=function(){Zl(t,e)}}var r=t.stateNode;return r!==null&&typeof r.componentDidCatch=="function"&&(n.callback=function(){Zl(t,e),typeof s!="function"&&(xn===null?xn=new Set([this]):xn.add(this));var o=e.stack;this.componentDidCatch(e.value,{componentStack:o!==null?o:""})}),n}function zd(t,e,n){var s=t.pingCache;if(s===null){s=t.pingCache=new Bx;var i=new Set;s.set(e,i)}else i=s.get(e),i===void 0&&(i=new Set,s.set(e,i));i.has(n)||(i.add(n),t=Jx.bind(null,t,e,n),e.then(t,t))}function Wd(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function $d(t,e,n,s,i){return t.mode&1?(t.flags|=65536,t.lanes=i,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=Yt(-1,1),e.tag=2,yn(n,e,1))),n.lanes|=1),t)}var Ix=Gt.ReactCurrentOwner,Qe=!1;function $e(t,e,n,s){e.child=t===null?rm(e,null,n,s):Ls(e,t.child,n,s)}function Hd(t,e,n,s,i){n=n.render;var r=e.ref;return Ms(e,i),s=cu(t,e,n,s,r,i),n=uu(),t!==null&&!Qe?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Qt(t,e,i)):(de&&n&&Gc(e),e.flags|=1,$e(t,e,s,i),e.child)}function Ud(t,e,n,s,i){if(t===null){var r=n.type;return typeof r=="function"&&!bu(r)&&r.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=r,Tm(t,e,r,s,i)):(t=io(n.type,null,s,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!(t.lanes&i)){var o=r.memoizedProps;if(n=n.compare,n=n!==null?n:Fi,n(o,s)&&t.ref===e.ref)return Qt(t,e,i)}return e.flags|=1,t=bn(r,s),t.ref=e.ref,t.return=e,e.child=t}function Tm(t,e,n,s,i){if(t!==null){var r=t.memoizedProps;if(Fi(r,s)&&t.ref===e.ref)if(Qe=!1,e.pendingProps=s=r,(t.lanes&i)!==0)t.flags&131072&&(Qe=!0);else return e.lanes=t.lanes,Qt(t,e,i)}return ec(t,e,n,s,i)}function Rm(t,e,n){var s=e.pendingProps,i=s.children,r=t!==null?t.memoizedState:null;if(s.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},ae(ws,nt),nt|=n;else{if(!(n&1073741824))return t=r!==null?r.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,ae(ws,nt),nt|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=r!==null?r.baseLanes:n,ae(ws,nt),nt|=s}else r!==null?(s=r.baseLanes|n,e.memoizedState=null):s=n,ae(ws,nt),nt|=s;return $e(t,e,i,n),e.child}function Om(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function ec(t,e,n,s,i){var r=Je(n)?qn:ze.current;return r=Ds(e,r),Ms(e,i),n=cu(t,e,n,s,r,i),s=uu(),t!==null&&!Qe?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Qt(t,e,i)):(de&&s&&Gc(e),e.flags|=1,$e(t,e,n,i),e.child)}function Vd(t,e,n,s,i){if(Je(n)){var r=!0;jo(e)}else r=!1;if(Ms(e,i),e.stateNode===null)to(t,e),Em(e,n,s),Jl(e,n,s,i),s=!0;else if(t===null){var o=e.stateNode,a=e.memoizedProps;o.props=a;var l=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=pt(c):(c=Je(n)?qn:ze.current,c=Ds(e,c));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==s||l!==c)&&Id(e,o,s,c),nn=!1;var f=e.memoizedState;o.state=f,Mo(e,s,o,i),l=e.memoizedState,a!==s||f!==l||Ge.current||nn?(typeof d=="function"&&(Gl(e,n,d,s),l=e.memoizedState),(a=nn||Bd(e,n,a,s,f,l,c))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=s,e.memoizedState=l),o.props=s,o.state=l,o.context=c,s=a):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),s=!1)}else{o=e.stateNode,am(t,e),a=e.memoizedProps,c=e.type===e.elementType?a:yt(e.type,a),o.props=c,h=e.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=pt(l):(l=Je(n)?qn:ze.current,l=Ds(e,l));var p=n.getDerivedStateFromProps;(d=typeof p=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||f!==l)&&Id(e,o,s,l),nn=!1,f=e.memoizedState,o.state=f,Mo(e,s,o,i);var m=e.memoizedState;a!==h||f!==m||Ge.current||nn?(typeof p=="function"&&(Gl(e,n,p,s),m=e.memoizedState),(c=nn||Bd(e,n,c,s,f,m,l)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(s,m,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(s,m,l)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=s,e.memoizedState=m),o.props=s,o.state=m,o.context=l,s=c):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),s=!1)}return tc(t,e,n,s,r,i)}function tc(t,e,n,s,i,r){Om(t,e);var o=(e.flags&128)!==0;if(!s&&!o)return i&&Md(e,n,!1),Qt(t,e,r);s=e.stateNode,Ix.current=e;var a=o&&typeof n.getDerivedStateFromError!="function"?null:s.render();return e.flags|=1,t!==null&&o?(e.child=Ls(e,t.child,null,r),e.child=Ls(e,null,a,r)):$e(t,e,a,r),e.memoizedState=s.state,i&&Md(e,n,!0),e.child}function Dm(t){var e=t.stateNode;e.pendingContext?Pd(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Pd(t,e.context,!1),ru(t,e.containerInfo)}function Yd(t,e,n,s,i){return As(),Zc(i),e.flags|=256,$e(t,e,n,s),e.child}var nc={dehydrated:null,treeContext:null,retryLane:0};function sc(t){return{baseLanes:t,cachePool:null,transitions:null}}function Am(t,e,n){var s=e.pendingProps,i=pe.current,r=!1,o=(e.flags&128)!==0,a;if((a=o)||(a=t!==null&&t.memoizedState===null?!1:(i&2)!==0),a?(r=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(i|=1),ae(pe,i&1),t===null)return ql(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(o=s.children,t=s.fallback,r?(s=e.mode,r=e.child,o={mode:"hidden",children:o},!(s&1)&&r!==null?(r.childLanes=0,r.pendingProps=o):r=ha(o,s,0,null),t=Un(t,s,n,null),r.return=e,t.return=e,r.sibling=t,e.child=r,e.child.memoizedState=sc(n),e.memoizedState=nc,t):fu(e,o));if(i=t.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return zx(t,e,o,s,a,i,n);if(r){r=s.fallback,o=e.mode,i=t.child,a=i.sibling;var l={mode:"hidden",children:s.children};return!(o&1)&&e.child!==i?(s=e.child,s.childLanes=0,s.pendingProps=l,e.deletions=null):(s=bn(i,l),s.subtreeFlags=i.subtreeFlags&14680064),a!==null?r=bn(a,r):(r=Un(r,o,n,null),r.flags|=2),r.return=e,s.return=e,s.sibling=r,e.child=s,s=r,r=e.child,o=t.child.memoizedState,o=o===null?sc(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},r.memoizedState=o,r.childLanes=t.childLanes&~n,e.memoizedState=nc,s}return r=t.child,t=r.sibling,s=bn(r,{mode:"visible",children:s.children}),!(e.mode&1)&&(s.lanes=n),s.return=e,s.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=s,e.memoizedState=null,s}function fu(t,e){return e=ha({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function Er(t,e,n,s){return s!==null&&Zc(s),Ls(e,t.child,null,n),t=fu(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function zx(t,e,n,s,i,r,o){if(n)return e.flags&256?(e.flags&=-257,s=el(Error(T(422))),Er(t,e,o,s)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(r=s.fallback,i=e.mode,s=ha({mode:"visible",children:s.children},i,0,null),r=Un(r,i,o,null),r.flags|=2,s.return=e,r.return=e,s.sibling=r,e.child=s,e.mode&1&&Ls(e,t.child,null,o),e.child.memoizedState=sc(o),e.memoizedState=nc,r);if(!(e.mode&1))return Er(t,e,o,null);if(i.data==="$!"){if(s=i.nextSibling&&i.nextSibling.dataset,s)var a=s.dgst;return s=a,r=Error(T(419)),s=el(r,s,void 0),Er(t,e,o,s)}if(a=(o&t.childLanes)!==0,Qe||a){if(s=Me,s!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(s.suspendedLanes|o)?0:i,i!==0&&i!==r.retryLane&&(r.retryLane=i,qt(t,i),_t(s,t,i,-1))}return vu(),s=el(Error(T(421))),Er(t,e,o,s)}return i.data==="$?"?(e.flags|=128,e.child=t.child,e=Zx.bind(null,t),i._reactRetry=e,null):(t=r.treeContext,st=gn(i.nextSibling),it=e,de=!0,vt=null,t!==null&&(ct[ut++]=Ht,ct[ut++]=Ut,ct[ut++]=Qn,Ht=t.id,Ut=t.overflow,Qn=e),e=fu(e,s.children),e.flags|=4096,e)}function Xd(t,e,n){t.lanes|=e;var s=t.alternate;s!==null&&(s.lanes|=e),Ql(t.return,e,n)}function tl(t,e,n,s,i){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:i}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=s,r.tail=n,r.tailMode=i)}function Lm(t,e,n){var s=e.pendingProps,i=s.revealOrder,r=s.tail;if($e(t,e,s.children,n),s=pe.current,s&2)s=s&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Xd(t,n,e);else if(t.tag===19)Xd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}s&=1}if(ae(pe,s),!(e.mode&1))e.memoizedState=null;else switch(i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&To(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),tl(e,!1,i,n,r);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&To(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}tl(e,!0,n,null,r);break;case"together":tl(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function to(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Qt(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Jn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(T(153));if(e.child!==null){for(t=e.child,n=bn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=bn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Wx(t,e,n){switch(e.tag){case 3:Dm(e),As();break;case 5:lm(e);break;case 1:Je(e.type)&&jo(e);break;case 4:ru(e,e.stateNode.containerInfo);break;case 10:var s=e.type._context,i=e.memoizedProps.value;ae(Eo,s._currentValue),s._currentValue=i;break;case 13:if(s=e.memoizedState,s!==null)return s.dehydrated!==null?(ae(pe,pe.current&1),e.flags|=128,null):n&e.child.childLanes?Am(t,e,n):(ae(pe,pe.current&1),t=Qt(t,e,n),t!==null?t.sibling:null);ae(pe,pe.current&1);break;case 19:if(s=(n&e.childLanes)!==0,t.flags&128){if(s)return Lm(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),ae(pe,pe.current),s)break;return null;case 22:case 23:return e.lanes=0,Rm(t,e,n)}return Qt(t,e,n)}var Fm,ic,Bm,Im;Fm=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ic=function(){};Bm=function(t,e,n,s){var i=t.memoizedProps;if(i!==s){t=e.stateNode,zn(At.current);var r=null;switch(n){case"input":i=Cl(t,i),s=Cl(t,s),r=[];break;case"select":i=ge({},i,{value:void 0}),s=ge({},s,{value:void 0}),r=[];break;case"textarea":i=Pl(t,i),s=Pl(t,s),r=[];break;default:typeof i.onClick!="function"&&typeof s.onClick=="function"&&(t.onclick=wo)}Tl(n,s);var o;n=null;for(c in i)if(!s.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var a=i[c];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Mi.hasOwnProperty(c)?r||(r=[]):(r=r||[]).push(c,null));for(c in s){var l=s[c];if(a=i!=null?i[c]:void 0,s.hasOwnProperty(c)&&l!==a&&(l!=null||a!=null))if(c==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(r||(r=[]),r.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(r=r||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(r=r||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Mi.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&ce("scroll",t),r||a===l||(r=[])):(r=r||[]).push(c,l))}n&&(r=r||[]).push("style",n);var c=r;(e.updateQueue=c)&&(e.flags|=4)}};Im=function(t,e,n,s){n!==s&&(e.flags|=4)};function ei(t,e){if(!de)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:s.sibling=null}}function Ae(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,s=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,s|=i.subtreeFlags&14680064,s|=i.flags&14680064,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,s|=i.subtreeFlags,s|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=s,t.childLanes=n,e}function $x(t,e,n){var s=e.pendingProps;switch(Jc(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ae(e),null;case 1:return Je(e.type)&&So(),Ae(e),null;case 3:return s=e.stateNode,Fs(),ue(Ge),ue(ze),au(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(t===null||t.child===null)&&(Cr(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,vt!==null&&(hc(vt),vt=null))),ic(t,e),Ae(e),null;case 5:ou(e);var i=zn($i.current);if(n=e.type,t!==null&&e.stateNode!=null)Bm(t,e,n,s,i),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!s){if(e.stateNode===null)throw Error(T(166));return Ae(e),null}if(t=zn(At.current),Cr(e)){s=e.stateNode,n=e.type;var r=e.memoizedProps;switch(s[Rt]=e,s[zi]=r,t=(e.mode&1)!==0,n){case"dialog":ce("cancel",s),ce("close",s);break;case"iframe":case"object":case"embed":ce("load",s);break;case"video":case"audio":for(i=0;i<di.length;i++)ce(di[i],s);break;case"source":ce("error",s);break;case"img":case"image":case"link":ce("error",s),ce("load",s);break;case"details":ce("toggle",s);break;case"input":nd(s,r),ce("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!r.multiple},ce("invalid",s);break;case"textarea":id(s,r),ce("invalid",s)}Tl(n,r),i=null;for(var o in r)if(r.hasOwnProperty(o)){var a=r[o];o==="children"?typeof a=="string"?s.textContent!==a&&(r.suppressHydrationWarning!==!0&&jr(s.textContent,a,t),i=["children",a]):typeof a=="number"&&s.textContent!==""+a&&(r.suppressHydrationWarning!==!0&&jr(s.textContent,a,t),i=["children",""+a]):Mi.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&ce("scroll",s)}switch(n){case"input":yr(s),sd(s,r,!0);break;case"textarea":yr(s),rd(s);break;case"select":case"option":break;default:typeof r.onClick=="function"&&(s.onclick=wo)}s=i,e.updateQueue=s,s!==null&&(e.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=fp(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof s.is=="string"?t=o.createElement(n,{is:s.is}):(t=o.createElement(n),n==="select"&&(o=t,s.multiple?o.multiple=!0:s.size&&(o.size=s.size))):t=o.createElementNS(t,n),t[Rt]=e,t[zi]=s,Fm(t,e,!1,!1),e.stateNode=t;e:{switch(o=Rl(n,s),n){case"dialog":ce("cancel",t),ce("close",t),i=s;break;case"iframe":case"object":case"embed":ce("load",t),i=s;break;case"video":case"audio":for(i=0;i<di.length;i++)ce(di[i],t);i=s;break;case"source":ce("error",t),i=s;break;case"img":case"image":case"link":ce("error",t),ce("load",t),i=s;break;case"details":ce("toggle",t),i=s;break;case"input":nd(t,s),i=Cl(t,s),ce("invalid",t);break;case"option":i=s;break;case"select":t._wrapperState={wasMultiple:!!s.multiple},i=ge({},s,{value:void 0}),ce("invalid",t);break;case"textarea":id(t,s),i=Pl(t,s),ce("invalid",t);break;default:i=s}Tl(n,i),a=i;for(r in a)if(a.hasOwnProperty(r)){var l=a[r];r==="style"?gp(t,l):r==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&pp(t,l)):r==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Ti(t,l):typeof l=="number"&&Ti(t,""+l):r!=="suppressContentEditableWarning"&&r!=="suppressHydrationWarning"&&r!=="autoFocus"&&(Mi.hasOwnProperty(r)?l!=null&&r==="onScroll"&&ce("scroll",t):l!=null&&Fc(t,r,l,o))}switch(n){case"input":yr(t),sd(t,s,!1);break;case"textarea":yr(t),rd(t);break;case"option":s.value!=null&&t.setAttribute("value",""+kn(s.value));break;case"select":t.multiple=!!s.multiple,r=s.value,r!=null?Cs(t,!!s.multiple,r,!1):s.defaultValue!=null&&Cs(t,!!s.multiple,s.defaultValue,!0);break;default:typeof i.onClick=="function"&&(t.onclick=wo)}switch(n){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return Ae(e),null;case 6:if(t&&e.stateNode!=null)Im(t,e,t.memoizedProps,s);else{if(typeof s!="string"&&e.stateNode===null)throw Error(T(166));if(n=zn($i.current),zn(At.current),Cr(e)){if(s=e.stateNode,n=e.memoizedProps,s[Rt]=e,(r=s.nodeValue!==n)&&(t=it,t!==null))switch(t.tag){case 3:jr(s.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&jr(s.nodeValue,n,(t.mode&1)!==0)}r&&(e.flags|=4)}else s=(n.nodeType===9?n:n.ownerDocument).createTextNode(s),s[Rt]=e,e.stateNode=s}return Ae(e),null;case 13:if(ue(pe),s=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(de&&st!==null&&e.mode&1&&!(e.flags&128))sm(),As(),e.flags|=98560,r=!1;else if(r=Cr(e),s!==null&&s.dehydrated!==null){if(t===null){if(!r)throw Error(T(318));if(r=e.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(T(317));r[Rt]=e}else As(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;Ae(e),r=!1}else vt!==null&&(hc(vt),vt=null),r=!0;if(!r)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(s=s!==null,s!==(t!==null&&t.memoizedState!==null)&&s&&(e.child.flags|=8192,e.mode&1&&(t===null||pe.current&1?Ce===0&&(Ce=3):vu())),e.updateQueue!==null&&(e.flags|=4),Ae(e),null);case 4:return Fs(),ic(t,e),t===null&&Bi(e.stateNode.containerInfo),Ae(e),null;case 10:return nu(e.type._context),Ae(e),null;case 17:return Je(e.type)&&So(),Ae(e),null;case 19:if(ue(pe),r=e.memoizedState,r===null)return Ae(e),null;if(s=(e.flags&128)!==0,o=r.rendering,o===null)if(s)ei(r,!1);else{if(Ce!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(o=To(t),o!==null){for(e.flags|=128,ei(r,!1),s=o.updateQueue,s!==null&&(e.updateQueue=s,e.flags|=4),e.subtreeFlags=0,s=n,n=e.child;n!==null;)r=n,t=s,r.flags&=14680066,o=r.alternate,o===null?(r.childLanes=0,r.lanes=t,r.child=null,r.subtreeFlags=0,r.memoizedProps=null,r.memoizedState=null,r.updateQueue=null,r.dependencies=null,r.stateNode=null):(r.childLanes=o.childLanes,r.lanes=o.lanes,r.child=o.child,r.subtreeFlags=0,r.deletions=null,r.memoizedProps=o.memoizedProps,r.memoizedState=o.memoizedState,r.updateQueue=o.updateQueue,r.type=o.type,t=o.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return ae(pe,pe.current&1|2),e.child}t=t.sibling}r.tail!==null&&be()>Is&&(e.flags|=128,s=!0,ei(r,!1),e.lanes=4194304)}else{if(!s)if(t=To(o),t!==null){if(e.flags|=128,s=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),ei(r,!0),r.tail===null&&r.tailMode==="hidden"&&!o.alternate&&!de)return Ae(e),null}else 2*be()-r.renderingStartTime>Is&&n!==1073741824&&(e.flags|=128,s=!0,ei(r,!1),e.lanes=4194304);r.isBackwards?(o.sibling=e.child,e.child=o):(n=r.last,n!==null?n.sibling=o:e.child=o,r.last=o)}return r.tail!==null?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.renderingStartTime=be(),e.sibling=null,n=pe.current,ae(pe,s?n&1|2:n&1),e):(Ae(e),null);case 22:case 23:return xu(),s=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==s&&(e.flags|=8192),s&&e.mode&1?nt&1073741824&&(Ae(e),e.subtreeFlags&6&&(e.flags|=8192)):Ae(e),null;case 24:return null;case 25:return null}throw Error(T(156,e.tag))}function Hx(t,e){switch(Jc(e),e.tag){case 1:return Je(e.type)&&So(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Fs(),ue(Ge),ue(ze),au(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return ou(e),null;case 13:if(ue(pe),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(T(340));As()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return ue(pe),null;case 4:return Fs(),null;case 10:return nu(e.type._context),null;case 22:case 23:return xu(),null;case 24:return null;default:return null}}var Pr=!1,Fe=!1,Ux=typeof WeakSet=="function"?WeakSet:Set,B=null;function ks(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(s){ye(t,e,s)}else n.current=null}function rc(t,e,n){try{n()}catch(s){ye(t,e,s)}}var Kd=!1;function Vx(t,e){if($l=bo,t=Up(),Qc(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var i=s.anchorOffset,r=s.focusNode;s=s.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,c=0,d=0,h=t,f=null;t:for(;;){for(var p;h!==n||i!==0&&h.nodeType!==3||(a=o+i),h!==r||s!==0&&h.nodeType!==3||(l=o+s),h.nodeType===3&&(o+=h.nodeValue.length),(p=h.firstChild)!==null;)f=h,h=p;for(;;){if(h===t)break t;if(f===n&&++c===i&&(a=o),f===r&&++d===s&&(l=o),(p=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=p}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Hl={focusedElem:t,selectionRange:n},bo=!1,B=e;B!==null;)if(e=B,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,B=t;else for(;B!==null;){e=B;try{var m=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(m!==null){var y=m.memoizedProps,v=m.memoizedState,g=e.stateNode,x=g.getSnapshotBeforeUpdate(e.elementType===e.type?y:yt(e.type,y),v);g.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var b=e.stateNode.containerInfo;b.nodeType===1?b.textContent="":b.nodeType===9&&b.documentElement&&b.removeChild(b.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(k){ye(e,e.return,k)}if(t=e.sibling,t!==null){t.return=e.return,B=t;break}B=e.return}return m=Kd,Kd=!1,m}function ki(t,e,n){var s=e.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var i=s=s.next;do{if((i.tag&t)===t){var r=i.destroy;i.destroy=void 0,r!==void 0&&rc(e,n,r)}i=i.next}while(i!==s)}}function ua(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var s=n.create;n.destroy=s()}n=n.next}while(n!==e)}}function oc(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function zm(t){var e=t.alternate;e!==null&&(t.alternate=null,zm(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[Rt],delete e[zi],delete e[Yl],delete e[Nx],delete e[Ex])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Wm(t){return t.tag===5||t.tag===3||t.tag===4}function qd(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Wm(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ac(t,e,n){var s=t.tag;if(s===5||s===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=wo));else if(s!==4&&(t=t.child,t!==null))for(ac(t,e,n),t=t.sibling;t!==null;)ac(t,e,n),t=t.sibling}function lc(t,e,n){var s=t.tag;if(s===5||s===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(s!==4&&(t=t.child,t!==null))for(lc(t,e,n),t=t.sibling;t!==null;)lc(t,e,n),t=t.sibling}var Te=null,xt=!1;function Jt(t,e,n){for(n=n.child;n!==null;)$m(t,e,n),n=n.sibling}function $m(t,e,n){if(Dt&&typeof Dt.onCommitFiberUnmount=="function")try{Dt.onCommitFiberUnmount(na,n)}catch{}switch(n.tag){case 5:Fe||ks(n,e);case 6:var s=Te,i=xt;Te=null,Jt(t,e,n),Te=s,xt=i,Te!==null&&(xt?(t=Te,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):Te.removeChild(n.stateNode));break;case 18:Te!==null&&(xt?(t=Te,n=n.stateNode,t.nodeType===8?Ka(t.parentNode,n):t.nodeType===1&&Ka(t,n),Ai(t)):Ka(Te,n.stateNode));break;case 4:s=Te,i=xt,Te=n.stateNode.containerInfo,xt=!0,Jt(t,e,n),Te=s,xt=i;break;case 0:case 11:case 14:case 15:if(!Fe&&(s=n.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){i=s=s.next;do{var r=i,o=r.destroy;r=r.tag,o!==void 0&&(r&2||r&4)&&rc(n,e,o),i=i.next}while(i!==s)}Jt(t,e,n);break;case 1:if(!Fe&&(ks(n,e),s=n.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=n.memoizedProps,s.state=n.memoizedState,s.componentWillUnmount()}catch(a){ye(n,e,a)}Jt(t,e,n);break;case 21:Jt(t,e,n);break;case 22:n.mode&1?(Fe=(s=Fe)||n.memoizedState!==null,Jt(t,e,n),Fe=s):Jt(t,e,n);break;default:Jt(t,e,n)}}function Qd(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Ux),e.forEach(function(s){var i=ev.bind(null,t,s);n.has(s)||(n.add(s),s.then(i,i))})}}function gt(t,e){var n=e.deletions;if(n!==null)for(var s=0;s<n.length;s++){var i=n[s];try{var r=t,o=e,a=o;e:for(;a!==null;){switch(a.tag){case 5:Te=a.stateNode,xt=!1;break e;case 3:Te=a.stateNode.containerInfo,xt=!0;break e;case 4:Te=a.stateNode.containerInfo,xt=!0;break e}a=a.return}if(Te===null)throw Error(T(160));$m(r,o,i),Te=null,xt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(c){ye(i,e,c)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)Hm(e,t),e=e.sibling}function Hm(t,e){var n=t.alternate,s=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(gt(e,t),Nt(t),s&4){try{ki(3,t,t.return),ua(3,t)}catch(y){ye(t,t.return,y)}try{ki(5,t,t.return)}catch(y){ye(t,t.return,y)}}break;case 1:gt(e,t),Nt(t),s&512&&n!==null&&ks(n,n.return);break;case 5:if(gt(e,t),Nt(t),s&512&&n!==null&&ks(n,n.return),t.flags&32){var i=t.stateNode;try{Ti(i,"")}catch(y){ye(t,t.return,y)}}if(s&4&&(i=t.stateNode,i!=null)){var r=t.memoizedProps,o=n!==null?n.memoizedProps:r,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&r.type==="radio"&&r.name!=null&&dp(i,r),Rl(a,o);var c=Rl(a,r);for(o=0;o<l.length;o+=2){var d=l[o],h=l[o+1];d==="style"?gp(i,h):d==="dangerouslySetInnerHTML"?pp(i,h):d==="children"?Ti(i,h):Fc(i,d,h,c)}switch(a){case"input":Nl(i,r);break;case"textarea":hp(i,r);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!r.multiple;var p=r.value;p!=null?Cs(i,!!r.multiple,p,!1):f!==!!r.multiple&&(r.defaultValue!=null?Cs(i,!!r.multiple,r.defaultValue,!0):Cs(i,!!r.multiple,r.multiple?[]:"",!1))}i[zi]=r}catch(y){ye(t,t.return,y)}}break;case 6:if(gt(e,t),Nt(t),s&4){if(t.stateNode===null)throw Error(T(162));i=t.stateNode,r=t.memoizedProps;try{i.nodeValue=r}catch(y){ye(t,t.return,y)}}break;case 3:if(gt(e,t),Nt(t),s&4&&n!==null&&n.memoizedState.isDehydrated)try{Ai(e.containerInfo)}catch(y){ye(t,t.return,y)}break;case 4:gt(e,t),Nt(t);break;case 13:gt(e,t),Nt(t),i=t.child,i.flags&8192&&(r=i.memoizedState!==null,i.stateNode.isHidden=r,!r||i.alternate!==null&&i.alternate.memoizedState!==null||(gu=be())),s&4&&Qd(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(Fe=(c=Fe)||d,gt(e,t),Fe=c):gt(e,t),Nt(t),s&8192){if(c=t.memoizedState!==null,(t.stateNode.isHidden=c)&&!d&&t.mode&1)for(B=t,d=t.child;d!==null;){for(h=B=d;B!==null;){switch(f=B,p=f.child,f.tag){case 0:case 11:case 14:case 15:ki(4,f,f.return);break;case 1:ks(f,f.return);var m=f.stateNode;if(typeof m.componentWillUnmount=="function"){s=f,n=f.return;try{e=s,m.props=e.memoizedProps,m.state=e.memoizedState,m.componentWillUnmount()}catch(y){ye(s,n,y)}}break;case 5:ks(f,f.return);break;case 22:if(f.memoizedState!==null){Jd(h);continue}}p!==null?(p.return=f,B=p):Jd(h)}d=d.sibling}e:for(d=null,h=t;;){if(h.tag===5){if(d===null){d=h;try{i=h.stateNode,c?(r=i.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none"):(a=h.stateNode,l=h.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=mp("display",o))}catch(y){ye(t,t.return,y)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(y){ye(t,t.return,y)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===t)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===t)break e;for(;h.sibling===null;){if(h.return===null||h.return===t)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:gt(e,t),Nt(t),s&4&&Qd(t);break;case 21:break;default:gt(e,t),Nt(t)}}function Nt(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(Wm(n)){var s=n;break e}n=n.return}throw Error(T(160))}switch(s.tag){case 5:var i=s.stateNode;s.flags&32&&(Ti(i,""),s.flags&=-33);var r=qd(t);lc(t,r,i);break;case 3:case 4:var o=s.stateNode.containerInfo,a=qd(t);ac(t,a,o);break;default:throw Error(T(161))}}catch(l){ye(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Yx(t,e,n){B=t,Um(t)}function Um(t,e,n){for(var s=(t.mode&1)!==0;B!==null;){var i=B,r=i.child;if(i.tag===22&&s){var o=i.memoizedState!==null||Pr;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Fe;a=Pr;var c=Fe;if(Pr=o,(Fe=l)&&!c)for(B=i;B!==null;)o=B,l=o.child,o.tag===22&&o.memoizedState!==null?Zd(i):l!==null?(l.return=o,B=l):Zd(i);for(;r!==null;)B=r,Um(r),r=r.sibling;B=i,Pr=a,Fe=c}Gd(t)}else i.subtreeFlags&8772&&r!==null?(r.return=i,B=r):Gd(t)}}function Gd(t){for(;B!==null;){var e=B;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:Fe||ua(5,e);break;case 1:var s=e.stateNode;if(e.flags&4&&!Fe)if(n===null)s.componentDidMount();else{var i=e.elementType===e.type?n.memoizedProps:yt(e.type,n.memoizedProps);s.componentDidUpdate(i,n.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var r=e.updateQueue;r!==null&&Ad(e,r,s);break;case 3:var o=e.updateQueue;if(o!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}Ad(e,o,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var c=e.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Ai(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}Fe||e.flags&512&&oc(e)}catch(f){ye(e,e.return,f)}}if(e===t){B=null;break}if(n=e.sibling,n!==null){n.return=e.return,B=n;break}B=e.return}}function Jd(t){for(;B!==null;){var e=B;if(e===t){B=null;break}var n=e.sibling;if(n!==null){n.return=e.return,B=n;break}B=e.return}}function Zd(t){for(;B!==null;){var e=B;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{ua(4,e)}catch(l){ye(e,n,l)}break;case 1:var s=e.stateNode;if(typeof s.componentDidMount=="function"){var i=e.return;try{s.componentDidMount()}catch(l){ye(e,i,l)}}var r=e.return;try{oc(e)}catch(l){ye(e,r,l)}break;case 5:var o=e.return;try{oc(e)}catch(l){ye(e,o,l)}}}catch(l){ye(e,e.return,l)}if(e===t){B=null;break}var a=e.sibling;if(a!==null){a.return=e.return,B=a;break}B=e.return}}var Xx=Math.ceil,Do=Gt.ReactCurrentDispatcher,pu=Gt.ReactCurrentOwner,ft=Gt.ReactCurrentBatchConfig,G=0,Me=null,ke=null,Re=0,nt=0,ws=Cn(0),Ce=0,Yi=null,Jn=0,da=0,mu=0,wi=null,Ke=null,gu=0,Is=1/0,zt=null,Ao=!1,cc=null,xn=null,Mr=!1,an=null,Lo=0,Si=0,uc=null,no=-1,so=0;function He(){return G&6?be():no!==-1?no:no=be()}function vn(t){return t.mode&1?G&2&&Re!==0?Re&-Re:Mx.transition!==null?(so===0&&(so=Ep()),so):(t=ie,t!==0||(t=window.event,t=t===void 0?16:Ap(t.type)),t):1}function _t(t,e,n,s){if(50<Si)throw Si=0,uc=null,Error(T(185));rr(t,n,s),(!(G&2)||t!==Me)&&(t===Me&&(!(G&2)&&(da|=n),Ce===4&&rn(t,Re)),Ze(t,s),n===1&&G===0&&!(e.mode&1)&&(Is=be()+500,aa&&Nn()))}function Ze(t,e){var n=t.callbackNode;M0(t,e);var s=vo(t,t===Me?Re:0);if(s===0)n!==null&&ld(n),t.callbackNode=null,t.callbackPriority=0;else if(e=s&-s,t.callbackPriority!==e){if(n!=null&&ld(n),e===1)t.tag===0?Px(eh.bind(null,t)):em(eh.bind(null,t)),jx(function(){!(G&6)&&Nn()}),n=null;else{switch(Pp(s)){case 1:n=$c;break;case 4:n=Cp;break;case 16:n=xo;break;case 536870912:n=Np;break;default:n=xo}n=Jm(n,Vm.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function Vm(t,e){if(no=-1,so=0,G&6)throw Error(T(327));var n=t.callbackNode;if(Ts()&&t.callbackNode!==n)return null;var s=vo(t,t===Me?Re:0);if(s===0)return null;if(s&30||s&t.expiredLanes||e)e=Fo(t,s);else{e=s;var i=G;G|=2;var r=Xm();(Me!==t||Re!==e)&&(zt=null,Is=be()+500,Hn(t,e));do try{Qx();break}catch(a){Ym(t,a)}while(1);tu(),Do.current=r,G=i,ke!==null?e=0:(Me=null,Re=0,e=Ce)}if(e!==0){if(e===2&&(i=Fl(t),i!==0&&(s=i,e=dc(t,i))),e===1)throw n=Yi,Hn(t,0),rn(t,s),Ze(t,be()),n;if(e===6)rn(t,s);else{if(i=t.current.alternate,!(s&30)&&!Kx(i)&&(e=Fo(t,s),e===2&&(r=Fl(t),r!==0&&(s=r,e=dc(t,r))),e===1))throw n=Yi,Hn(t,0),rn(t,s),Ze(t,be()),n;switch(t.finishedWork=i,t.finishedLanes=s,e){case 0:case 1:throw Error(T(345));case 2:Dn(t,Ke,zt);break;case 3:if(rn(t,s),(s&130023424)===s&&(e=gu+500-be(),10<e)){if(vo(t,0)!==0)break;if(i=t.suspendedLanes,(i&s)!==s){He(),t.pingedLanes|=t.suspendedLanes&i;break}t.timeoutHandle=Vl(Dn.bind(null,t,Ke,zt),e);break}Dn(t,Ke,zt);break;case 4:if(rn(t,s),(s&4194240)===s)break;for(e=t.eventTimes,i=-1;0<s;){var o=31-bt(s);r=1<<o,o=e[o],o>i&&(i=o),s&=~r}if(s=i,s=be()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*Xx(s/1960))-s,10<s){t.timeoutHandle=Vl(Dn.bind(null,t,Ke,zt),s);break}Dn(t,Ke,zt);break;case 5:Dn(t,Ke,zt);break;default:throw Error(T(329))}}}return Ze(t,be()),t.callbackNode===n?Vm.bind(null,t):null}function dc(t,e){var n=wi;return t.current.memoizedState.isDehydrated&&(Hn(t,e).flags|=256),t=Fo(t,e),t!==2&&(e=Ke,Ke=n,e!==null&&hc(e)),t}function hc(t){Ke===null?Ke=t:Ke.push.apply(Ke,t)}function Kx(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var s=0;s<n.length;s++){var i=n[s],r=i.getSnapshot;i=i.value;try{if(!wt(r(),i))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function rn(t,e){for(e&=~mu,e&=~da,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-bt(e),s=1<<n;t[n]=-1,e&=~s}}function eh(t){if(G&6)throw Error(T(327));Ts();var e=vo(t,0);if(!(e&1))return Ze(t,be()),null;var n=Fo(t,e);if(t.tag!==0&&n===2){var s=Fl(t);s!==0&&(e=s,n=dc(t,s))}if(n===1)throw n=Yi,Hn(t,0),rn(t,e),Ze(t,be()),n;if(n===6)throw Error(T(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,Dn(t,Ke,zt),Ze(t,be()),null}function yu(t,e){var n=G;G|=1;try{return t(e)}finally{G=n,G===0&&(Is=be()+500,aa&&Nn())}}function Zn(t){an!==null&&an.tag===0&&!(G&6)&&Ts();var e=G;G|=1;var n=ft.transition,s=ie;try{if(ft.transition=null,ie=1,t)return t()}finally{ie=s,ft.transition=n,G=e,!(G&6)&&Nn()}}function xu(){nt=ws.current,ue(ws)}function Hn(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,Sx(n)),ke!==null)for(n=ke.return;n!==null;){var s=n;switch(Jc(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&So();break;case 3:Fs(),ue(Ge),ue(ze),au();break;case 5:ou(s);break;case 4:Fs();break;case 13:ue(pe);break;case 19:ue(pe);break;case 10:nu(s.type._context);break;case 22:case 23:xu()}n=n.return}if(Me=t,ke=t=bn(t.current,null),Re=nt=e,Ce=0,Yi=null,mu=da=Jn=0,Ke=wi=null,In!==null){for(e=0;e<In.length;e++)if(n=In[e],s=n.interleaved,s!==null){n.interleaved=null;var i=s.next,r=n.pending;if(r!==null){var o=r.next;r.next=i,s.next=o}n.pending=s}In=null}return t}function Ym(t,e){do{var n=ke;try{if(tu(),Zr.current=Oo,Ro){for(var s=me.memoizedState;s!==null;){var i=s.queue;i!==null&&(i.pending=null),s=s.next}Ro=!1}if(Gn=0,Ee=je=me=null,_i=!1,Hi=0,pu.current=null,n===null||n.return===null){Ce=1,Yi=e,ke=null;break}e:{var r=t,o=n.return,a=n,l=e;if(e=Re,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,d=a,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=Wd(o);if(p!==null){p.flags&=-257,$d(p,o,a,r,e),p.mode&1&&zd(r,c,e),e=p,l=c;var m=e.updateQueue;if(m===null){var y=new Set;y.add(l),e.updateQueue=y}else m.add(l);break e}else{if(!(e&1)){zd(r,c,e),vu();break e}l=Error(T(426))}}else if(de&&a.mode&1){var v=Wd(o);if(v!==null){!(v.flags&65536)&&(v.flags|=256),$d(v,o,a,r,e),Zc(Bs(l,a));break e}}r=l=Bs(l,a),Ce!==4&&(Ce=2),wi===null?wi=[r]:wi.push(r),r=o;do{switch(r.tag){case 3:r.flags|=65536,e&=-e,r.lanes|=e;var g=Pm(r,l,e);Dd(r,g);break e;case 1:a=l;var x=r.type,b=r.stateNode;if(!(r.flags&128)&&(typeof x.getDerivedStateFromError=="function"||b!==null&&typeof b.componentDidCatch=="function"&&(xn===null||!xn.has(b)))){r.flags|=65536,e&=-e,r.lanes|=e;var k=Mm(r,a,e);Dd(r,k);break e}}r=r.return}while(r!==null)}qm(n)}catch(_){e=_,ke===n&&n!==null&&(ke=n=n.return);continue}break}while(1)}function Xm(){var t=Do.current;return Do.current=Oo,t===null?Oo:t}function vu(){(Ce===0||Ce===3||Ce===2)&&(Ce=4),Me===null||!(Jn&268435455)&&!(da&268435455)||rn(Me,Re)}function Fo(t,e){var n=G;G|=2;var s=Xm();(Me!==t||Re!==e)&&(zt=null,Hn(t,e));do try{qx();break}catch(i){Ym(t,i)}while(1);if(tu(),G=n,Do.current=s,ke!==null)throw Error(T(261));return Me=null,Re=0,Ce}function qx(){for(;ke!==null;)Km(ke)}function Qx(){for(;ke!==null&&!_0();)Km(ke)}function Km(t){var e=Gm(t.alternate,t,nt);t.memoizedProps=t.pendingProps,e===null?qm(t):ke=e,pu.current=null}function qm(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Hx(n,e),n!==null){n.flags&=32767,ke=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{Ce=6,ke=null;return}}else if(n=$x(n,e,nt),n!==null){ke=n;return}if(e=e.sibling,e!==null){ke=e;return}ke=e=t}while(e!==null);Ce===0&&(Ce=5)}function Dn(t,e,n){var s=ie,i=ft.transition;try{ft.transition=null,ie=1,Gx(t,e,n,s)}finally{ft.transition=i,ie=s}return null}function Gx(t,e,n,s){do Ts();while(an!==null);if(G&6)throw Error(T(327));n=t.finishedWork;var i=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(T(177));t.callbackNode=null,t.callbackPriority=0;var r=n.lanes|n.childLanes;if(T0(t,r),t===Me&&(ke=Me=null,Re=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Mr||(Mr=!0,Jm(xo,function(){return Ts(),null})),r=(n.flags&15990)!==0,n.subtreeFlags&15990||r){r=ft.transition,ft.transition=null;var o=ie;ie=1;var a=G;G|=4,pu.current=null,Vx(t,n),Hm(n,t),yx(Hl),bo=!!$l,Hl=$l=null,t.current=n,Yx(n),k0(),G=a,ie=o,ft.transition=r}else t.current=n;if(Mr&&(Mr=!1,an=t,Lo=i),r=t.pendingLanes,r===0&&(xn=null),j0(n.stateNode),Ze(t,be()),e!==null)for(s=t.onRecoverableError,n=0;n<e.length;n++)i=e[n],s(i.value,{componentStack:i.stack,digest:i.digest});if(Ao)throw Ao=!1,t=cc,cc=null,t;return Lo&1&&t.tag!==0&&Ts(),r=t.pendingLanes,r&1?t===uc?Si++:(Si=0,uc=t):Si=0,Nn(),null}function Ts(){if(an!==null){var t=Pp(Lo),e=ft.transition,n=ie;try{if(ft.transition=null,ie=16>t?16:t,an===null)var s=!1;else{if(t=an,an=null,Lo=0,G&6)throw Error(T(331));var i=G;for(G|=4,B=t.current;B!==null;){var r=B,o=r.child;if(B.flags&16){var a=r.deletions;if(a!==null){for(var l=0;l<a.length;l++){var c=a[l];for(B=c;B!==null;){var d=B;switch(d.tag){case 0:case 11:case 15:ki(8,d,r)}var h=d.child;if(h!==null)h.return=d,B=h;else for(;B!==null;){d=B;var f=d.sibling,p=d.return;if(zm(d),d===c){B=null;break}if(f!==null){f.return=p,B=f;break}B=p}}}var m=r.alternate;if(m!==null){var y=m.child;if(y!==null){m.child=null;do{var v=y.sibling;y.sibling=null,y=v}while(y!==null)}}B=r}}if(r.subtreeFlags&2064&&o!==null)o.return=r,B=o;else e:for(;B!==null;){if(r=B,r.flags&2048)switch(r.tag){case 0:case 11:case 15:ki(9,r,r.return)}var g=r.sibling;if(g!==null){g.return=r.return,B=g;break e}B=r.return}}var x=t.current;for(B=x;B!==null;){o=B;var b=o.child;if(o.subtreeFlags&2064&&b!==null)b.return=o,B=b;else e:for(o=x;B!==null;){if(a=B,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ua(9,a)}}catch(_){ye(a,a.return,_)}if(a===o){B=null;break e}var k=a.sibling;if(k!==null){k.return=a.return,B=k;break e}B=a.return}}if(G=i,Nn(),Dt&&typeof Dt.onPostCommitFiberRoot=="function")try{Dt.onPostCommitFiberRoot(na,t)}catch{}s=!0}return s}finally{ie=n,ft.transition=e}}return!1}function th(t,e,n){e=Bs(n,e),e=Pm(t,e,1),t=yn(t,e,1),e=He(),t!==null&&(rr(t,1,e),Ze(t,e))}function ye(t,e,n){if(t.tag===3)th(t,t,n);else for(;e!==null;){if(e.tag===3){th(e,t,n);break}else if(e.tag===1){var s=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(xn===null||!xn.has(s))){t=Bs(n,t),t=Mm(e,t,1),e=yn(e,t,1),t=He(),e!==null&&(rr(e,1,t),Ze(e,t));break}}e=e.return}}function Jx(t,e,n){var s=t.pingCache;s!==null&&s.delete(e),e=He(),t.pingedLanes|=t.suspendedLanes&n,Me===t&&(Re&n)===n&&(Ce===4||Ce===3&&(Re&130023424)===Re&&500>be()-gu?Hn(t,0):mu|=n),Ze(t,e)}function Qm(t,e){e===0&&(t.mode&1?(e=br,br<<=1,!(br&130023424)&&(br=4194304)):e=1);var n=He();t=qt(t,e),t!==null&&(rr(t,e,n),Ze(t,n))}function Zx(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Qm(t,n)}function ev(t,e){var n=0;switch(t.tag){case 13:var s=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:s=t.stateNode;break;default:throw Error(T(314))}s!==null&&s.delete(e),Qm(t,n)}var Gm;Gm=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Ge.current)Qe=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Qe=!1,Wx(t,e,n);Qe=!!(t.flags&131072)}else Qe=!1,de&&e.flags&1048576&&tm(e,No,e.index);switch(e.lanes=0,e.tag){case 2:var s=e.type;to(t,e),t=e.pendingProps;var i=Ds(e,ze.current);Ms(e,n),i=cu(null,e,s,t,i,n);var r=uu();return e.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Je(s)?(r=!0,jo(e)):r=!1,e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,iu(e),i.updater=ca,e.stateNode=i,i._reactInternals=e,Jl(e,s,t,n),e=tc(null,e,s,!0,r,n)):(e.tag=0,de&&r&&Gc(e),$e(null,e,i,n),e=e.child),e;case 16:s=e.elementType;e:{switch(to(t,e),t=e.pendingProps,i=s._init,s=i(s._payload),e.type=s,i=e.tag=nv(s),t=yt(s,t),i){case 0:e=ec(null,e,s,t,n);break e;case 1:e=Vd(null,e,s,t,n);break e;case 11:e=Hd(null,e,s,t,n);break e;case 14:e=Ud(null,e,s,yt(s.type,t),n);break e}throw Error(T(306,s,""))}return e;case 0:return s=e.type,i=e.pendingProps,i=e.elementType===s?i:yt(s,i),ec(t,e,s,i,n);case 1:return s=e.type,i=e.pendingProps,i=e.elementType===s?i:yt(s,i),Vd(t,e,s,i,n);case 3:e:{if(Dm(e),t===null)throw Error(T(387));s=e.pendingProps,r=e.memoizedState,i=r.element,am(t,e),Mo(e,s,null,n);var o=e.memoizedState;if(s=o.element,r.isDehydrated)if(r={element:s,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){i=Bs(Error(T(423)),e),e=Yd(t,e,s,n,i);break e}else if(s!==i){i=Bs(Error(T(424)),e),e=Yd(t,e,s,n,i);break e}else for(st=gn(e.stateNode.containerInfo.firstChild),it=e,de=!0,vt=null,n=rm(e,null,s,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(As(),s===i){e=Qt(t,e,n);break e}$e(t,e,s,n)}e=e.child}return e;case 5:return lm(e),t===null&&ql(e),s=e.type,i=e.pendingProps,r=t!==null?t.memoizedProps:null,o=i.children,Ul(s,i)?o=null:r!==null&&Ul(s,r)&&(e.flags|=32),Om(t,e),$e(t,e,o,n),e.child;case 6:return t===null&&ql(e),null;case 13:return Am(t,e,n);case 4:return ru(e,e.stateNode.containerInfo),s=e.pendingProps,t===null?e.child=Ls(e,null,s,n):$e(t,e,s,n),e.child;case 11:return s=e.type,i=e.pendingProps,i=e.elementType===s?i:yt(s,i),Hd(t,e,s,i,n);case 7:return $e(t,e,e.pendingProps,n),e.child;case 8:return $e(t,e,e.pendingProps.children,n),e.child;case 12:return $e(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(s=e.type._context,i=e.pendingProps,r=e.memoizedProps,o=i.value,ae(Eo,s._currentValue),s._currentValue=o,r!==null)if(wt(r.value,o)){if(r.children===i.children&&!Ge.current){e=Qt(t,e,n);break e}}else for(r=e.child,r!==null&&(r.return=e);r!==null;){var a=r.dependencies;if(a!==null){o=r.child;for(var l=a.firstContext;l!==null;){if(l.context===s){if(r.tag===1){l=Yt(-1,n&-n),l.tag=2;var c=r.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?l.next=l:(l.next=d.next,d.next=l),c.pending=l}}r.lanes|=n,l=r.alternate,l!==null&&(l.lanes|=n),Ql(r.return,n,e),a.lanes|=n;break}l=l.next}}else if(r.tag===10)o=r.type===e.type?null:r.child;else if(r.tag===18){if(o=r.return,o===null)throw Error(T(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Ql(o,n,e),o=r.sibling}else o=r.child;if(o!==null)o.return=r;else for(o=r;o!==null;){if(o===e){o=null;break}if(r=o.sibling,r!==null){r.return=o.return,o=r;break}o=o.return}r=o}$e(t,e,i.children,n),e=e.child}return e;case 9:return i=e.type,s=e.pendingProps.children,Ms(e,n),i=pt(i),s=s(i),e.flags|=1,$e(t,e,s,n),e.child;case 14:return s=e.type,i=yt(s,e.pendingProps),i=yt(s.type,i),Ud(t,e,s,i,n);case 15:return Tm(t,e,e.type,e.pendingProps,n);case 17:return s=e.type,i=e.pendingProps,i=e.elementType===s?i:yt(s,i),to(t,e),e.tag=1,Je(s)?(t=!0,jo(e)):t=!1,Ms(e,n),Em(e,s,i),Jl(e,s,i,n),tc(null,e,s,!0,t,n);case 19:return Lm(t,e,n);case 22:return Rm(t,e,n)}throw Error(T(156,e.tag))};function Jm(t,e){return jp(t,e)}function tv(t,e,n,s){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(t,e,n,s){return new tv(t,e,n,s)}function bu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function nv(t){if(typeof t=="function")return bu(t)?1:0;if(t!=null){if(t=t.$$typeof,t===Ic)return 11;if(t===zc)return 14}return 2}function bn(t,e){var n=t.alternate;return n===null?(n=dt(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function io(t,e,n,s,i,r){var o=2;if(s=t,typeof t=="function")bu(t)&&(o=1);else if(typeof t=="string")o=5;else e:switch(t){case fs:return Un(n.children,i,r,e);case Bc:o=8,i|=8;break;case kl:return t=dt(12,n,e,i|2),t.elementType=kl,t.lanes=r,t;case wl:return t=dt(13,n,e,i),t.elementType=wl,t.lanes=r,t;case Sl:return t=dt(19,n,e,i),t.elementType=Sl,t.lanes=r,t;case lp:return ha(n,i,r,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case op:o=10;break e;case ap:o=9;break e;case Ic:o=11;break e;case zc:o=14;break e;case tn:o=16,s=null;break e}throw Error(T(130,t==null?t:typeof t,""))}return e=dt(o,n,e,i),e.elementType=t,e.type=s,e.lanes=r,e}function Un(t,e,n,s){return t=dt(7,t,s,e),t.lanes=n,t}function ha(t,e,n,s){return t=dt(22,t,s,e),t.elementType=lp,t.lanes=n,t.stateNode={isHidden:!1},t}function nl(t,e,n){return t=dt(6,t,null,e),t.lanes=n,t}function sl(t,e,n){return e=dt(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function sv(t,e,n,s,i){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Fa(0),this.expirationTimes=Fa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fa(0),this.identifierPrefix=s,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function _u(t,e,n,s,i,r,o,a,l){return t=new sv(t,e,n,a,l),e===1?(e=1,r===!0&&(e|=8)):e=0,r=dt(3,null,null,e),t.current=r,r.stateNode=t,r.memoizedState={element:s,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},iu(r),t}function iv(t,e,n){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:hs,key:s==null?null:""+s,children:t,containerInfo:e,implementation:n}}function Zm(t){if(!t)return wn;t=t._reactInternals;e:{if(rs(t)!==t||t.tag!==1)throw Error(T(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Je(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(T(171))}if(t.tag===1){var n=t.type;if(Je(n))return Zp(t,n,e)}return e}function eg(t,e,n,s,i,r,o,a,l){return t=_u(n,s,!0,t,i,r,o,a,l),t.context=Zm(null),n=t.current,s=He(),i=vn(n),r=Yt(s,i),r.callback=e??null,yn(n,r,i),t.current.lanes=i,rr(t,i,s),Ze(t,s),t}function fa(t,e,n,s){var i=e.current,r=He(),o=vn(i);return n=Zm(n),e.context===null?e.context=n:e.pendingContext=n,e=Yt(r,o),e.payload={element:t},s=s===void 0?null:s,s!==null&&(e.callback=s),t=yn(i,e,o),t!==null&&(_t(t,i,o,r),Jr(t,i,o)),o}function Bo(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function nh(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function ku(t,e){nh(t,e),(t=t.alternate)&&nh(t,e)}function rv(){return null}var tg=typeof reportError=="function"?reportError:function(t){console.error(t)};function wu(t){this._internalRoot=t}pa.prototype.render=wu.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(T(409));fa(t,e,null,null)};pa.prototype.unmount=wu.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Zn(function(){fa(null,t,null,null)}),e[Kt]=null}};function pa(t){this._internalRoot=t}pa.prototype.unstable_scheduleHydration=function(t){if(t){var e=Rp();t={blockedOn:null,target:t,priority:e};for(var n=0;n<sn.length&&e!==0&&e<sn[n].priority;n++);sn.splice(n,0,t),n===0&&Dp(t)}};function Su(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function ma(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function sh(){}function ov(t,e,n,s,i){if(i){if(typeof s=="function"){var r=s;s=function(){var c=Bo(o);r.call(c)}}var o=eg(e,s,t,0,null,!1,!1,"",sh);return t._reactRootContainer=o,t[Kt]=o.current,Bi(t.nodeType===8?t.parentNode:t),Zn(),o}for(;i=t.lastChild;)t.removeChild(i);if(typeof s=="function"){var a=s;s=function(){var c=Bo(l);a.call(c)}}var l=_u(t,0,!1,null,null,!1,!1,"",sh);return t._reactRootContainer=l,t[Kt]=l.current,Bi(t.nodeType===8?t.parentNode:t),Zn(function(){fa(e,l,n,s)}),l}function ga(t,e,n,s,i){var r=n._reactRootContainer;if(r){var o=r;if(typeof i=="function"){var a=i;i=function(){var l=Bo(o);a.call(l)}}fa(e,o,t,i)}else o=ov(n,e,t,i,s);return Bo(o)}Mp=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=ui(e.pendingLanes);n!==0&&(Hc(e,n|1),Ze(e,be()),!(G&6)&&(Is=be()+500,Nn()))}break;case 13:Zn(function(){var s=qt(t,1);if(s!==null){var i=He();_t(s,t,1,i)}}),ku(t,1)}};Uc=function(t){if(t.tag===13){var e=qt(t,134217728);if(e!==null){var n=He();_t(e,t,134217728,n)}ku(t,134217728)}};Tp=function(t){if(t.tag===13){var e=vn(t),n=qt(t,e);if(n!==null){var s=He();_t(n,t,e,s)}ku(t,e)}};Rp=function(){return ie};Op=function(t,e){var n=ie;try{return ie=t,e()}finally{ie=n}};Dl=function(t,e,n){switch(e){case"input":if(Nl(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var s=n[e];if(s!==t&&s.form===t.form){var i=oa(s);if(!i)throw Error(T(90));up(s),Nl(s,i)}}}break;case"textarea":hp(t,n);break;case"select":e=n.value,e!=null&&Cs(t,!!n.multiple,e,!1)}};vp=yu;bp=Zn;var av={usingClientEntryPoint:!1,Events:[ar,ys,oa,yp,xp,yu]},ti={findFiberByHostInstance:Bn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},lv={bundleType:ti.bundleType,version:ti.version,rendererPackageName:ti.rendererPackageName,rendererConfig:ti.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Gt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=wp(t),t===null?null:t.stateNode},findFiberByHostInstance:ti.findFiberByHostInstance||rv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Tr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Tr.isDisabled&&Tr.supportsFiber)try{na=Tr.inject(lv),Dt=Tr}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=av;ot.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Su(e))throw Error(T(200));return iv(t,e,null,n)};ot.createRoot=function(t,e){if(!Su(t))throw Error(T(299));var n=!1,s="",i=tg;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(s=e.identifierPrefix),e.onRecoverableError!==void 0&&(i=e.onRecoverableError)),e=_u(t,1,!1,null,null,n,!1,s,i),t[Kt]=e.current,Bi(t.nodeType===8?t.parentNode:t),new wu(e)};ot.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(T(188)):(t=Object.keys(t).join(","),Error(T(268,t)));return t=wp(e),t=t===null?null:t.stateNode,t};ot.flushSync=function(t){return Zn(t)};ot.hydrate=function(t,e,n){if(!ma(e))throw Error(T(200));return ga(null,t,e,!0,n)};ot.hydrateRoot=function(t,e,n){if(!Su(t))throw Error(T(405));var s=n!=null&&n.hydratedSources||null,i=!1,r="",o=tg;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),e=eg(e,null,t,1,n??null,i,!1,r,o),t[Kt]=e.current,Bi(t),s)for(t=0;t<s.length;t++)n=s[t],i=n._getVersion,i=i(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,i]:e.mutableSourceEagerHydrationData.push(n,i);return new pa(e)};ot.render=function(t,e,n){if(!ma(e))throw Error(T(200));return ga(null,t,e,!1,n)};ot.unmountComponentAtNode=function(t){if(!ma(t))throw Error(T(40));return t._reactRootContainer?(Zn(function(){ga(null,null,t,!1,function(){t._reactRootContainer=null,t[Kt]=null})}),!0):!1};ot.unstable_batchedUpdates=yu;ot.unstable_renderSubtreeIntoContainer=function(t,e,n,s){if(!ma(n))throw Error(T(200));if(t==null||t._reactInternals===void 0)throw Error(T(38));return ga(t,e,n,!1,s)};ot.version="18.3.1-next-f1338f8080-20240426";function ng(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ng)}catch(t){console.error(t)}}ng(),np.exports=ot;var cv=np.exports,ih=cv;bl.createRoot=ih.createRoot,bl.hydrateRoot=ih.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xi(){return Xi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Xi.apply(this,arguments)}var ln;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(ln||(ln={}));const rh="popstate";function uv(t){t===void 0&&(t={});function e(s,i){let{pathname:r,search:o,hash:a}=s.location;return fc("",{pathname:r,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(s,i){return typeof i=="string"?i:ig(i)}return hv(e,n,null,t)}function we(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function sg(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function dv(){return Math.random().toString(36).substr(2,8)}function oh(t,e){return{usr:t.state,key:t.key,idx:e}}function fc(t,e,n,s){return n===void 0&&(n=null),Xi({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Vs(e):e,{state:n,key:e&&e.key||s||dv()})}function ig(t){let{pathname:e="/",search:n="",hash:s=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),s&&s!=="#"&&(e+=s.charAt(0)==="#"?s:"#"+s),e}function Vs(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let s=t.indexOf("?");s>=0&&(e.search=t.substr(s),t=t.substr(0,s)),t&&(e.pathname=t)}return e}function hv(t,e,n,s){s===void 0&&(s={});let{window:i=document.defaultView,v5Compat:r=!1}=s,o=i.history,a=ln.Pop,l=null,c=d();c==null&&(c=0,o.replaceState(Xi({},o.state,{idx:c}),""));function d(){return(o.state||{idx:null}).idx}function h(){a=ln.Pop;let v=d(),g=v==null?null:v-c;c=v,l&&l({action:a,location:y.location,delta:g})}function f(v,g){a=ln.Push;let x=fc(y.location,v,g);n&&n(x,v),c=d()+1;let b=oh(x,c),k=y.createHref(x);try{o.pushState(b,"",k)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;i.location.assign(k)}r&&l&&l({action:a,location:y.location,delta:1})}function p(v,g){a=ln.Replace;let x=fc(y.location,v,g);n&&n(x,v),c=d();let b=oh(x,c),k=y.createHref(x);o.replaceState(b,"",k),r&&l&&l({action:a,location:y.location,delta:0})}function m(v){let g=i.location.origin!=="null"?i.location.origin:i.location.href,x=typeof v=="string"?v:ig(v);return x=x.replace(/ $/,"%20"),we(g,"No window.location.(origin|href) available to create URL for href: "+x),new URL(x,g)}let y={get action(){return a},get location(){return t(i,o)},listen(v){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(rh,h),l=v,()=>{i.removeEventListener(rh,h),l=null}},createHref(v){return e(i,v)},createURL:m,encodeLocation(v){let g=m(v);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:p,go(v){return o.go(v)}};return y}var ah;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(ah||(ah={}));function fv(t,e,n){return n===void 0&&(n="/"),pv(t,e,n,!1)}function pv(t,e,n,s){let i=typeof e=="string"?Vs(e):e,r=ag(i.pathname||"/",n);if(r==null)return null;let o=rg(t);mv(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let c=Cv(r);a=Sv(o[l],c,s)}return a}function rg(t,e,n,s){e===void 0&&(e=[]),n===void 0&&(n=[]),s===void 0&&(s="");let i=(r,o,a)=>{let l={relativePath:a===void 0?r.path||"":a,caseSensitive:r.caseSensitive===!0,childrenIndex:o,route:r};l.relativePath.startsWith("/")&&(we(l.relativePath.startsWith(s),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+s+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(s.length));let c=Vn([s,l.relativePath]),d=n.concat(l);r.children&&r.children.length>0&&(we(r.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),rg(r.children,e,d,c)),!(r.path==null&&!r.index)&&e.push({path:c,score:kv(c,r.index),routesMeta:d})};return t.forEach((r,o)=>{var a;if(r.path===""||!((a=r.path)!=null&&a.includes("?")))i(r,o);else for(let l of og(r.path))i(r,o,l)}),e}function og(t){let e=t.split("/");if(e.length===0)return[];let[n,...s]=e,i=n.endsWith("?"),r=n.replace(/\?$/,"");if(s.length===0)return i?[r,""]:[r];let o=og(s.join("/")),a=[];return a.push(...o.map(l=>l===""?r:[r,l].join("/"))),i&&a.push(...o),a.map(l=>t.startsWith("/")&&l===""?"/":l)}function mv(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:wv(e.routesMeta.map(s=>s.childrenIndex),n.routesMeta.map(s=>s.childrenIndex)))}const gv=/^:[\w-]+$/,yv=3,xv=2,vv=1,bv=10,_v=-2,lh=t=>t==="*";function kv(t,e){let n=t.split("/"),s=n.length;return n.some(lh)&&(s+=_v),e&&(s+=xv),n.filter(i=>!lh(i)).reduce((i,r)=>i+(gv.test(r)?yv:r===""?vv:bv),s)}function wv(t,e){return t.length===e.length&&t.slice(0,-1).every((s,i)=>s===e[i])?t[t.length-1]-e[e.length-1]:0}function Sv(t,e,n){n===void 0&&(n=!1);let{routesMeta:s}=t,i={},r="/",o=[];for(let a=0;a<s.length;++a){let l=s[a],c=a===s.length-1,d=r==="/"?e:e.slice(r.length)||"/",h=ch({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},d),f=l.route;if(!h&&c&&n&&!s[s.length-1].route.index&&(h=ch({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},d)),!h)return null;Object.assign(i,h.params),o.push({params:i,pathname:Vn([r,h.pathname]),pathnameBase:Mv(Vn([r,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(r=Vn([r,h.pathnameBase]))}return o}function ch(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,s]=jv(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let r=i[0],o=r.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:s.reduce((c,d,h)=>{let{paramName:f,isOptional:p}=d;if(f==="*"){let y=a[h]||"";o=r.slice(0,r.length-y.length).replace(/(.)\/+$/,"$1")}const m=a[h];return p&&!m?c[f]=void 0:c[f]=(m||"").replace(/%2F/g,"/"),c},{}),pathname:r,pathnameBase:o,pattern:t}}function jv(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),sg(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let s=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(s.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(s.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),s]}function Cv(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return sg(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function ag(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,s=t.charAt(n);return s&&s!=="/"?null:t.slice(n)||"/"}function Nv(t,e){e===void 0&&(e="/");let{pathname:n,search:s="",hash:i=""}=typeof t=="string"?Vs(t):t;return{pathname:n?n.startsWith("/")?n:Ev(n,e):e,search:Tv(s),hash:Rv(i)}}function Ev(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function il(t,e,n,s){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(s)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Pv(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function lg(t,e){let n=Pv(t);return e?n.map((s,i)=>i===n.length-1?s.pathname:s.pathnameBase):n.map(s=>s.pathnameBase)}function cg(t,e,n,s){s===void 0&&(s=!1);let i;typeof t=="string"?i=Vs(t):(i=Xi({},t),we(!i.pathname||!i.pathname.includes("?"),il("?","pathname","search",i)),we(!i.pathname||!i.pathname.includes("#"),il("#","pathname","hash",i)),we(!i.search||!i.search.includes("#"),il("#","search","hash",i)));let r=t===""||i.pathname==="",o=r?"/":i.pathname,a;if(o==null)a=n;else{let h=e.length-1;if(!s&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),h-=1;i.pathname=f.join("/")}a=h>=0?e[h]:"/"}let l=Nv(i,a),c=o&&o!=="/"&&o.endsWith("/"),d=(r||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(c||d)&&(l.pathname+="/"),l}const Vn=t=>t.join("/").replace(/\/\/+/g,"/"),Mv=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Tv=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Rv=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function Ov(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const ug=["post","put","patch","delete"];new Set(ug);const Dv=["get",...ug];new Set(Dv);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ki(){return Ki=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Ki.apply(this,arguments)}const ju=P.createContext(null),Av=P.createContext(null),cr=P.createContext(null),ya=P.createContext(null),os=P.createContext({outlet:null,matches:[],isDataRoute:!1}),dg=P.createContext(null);function ur(){return P.useContext(ya)!=null}function Cu(){return ur()||we(!1),P.useContext(ya).location}function hg(t){P.useContext(cr).static||P.useLayoutEffect(t)}function xa(){let{isDataRoute:t}=P.useContext(os);return t?Kv():Lv()}function Lv(){ur()||we(!1);let t=P.useContext(ju),{basename:e,future:n,navigator:s}=P.useContext(cr),{matches:i}=P.useContext(os),{pathname:r}=Cu(),o=JSON.stringify(lg(i,n.v7_relativeSplatPath)),a=P.useRef(!1);return hg(()=>{a.current=!0}),P.useCallback(function(c,d){if(d===void 0&&(d={}),!a.current)return;if(typeof c=="number"){s.go(c);return}let h=cg(c,JSON.parse(o),r,d.relative==="path");t==null&&e!=="/"&&(h.pathname=h.pathname==="/"?e:Vn([e,h.pathname])),(d.replace?s.replace:s.push)(h,d.state,d)},[e,s,o,r,t])}function Fv(t,e){return Bv(t,e)}function Bv(t,e,n,s){ur()||we(!1);let{navigator:i}=P.useContext(cr),{matches:r}=P.useContext(os),o=r[r.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let c=Cu(),d;if(e){var h;let v=typeof e=="string"?Vs(e):e;l==="/"||(h=v.pathname)!=null&&h.startsWith(l)||we(!1),d=v}else d=c;let f=d.pathname||"/",p=f;if(l!=="/"){let v=l.replace(/^\//,"").split("/");p="/"+f.replace(/^\//,"").split("/").slice(v.length).join("/")}let m=fv(t,{pathname:p}),y=Hv(m&&m.map(v=>Object.assign({},v,{params:Object.assign({},a,v.params),pathname:Vn([l,i.encodeLocation?i.encodeLocation(v.pathname).pathname:v.pathname]),pathnameBase:v.pathnameBase==="/"?l:Vn([l,i.encodeLocation?i.encodeLocation(v.pathnameBase).pathname:v.pathnameBase])})),r,n,s);return e&&y?P.createElement(ya.Provider,{value:{location:Ki({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:ln.Pop}},y):y}function Iv(){let t=Xv(),e=Ov(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},r=null;return P.createElement(P.Fragment,null,P.createElement("h2",null,"Unexpected Application Error!"),P.createElement("h3",{style:{fontStyle:"italic"}},e),n?P.createElement("pre",{style:i},n):null,r)}const zv=P.createElement(Iv,null);class Wv extends P.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?P.createElement(os.Provider,{value:this.props.routeContext},P.createElement(dg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function $v(t){let{routeContext:e,match:n,children:s}=t,i=P.useContext(ju);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),P.createElement(os.Provider,{value:e},s)}function Hv(t,e,n,s){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),s===void 0&&(s=null),t==null){var r;if(!n)return null;if(n.errors)t=n.matches;else if((r=s)!=null&&r.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let o=t,a=(i=n)==null?void 0:i.errors;if(a!=null){let d=o.findIndex(h=>h.route.id&&(a==null?void 0:a[h.route.id])!==void 0);d>=0||we(!1),o=o.slice(0,Math.min(o.length,d+1))}let l=!1,c=-1;if(n&&s&&s.v7_partialHydration)for(let d=0;d<o.length;d++){let h=o[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(c=d),h.route.id){let{loaderData:f,errors:p}=n,m=h.route.loader&&f[h.route.id]===void 0&&(!p||p[h.route.id]===void 0);if(h.route.lazy||m){l=!0,c>=0?o=o.slice(0,c+1):o=[o[0]];break}}}return o.reduceRight((d,h,f)=>{let p,m=!1,y=null,v=null;n&&(p=a&&h.route.id?a[h.route.id]:void 0,y=h.route.errorElement||zv,l&&(c<0&&f===0?(qv("route-fallback",!1),m=!0,v=null):c===f&&(m=!0,v=h.route.hydrateFallbackElement||null)));let g=e.concat(o.slice(0,f+1)),x=()=>{let b;return p?b=y:m?b=v:h.route.Component?b=P.createElement(h.route.Component,null):h.route.element?b=h.route.element:b=d,P.createElement($v,{match:h,routeContext:{outlet:d,matches:g,isDataRoute:n!=null},children:b})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?P.createElement(Wv,{location:n.location,revalidation:n.revalidation,component:y,error:p,children:x(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):x()},null)}var fg=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(fg||{}),Io=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(Io||{});function Uv(t){let e=P.useContext(ju);return e||we(!1),e}function Vv(t){let e=P.useContext(Av);return e||we(!1),e}function Yv(t){let e=P.useContext(os);return e||we(!1),e}function pg(t){let e=Yv(),n=e.matches[e.matches.length-1];return n.route.id||we(!1),n.route.id}function Xv(){var t;let e=P.useContext(dg),n=Vv(Io.UseRouteError),s=pg(Io.UseRouteError);return e!==void 0?e:(t=n.errors)==null?void 0:t[s]}function Kv(){let{router:t}=Uv(fg.UseNavigateStable),e=pg(Io.UseNavigateStable),n=P.useRef(!1);return hg(()=>{n.current=!0}),P.useCallback(function(i,r){r===void 0&&(r={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Ki({fromRouteId:e},r)))},[t,e])}const uh={};function qv(t,e,n){!e&&!uh[t]&&(uh[t]=!0)}function Qv(t,e){t==null||t.v7_startTransition,(t==null?void 0:t.v7_relativeSplatPath)===void 0&&(!e||e.v7_relativeSplatPath),e&&(e.v7_fetcherPersist,e.v7_normalizeFormMethod,e.v7_partialHydration,e.v7_skipActionErrorRevalidation)}function Gv(t){let{to:e,replace:n,state:s,relative:i}=t;ur()||we(!1);let{future:r,static:o}=P.useContext(cr),{matches:a}=P.useContext(os),{pathname:l}=Cu(),c=xa(),d=cg(e,lg(a,r.v7_relativeSplatPath),l,i==="path"),h=JSON.stringify(d);return P.useEffect(()=>c(JSON.parse(h),{replace:n,state:s,relative:i}),[c,h,i,n,s]),null}function ds(t){we(!1)}function Jv(t){let{basename:e="/",children:n=null,location:s,navigationType:i=ln.Pop,navigator:r,static:o=!1,future:a}=t;ur()&&we(!1);let l=e.replace(/^\/*/,"/"),c=P.useMemo(()=>({basename:l,navigator:r,static:o,future:Ki({v7_relativeSplatPath:!1},a)}),[l,a,r,o]);typeof s=="string"&&(s=Vs(s));let{pathname:d="/",search:h="",hash:f="",state:p=null,key:m="default"}=s,y=P.useMemo(()=>{let v=ag(d,l);return v==null?null:{location:{pathname:v,search:h,hash:f,state:p,key:m},navigationType:i}},[l,d,h,f,p,m,i]);return y==null?null:P.createElement(cr.Provider,{value:c},P.createElement(ya.Provider,{children:n,value:y}))}function Zv(t){let{children:e,location:n}=t;return Fv(pc(e),n)}new Promise(()=>{});function pc(t,e){e===void 0&&(e=[]);let n=[];return P.Children.forEach(t,(s,i)=>{if(!P.isValidElement(s))return;let r=[...e,i];if(s.type===P.Fragment){n.push.apply(n,pc(s.props.children,r));return}s.type!==ds&&we(!1),!s.props.index||!s.props.children||we(!1);let o={id:s.props.id||r.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(o.children=pc(s.props.children,r)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const eb="6";try{window.__reactRouterVersion=eb}catch{}const tb="startTransition",dh=Jy[tb];function nb(t){let{basename:e,children:n,future:s,window:i}=t,r=P.useRef();r.current==null&&(r.current=uv({window:i,v5Compat:!0}));let o=r.current,[a,l]=P.useState({action:o.action,location:o.location}),{v7_startTransition:c}=s||{},d=P.useCallback(h=>{c&&dh?dh(()=>l(h)):l(h)},[l,c]);return P.useLayoutEffect(()=>o.listen(d),[o,d]),P.useEffect(()=>Qv(s),[s]),P.createElement(Jv,{basename:e,children:n,location:a.location,navigationType:a.action,navigator:o,future:s})}var hh;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(hh||(hh={}));var fh;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(fh||(fh={}));const sb=({onLogin:t})=>{const[e,n]=P.useState({name:"",employee_id:""}),[s,i]=P.useState(!1),[r,o]=P.useState(""),a=async c=>{c.preventDefault(),i(!0),o("");try{console.log("Attempting login with:",e);const d=await fetch("http://localhost:5000/login",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(e),mode:"cors"});console.log("Response status:",d.status);const h=await d.json();console.log("Response data:",h),d.ok&&h.status==="success"?(console.log("Login successful, calling onLogin"),t(h.employee)):d.status===401?o(h.message||"Invalid credentials. Please check your name and Employee ID."):d.status===400?o(h.message||"Please fill in all fields."):o(h.message||`Server error: ${d.status}`)}catch(d){console.error("Login error:",d),d.name==="TypeError"&&d.message.includes("fetch")?o(`❌ Cannot connect to backend server. Please start the backend first:

1. Open Command Prompt
2. Go to: c:\\smart library\\backend
3. Run: python app_with_admin_features.py`):d.message.includes("CORS")?o("CORS error. Backend CORS configuration issue."):d.message.includes("NetworkError")?o("Network error. Check if backend is running on port 5000."):o(`Connection failed: ${d.message}

Please ensure backend is running.`)}finally{i(!1)}},l=c=>{const{name:d,value:h}=c.target;n({...e,[d]:h})};return u.jsx("div",{className:"professional-login",children:u.jsx("div",{className:"login-container",children:u.jsxs("div",{className:"login-card",children:[u.jsxs("div",{className:"login-header",children:[u.jsxs("div",{className:"company-logo",children:[u.jsx("div",{className:"logo-icon",children:"📚"}),u.jsx("h1",{children:"Smart Library"})]}),u.jsx("p",{className:"login-subtitle",children:"Enterprise Library Management System"})]}),u.jsxs("form",{onSubmit:a,className:"login-form",children:[u.jsxs("div",{className:"form-group",children:[u.jsx("label",{htmlFor:"name",className:"form-label",children:"Employee Name"}),u.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:l,className:"form-input",placeholder:"Enter your full name",required:!0,autoComplete:"name"})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{htmlFor:"employee_id",className:"form-label",children:"Employee ID"}),u.jsx("input",{type:"text",id:"employee_id",name:"employee_id",value:e.employee_id,onChange:l,className:"form-input",placeholder:"Enter your Employee ID",required:!0,autoComplete:"username"})]}),r&&u.jsxs("div",{className:"error-message",children:[u.jsx("div",{className:"error-icon",children:"⚠️"}),u.jsx("span",{children:r})]}),u.jsx("button",{type:"submit",className:"btn btn-primary btn-login",disabled:s,children:s?u.jsxs(u.Fragment,{children:[u.jsx("div",{className:"spinner"}),"Signing In..."]}):u.jsxs(u.Fragment,{children:[u.jsx("span",{children:"Sign In"}),u.jsx("span",{className:"login-arrow",children:"→"})]})})]}),u.jsx("div",{className:"login-footer",children:u.jsxs("div",{className:"login-help",children:[u.jsx("h4",{children:"Login Instructions"}),u.jsx("p",{children:"Enter your registered employee name and Employee ID to access the system."}),u.jsxs("p",{children:[u.jsx("strong",{children:"Note:"})," Use your exact name and Employee ID as registered in the employee database."]})]})})]})})})},ib=()=>{const t=xa(),e=n=>{localStorage.setItem("employee",JSON.stringify(n)),n.role==="admin"?t("/admin"):t("/employee")};return u.jsx(sb,{onLogin:e})};const rb=({employee:t,onLogout:e})=>{const[n,s]=P.useState("overview"),[i,r]=P.useState([]),[o,a]=P.useState([]),[l,c]=P.useState([]),[d,h]=P.useState({}),[f,p]=P.useState(!1),[m,y]=P.useState("all"),[v,g]=P.useState(null),[x,b]=P.useState(!1);P.useState({});const[k,_]=P.useState(null);P.useEffect(()=>{C();const M=setInterval(()=>{C()},3e4);return _(M),()=>{M&&clearInterval(M)}},[]),P.useEffect(()=>{N()},[i,o]);const C=async()=>{p(!0);try{const M=await fetch("http://localhost:5000/books");if(M.ok){const z=await M.json();r(z.books||[])}const H=await fetch("http://localhost:5000/employees");if(H.ok){const z=await H.json();a(z.employees||[])}const U=await fetch("http://localhost:5000/feedback");if(U.ok){const z=await U.json();c(z.feedback||[])}N()}catch(M){console.error("Error fetching dashboard data:",M)}finally{p(!1)}},N=()=>{if(!i.length||!o.length)return;const M=i.length,H=i.filter(w=>w.status==="available").length,U=i.filter(w=>w.status==="taken").length,z=i.filter(w=>w.status==="overdue").length,K=i.filter(w=>w.language==="English").length,ee=i.filter(w=>w.language==="Tamil").length,D=i.filter(w=>w.status==="taken"&&w.language==="English").length,j=i.filter(w=>w.status==="taken"&&w.language==="Tamil").length,O=new Set(i.filter(w=>w.status==="taken").map(w=>w.taken_by_employee_id)).size,W={};o.forEach(w=>{const A=w.department||"Unknown";W[A]||(W[A]={total:0,withBooks:0,booksCount:0}),W[A].total++;const V=i.filter(te=>te.status==="taken"&&te.taken_by_employee_id===w.employee_id);V.length>0&&(W[A].withBooks++,W[A].booksCount+=V.length)});const $={};i.forEach(w=>{const A=w.genre||"Unknown";$[A]||($[A]={total:0,taken:0,language:w.language}),$[A].total++,w.status==="taken"&&$[A].taken++});const Q=i.filter(w=>w.times_taken>0).sort((w,A)=>A.times_taken-w.times_taken).slice(0,10),re=new Date,ve=i.filter(w=>{if(w.status==="taken"&&w.due_date){const A=new Date(w.due_date),V=Math.ceil((A-re)/(1e3*60*60*24));return V<=3&&V>=0}return!1});h({totalBooks:M,availableBooks:H,takenBooks:U,overdueBooks:z,englishBooks:K,tamilBooks:ee,englishTaken:D,tamilTaken:j,employeesWithBooks:O,totalEmployees:o.length,departmentStats:W,genreStats:$,popularBooks:Q,dueSoon:ve.length,totalFeedback:l.length,utilizationRate:M>0?(U/M*100).toFixed(1):0})},S=()=>{var M;return u.jsxs("div",{className:"admin-overview",children:[u.jsxs("div",{className:"real-time-header",children:[u.jsxs("div",{className:"status-indicator",children:[u.jsx("span",{className:"live-dot"}),u.jsx("span",{children:"Live Dashboard - Updates every 30 seconds"})]}),u.jsxs("div",{className:"last-updated",children:["Last updated: ",new Date().toLocaleTimeString()]})]}),u.jsxs("div",{className:"stats-grid",children:[u.jsxs("div",{className:"stat-card total-books premium",children:[u.jsx("div",{className:"stat-icon",children:"📚"}),u.jsxs("div",{className:"stat-content",children:[u.jsx("h3",{children:d.totalBooks}),u.jsx("p",{children:"Total Library Collection"}),u.jsxs("div",{className:"stat-breakdown",children:[u.jsxs("span",{children:["🇬🇧 ",d.englishBooks," English Books"]}),u.jsxs("span",{children:["🇮🇳 ",d.tamilBooks," தமிழ் நூல்கள்"]})]}),u.jsx("div",{className:"utilization-rate",children:u.jsxs("span",{children:["📊 ",d.utilizationRate,"% Utilization Rate"]})})]})]}),u.jsxs("div",{className:"stat-card books-in-circulation premium",children:[u.jsx("div",{className:"stat-icon",children:"🔄"}),u.jsxs("div",{className:"stat-content",children:[u.jsx("h3",{children:d.takenBooks}),u.jsx("p",{children:"Books in Circulation"}),u.jsxs("div",{className:"language-breakdown",children:[u.jsxs("div",{className:"lang-stat",children:[u.jsx("span",{className:"lang-label",children:"English:"}),u.jsx("span",{className:"lang-count",children:d.englishTaken})]}),u.jsxs("div",{className:"lang-stat",children:[u.jsx("span",{className:"lang-label",children:"தமிழ்:"}),u.jsx("span",{className:"lang-count",children:d.tamilTaken})]})]}),u.jsx("div",{className:"progress-bar",children:u.jsx("div",{className:"progress-fill taken",style:{width:`${d.takenBooks/d.totalBooks*100}%`}})})]})]}),u.jsxs("div",{className:"stat-card available-books premium",children:[u.jsx("div",{className:"stat-icon",children:"✅"}),u.jsxs("div",{className:"stat-content",children:[u.jsx("h3",{children:d.availableBooks}),u.jsx("p",{children:"Available for Borrowing"}),u.jsx("div",{className:"availability-rate",children:u.jsxs("span",{children:[Math.round(d.availableBooks/d.totalBooks*100),"% Available"]})}),u.jsx("div",{className:"progress-bar",children:u.jsx("div",{className:"progress-fill available",style:{width:`${d.availableBooks/d.totalBooks*100}%`}})})]})]}),u.jsxs("div",{className:"stat-card active-readers premium",children:[u.jsx("div",{className:"stat-icon",children:"👥"}),u.jsxs("div",{className:"stat-content",children:[u.jsx("h3",{children:d.employeesWithBooks}),u.jsx("p",{children:"Active Readers"}),u.jsx("div",{className:"reader-percentage",children:u.jsxs("span",{children:[Math.round(d.employeesWithBooks/d.totalEmployees*100),"% of Staff"]})}),u.jsx("div",{className:"stat-breakdown",children:u.jsxs("span",{children:["📊 ",d.totalEmployees," Total Employees"]})})]})]}),u.jsxs("div",{className:"stat-card due-soon warning",children:[u.jsx("div",{className:"stat-icon",children:"⏰"}),u.jsxs("div",{className:"stat-content",children:[u.jsx("h3",{children:d.dueSoon}),u.jsx("p",{children:"Due Within 3 Days"}),u.jsx("div",{className:"urgency-indicator",children:d.dueSoon>0?u.jsx("span",{className:"urgent",children:"🚨 Requires Attention"}):u.jsx("span",{className:"good",children:"✅ All Current"})})]})]}),u.jsxs("div",{className:"stat-card overdue danger",children:[u.jsx("div",{className:"stat-icon",children:"⚠️"}),u.jsxs("div",{className:"stat-content",children:[u.jsx("h3",{children:d.overdueBooks}),u.jsx("p",{children:"Overdue Books"}),u.jsx("div",{className:"overdue-indicator",children:d.overdueBooks>0?u.jsx("span",{className:"critical",children:"📞 Contact Required"}):u.jsx("span",{className:"excellent",children:"🎉 No Overdue"})})]})]})]}),u.jsxs("div",{className:"department-analytics",children:[u.jsxs("div",{className:"section-header",children:[u.jsx("h3",{children:"🏢 Department-wise Reading Analytics"}),u.jsx("div",{className:"analytics-summary",children:u.jsxs("span",{children:["📈 Engagement tracking across ",Object.keys(d.departmentStats||{}).length," departments"]})})]}),u.jsx("div",{className:"department-grid",children:Object.entries(d.departmentStats||{}).map(([H,U])=>u.jsxs("div",{className:"department-card",children:[u.jsxs("div",{className:"dept-header",children:[u.jsx("h4",{children:H}),u.jsx("div",{className:"dept-stats-summary",children:u.jsxs("span",{className:"books-count",children:[U.booksCount," books"]})})]}),u.jsxs("div",{className:"dept-metrics",children:[u.jsxs("div",{className:"metric",children:[u.jsx("span",{className:"metric-label",children:"Total Staff:"}),u.jsx("span",{className:"metric-value",children:U.total})]}),u.jsxs("div",{className:"metric",children:[u.jsx("span",{className:"metric-label",children:"Active Readers:"}),u.jsx("span",{className:"metric-value",children:U.withBooks})]}),u.jsxs("div",{className:"metric",children:[u.jsx("span",{className:"metric-label",children:"Engagement:"}),u.jsxs("span",{className:"metric-value",children:[Math.round(U.withBooks/U.total*100),"%"]})]})]}),u.jsx("div",{className:"dept-progress",children:u.jsx("div",{className:"progress-bar",children:u.jsx("div",{className:"progress-fill dept",style:{width:`${U.withBooks/U.total*100}%`}})})})]},H))})]}),u.jsxs("div",{className:"popular-books-section",children:[u.jsxs("div",{className:"section-header",children:[u.jsx("h3",{children:"🔥 Most Popular Books"}),u.jsx("div",{className:"popularity-summary",children:u.jsx("span",{children:"📊 Based on borrowing frequency"})})]}),u.jsx("div",{className:"popular-books-grid",children:(M=d.popularBooks)==null?void 0:M.slice(0,6).map((H,U)=>{var z;return u.jsxs("div",{className:"popular-book-card",children:[u.jsxs("div",{className:"book-rank",children:["#",U+1]}),u.jsxs("div",{className:"book-info",children:[u.jsx("h5",{children:H.title}),u.jsx("p",{children:H.author}),u.jsxs("div",{className:"book-meta",children:[u.jsx("span",{className:`language-tag ${(z=H.language)==null?void 0:z.toLowerCase()}`,children:H.language==="Tamil"?"🇮🇳 தமிழ்":"🇬🇧 English"}),u.jsxs("span",{className:"times-taken",children:["📚 ",H.times_taken," times"]})]})]})]},H.id)})})]})]})},L=()=>u.jsxs("div",{className:"admin-books",children:[u.jsxs("div",{className:"books-header",children:[u.jsx("h3",{children:"📚 Book Management"}),u.jsx("div",{className:"book-filters",children:u.jsxs("select",{value:m,onChange:M=>y(M.target.value),className:"language-select",children:[u.jsx("option",{value:"all",children:"All Languages"}),u.jsx("option",{value:"English",children:"English Books"}),u.jsx("option",{value:"Tamil",children:"Tamil Books"})]})})]}),u.jsx("div",{className:"books-table",children:u.jsxs("table",{children:[u.jsx("thead",{children:u.jsxs("tr",{children:[u.jsx("th",{children:"Book ID"}),u.jsx("th",{children:"Title"}),u.jsx("th",{children:"Author"}),u.jsx("th",{children:"Language"}),u.jsx("th",{children:"Status"}),u.jsx("th",{children:"Taken By"}),u.jsx("th",{children:"Due Date"}),u.jsx("th",{children:"Actions"})]})}),u.jsx("tbody",{children:i.filter(M=>m==="all"||M.language===m).map(M=>{var H;return u.jsxs("tr",{children:[u.jsx("td",{children:M.book_no}),u.jsx("td",{className:"book-title",children:M.title}),u.jsx("td",{children:M.author}),u.jsx("td",{children:u.jsx("span",{className:`language-badge ${(H=M.language)==null?void 0:H.toLowerCase()}`,children:M.language==="Tamil"?"🇮🇳 தமிழ்":"🇬🇧 English"})}),u.jsx("td",{children:u.jsx("span",{className:`status-badge ${M.status}`,children:M.status})}),u.jsx("td",{children:M.taken_by_name||"-"}),u.jsx("td",{children:M.due_date||"-"}),u.jsxs("td",{children:[u.jsx("button",{className:"action-btn edit",children:"✏️ Edit"}),u.jsx("button",{className:"action-btn delete",children:"🗑️ Delete"})]})]},M.id)})})]})})]}),R=()=>u.jsxs("div",{className:"admin-employees",children:[u.jsxs("div",{className:"employees-header",children:[u.jsx("h3",{children:"👥 Employee Management"}),u.jsxs("div",{className:"employees-summary",children:[u.jsxs("span",{children:["📊 ",o.length," Total Employees"]}),u.jsxs("span",{children:["📚 ",d.employeesWithBooks," Currently Reading"]})]})]}),u.jsx("div",{className:"employees-table",children:u.jsxs("table",{children:[u.jsx("thead",{children:u.jsxs("tr",{children:[u.jsx("th",{children:"Employee ID"}),u.jsx("th",{children:"Name"}),u.jsx("th",{children:"Department"}),u.jsx("th",{children:"Email"}),u.jsx("th",{children:"Books Taken"}),u.jsx("th",{children:"Status"}),u.jsx("th",{children:"Actions"})]})}),u.jsx("tbody",{children:o.map(M=>{const H=i.filter(z=>z.status==="taken"&&z.taken_by_employee_id===M.employee_id),U=H.some(z=>z.status==="overdue");return u.jsxs("tr",{children:[u.jsx("td",{children:M.employee_id}),u.jsx("td",{className:"employee-name-cell",children:u.jsx("strong",{children:M.name})}),u.jsx("td",{children:M.department}),u.jsx("td",{children:M.email}),u.jsx("td",{children:u.jsxs("div",{className:"books-taken-info",children:[u.jsx("span",{className:"books-count",children:H.length}),H.length>0&&u.jsx("div",{className:"books-details",children:H.map(z=>u.jsxs("div",{className:"book-item",children:[u.jsx("span",{className:"book-title",children:z.title}),u.jsxs("span",{className:`due-date ${U?"overdue":""}`,children:["Due: ",z.due_date]})]},z.id))})]})}),u.jsx("td",{children:u.jsx("span",{className:`employee-status ${H.length>0?"active":"inactive"} ${U?"overdue":""}`,children:U?"⚠️ Overdue":H.length>0?"📚 Reading":"✅ Available"})}),u.jsxs("td",{children:[u.jsx("button",{className:"action-btn view",onClick:()=>{g(M),b(!0)},children:"👁️ View"}),u.jsx("button",{className:"action-btn edit",children:"✏️ Edit"})]})]},M.employee_id)})})]})})]}),I=()=>u.jsxs("div",{className:"admin-feedback",children:[u.jsxs("div",{className:"feedback-header",children:[u.jsx("h3",{children:"💬 Employee Feedback"}),u.jsx("div",{className:"feedback-summary",children:u.jsxs("span",{children:["📝 ",l.length," Total Feedback"]})})]}),u.jsx("div",{className:"feedback-list",children:l.length===0?u.jsxs("div",{className:"no-feedback",children:[u.jsx("div",{className:"no-feedback-icon",children:"📝"}),u.jsx("h4",{children:"No Feedback Yet"}),u.jsx("p",{children:"Employee feedback will appear here when submitted"})]}):l.map((M,H)=>u.jsxs("div",{className:"feedback-item",children:[u.jsxs("div",{className:"feedback-header",children:[u.jsxs("div",{className:"feedback-employee",children:[u.jsx("span",{className:"employee-name",children:M.employee_name}),u.jsxs("span",{className:"employee-id",children:["ID: ",M.employee_id]})]}),u.jsx("span",{className:"feedback-date",children:M.date})]}),u.jsx("div",{className:"feedback-content",children:u.jsx("p",{children:M.message})}),u.jsx("div",{className:"feedback-type",children:u.jsx("span",{className:`feedback-tag ${M.type}`,children:M.type==="suggestion"?"💡 Suggestion":M.type==="complaint"?"⚠️ Complaint":"💬 General"})}),u.jsxs("div",{className:"feedback-actions",children:[u.jsx("button",{className:"action-btn reply",children:"💬 Reply"}),u.jsx("button",{className:"action-btn mark-read",children:"✅ Mark Read"}),u.jsx("button",{className:"action-btn priority",children:"⭐ Priority"})]})]},H))})]});return u.jsxs("div",{className:"enhanced-admin-dashboard",children:[u.jsxs("div",{className:"admin-header",children:[u.jsxs("div",{className:"admin-info",children:[u.jsx("div",{className:"admin-avatar",children:"👨‍💼"}),u.jsxs("div",{className:"admin-details",children:[u.jsx("h2",{children:t.name}),u.jsx("p",{children:"System Administrator"})]})]}),u.jsx("button",{onClick:e,className:"logout-btn",children:"🚪 Logout"})]}),u.jsxs("div",{className:"admin-nav",children:[u.jsx("button",{className:`nav-tab ${n==="overview"?"active":""}`,onClick:()=>s("overview"),children:"📊 Overview"}),u.jsx("button",{className:`nav-tab ${n==="books"?"active":""}`,onClick:()=>s("books"),children:"📚 Books"}),u.jsx("button",{className:`nav-tab ${n==="employees"?"active":""}`,onClick:()=>s("employees"),children:"👥 Employees"}),u.jsx("button",{className:`nav-tab ${n==="feedback"?"active":""}`,onClick:()=>s("feedback"),children:"💬 Feedback"})]}),u.jsx("div",{className:"admin-content",children:f?u.jsxs("div",{className:"loading",children:[u.jsx("div",{className:"loading-spinner"}),u.jsx("h3",{children:"🔄 Loading Dashboard Data..."}),u.jsx("p",{children:"Fetching real-time library analytics"})]}):u.jsxs(u.Fragment,{children:[n==="overview"&&S(),n==="books"&&L(),n==="employees"&&R(),n==="feedback"&&I()]})})]})};const ob=({employee:t,onClose:e})=>{const[n,s]=P.useState("general"),[i,r]=P.useState(""),[o,a]=P.useState(""),[l,c]=P.useState({title:"",author:"",genre:"",priority:"medium"}),[d,h]=P.useState(!1),f=async p=>{if(p.preventDefault(),!o.trim()){alert("Please enter your feedback message");return}h(!0);try{const y=await(await fetch("http://localhost:5000/submit-feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({employee_name:t.name,employee_id:t.employee_id,type:n,title:i,message:o,book_details:n==="book_request"?l:{}})})).json();y.status==="success"?(alert("Feedback submitted successfully! Admin will review and respond soon."),r(""),a(""),c({title:"",author:"",genre:"",priority:"medium"}),s("general"),e&&e()):alert(y.message)}catch(m){console.error("Error submitting feedback:",m),alert("Failed to submit feedback. Please try again.")}finally{h(!1)}};return u.jsx("div",{className:"feedback-overlay",children:u.jsxs("div",{className:"feedback-form",children:[u.jsxs("div",{className:"feedback-header",children:[u.jsx("h2",{children:"💬 Submit Feedback"}),u.jsx("button",{className:"close-btn",onClick:e,children:"✕"})]}),u.jsxs("form",{onSubmit:f,children:[u.jsxs("div",{className:"form-group",children:[u.jsx("label",{children:"Feedback Type:"}),u.jsxs("select",{value:n,onChange:p=>s(p.target.value),className:"form-select",children:[u.jsx("option",{value:"general",children:"General Feedback"}),u.jsx("option",{value:"book_request",children:"Book Purchase Request"}),u.jsx("option",{value:"complaint",children:"Complaint"}),u.jsx("option",{value:"suggestion",children:"Suggestion"})]})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{children:"Title (Optional):"}),u.jsx("input",{type:"text",value:i,onChange:p=>r(p.target.value),placeholder:"Brief title for your feedback",className:"form-input"})]}),n==="book_request"&&u.jsxs("div",{className:"book-request-section",children:[u.jsx("h3",{children:"📚 Book Request Details"}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{children:"Book Title: *"}),u.jsx("input",{type:"text",value:l.title,onChange:p=>c({...l,title:p.target.value}),placeholder:"Enter book title",className:"form-input",required:!0})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{children:"Author:"}),u.jsx("input",{type:"text",value:l.author,onChange:p=>c({...l,author:p.target.value}),placeholder:"Enter author name",className:"form-input"})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{children:"Genre:"}),u.jsxs("select",{value:l.genre,onChange:p=>c({...l,genre:p.target.value}),className:"form-select",children:[u.jsx("option",{value:"",children:"Select genre"}),u.jsx("option",{value:"Fiction",children:"Fiction"}),u.jsx("option",{value:"Non-Fiction",children:"Non-Fiction"}),u.jsx("option",{value:"Science",children:"Science"}),u.jsx("option",{value:"Technology",children:"Technology"}),u.jsx("option",{value:"Business",children:"Business"}),u.jsx("option",{value:"Self Help",children:"Self Help"}),u.jsx("option",{value:"Biography",children:"Biography"}),u.jsx("option",{value:"History",children:"History"}),u.jsx("option",{value:"Cooking",children:"Cooking"}),u.jsx("option",{value:"Health",children:"Health"}),u.jsx("option",{value:"Finance",children:"Finance"}),u.jsx("option",{value:"Other",children:"Other"})]})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{children:"Priority:"}),u.jsxs("select",{value:l.priority,onChange:p=>c({...l,priority:p.target.value}),className:"form-select",children:[u.jsx("option",{value:"low",children:"Low - Nice to have"}),u.jsx("option",{value:"medium",children:"Medium - Would be useful"}),u.jsx("option",{value:"high",children:"High - Really needed"})]})]})]}),u.jsxs("div",{className:"form-group",children:[u.jsxs("label",{children:[n==="book_request"?"Why do you need this book?":"Your Message:"," *"]}),u.jsx("textarea",{value:o,onChange:p=>a(p.target.value),placeholder:n==="book_request"?"Explain why this book would be valuable for the library and how it would help you or other employees...":"Share your feedback, suggestions, or concerns...",className:"form-textarea",rows:"5",required:!0})]}),u.jsxs("div",{className:"form-actions",children:[u.jsx("button",{type:"button",onClick:e,className:"btn btn-secondary",children:"Cancel"}),u.jsx("button",{type:"submit",className:"btn btn-primary",disabled:d,children:d?"Submitting...":"Submit Feedback"})]})]}),u.jsxs("div",{className:"feedback-info",children:[u.jsx("h4",{children:"📋 Feedback Guidelines:"}),u.jsxs("ul",{children:[u.jsxs("li",{children:[u.jsx("strong",{children:"General Feedback:"})," Share your thoughts about the library system"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Book Requests:"})," Suggest books you'd like to see in our library"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Complaints:"})," Report any issues or problems"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Suggestions:"})," Ideas for improving the library"]})]}),u.jsx("p",{children:u.jsx("em",{children:"All feedback is reviewed by the admin team and you'll receive a response soon!"})})]})]})})},ab=({employee:t,onLogout:e})=>{const[n,s]=P.useState(""),[i,r]=P.useState([]),[o,a]=P.useState([]),[l,c]=P.useState([]),[d,h]=P.useState(!1),[f,p]=P.useState("all"),[m,y]=P.useState([]),[v,g]=P.useState(!1),[x,b]=P.useState(""),[k,_]=P.useState(""),[C,N]=P.useState(!1),[S,L]=P.useState({totalTaken:0,dueForRenewal:0,returned:0,monthlyLimit:2,currentlyHolding:0});P.useEffect(()=>{I(),R()},[]);const R=async(j="all")=>{try{const O=j==="all"?"http://localhost:5000/books":`http://localhost:5000/books?language=${j}`,W=await fetch(O);if(W.ok){const $=await W.json();a($.books||[])}}catch(O){console.error("Error fetching all books:",O)}},I=async()=>{try{const j=await fetch(`http://localhost:5000/employee/${t.id}/books`);if(j.ok){const O=await j.json();y(O.books||[]),M(O.books||[])}}catch(j){console.error("Error fetching my books:",j)}},M=j=>{const O=new Date,W=O.getMonth(),$=O.getFullYear();let Q=0;const re=j.filter(A=>A.status==="taken").length;j.forEach(A=>{if(A.taken_date&&A.status==="taken"){const V=new Date(A.taken_date);V.getMonth()===W&&V.getFullYear()===$&&Q++}A.monthly_history&&Array.isArray(A.monthly_history)&&A.monthly_history.forEach(V=>{V.month===W+1&&V.year===$&&Q++})});const ve=j.filter(A=>{if(!A.due_date||A.status!=="taken")return!1;const V=new Date(A.due_date),te=Math.ceil((V-O)/(1e3*60*60*24));return te<=3&&te>=0}).length,w=j.filter(A=>A.status==="available"&&(A.return_date||A.last_taken_by_employee_id===t.employee_id)).length;L({totalTaken:Q,dueForRenewal:ve,returned:w,monthlyLimit:2,currentlyHolding:re})},H=j=>{if(s(j),!j.trim()){c([]),h(!1),r([]);return}const O=j.toLowerCase().trim(),W=o.filter($=>{const Q=$.title.toLowerCase().includes(O),re=$.author.toLowerCase().includes(O),ve=$.book_no.toLowerCase().includes(O),w=$.genre.toLowerCase().includes(O);return Q||re||ve||w}).sort(($,Q)=>{const re=$.title.toLowerCase()===O||$.author.toLowerCase()===O,ve=Q.title.toLowerCase()===O||Q.author.toLowerCase()===O,w=$.title.toLowerCase().startsWith(O)||$.author.toLowerCase().startsWith(O),A=Q.title.toLowerCase().startsWith(O)||Q.author.toLowerCase().startsWith(O);return re&&!ve?-1:!re&&ve?1:w&&!A?-1:!w&&A?1:$.title.localeCompare(Q.title)}).slice(0,8);c(W),h(j.length>=2)},U=j=>{s(j.title),r([j]),h(!1)},z=async()=>{if(!n.trim()){r([]);return}g(!0);try{const j=`http://localhost:5000/books/search?q=${encodeURIComponent(n)}&language=${f}`,O=await fetch(j);if(O.ok){const W=await O.json();r(W.books||[]),h(!1)}else b("Failed to search books")}catch{b("Error searching books")}finally{g(!1)}},K=async j=>{var O;if(S.currentlyHolding>=2){b("You can only hold 2 books at a time. Please return a book first.");return}if(S.totalTaken>=S.monthlyLimit){b("You have reached your monthly limit of 2 books. Please wait for next month.");return}g(!0);try{const W=await fetch("http://localhost:5000/take-book",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({book_id:j,employee_id:t.id,employee_name:t.name})});if(W.ok){const $=await W.json();_(`📚 Book taken successfully! Due date: ${((O=$.book)==null?void 0:O.due_date)||"N/A"}`),I(),z(),setTimeout(()=>_(""),5e3)}else{const $=await W.json();b($.message||"Failed to take book")}}catch{b("Error taking book")}finally{g(!1)}},ee=async j=>{g(!0);try{const O=await fetch("http://localhost:5000/return-book",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({book_id:j,employee_id:t.id})});if(O.ok)_("Book returned successfully!"),I(),setTimeout(()=>_(""),3e3);else{const W=await O.json();b(W.message||"Failed to return book")}}catch{b("Error returning book")}finally{g(!1)}},D=async j=>{g(!0);try{const O=await fetch("http://localhost:5000/renew-book",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({book_id:j,employee_id:t.id})});if(O.ok)_("Book renewed successfully!"),I(),setTimeout(()=>_(""),3e3);else{const W=await O.json();b(W.message||"Failed to renew book")}}catch{b("Error renewing book")}finally{g(!1)}};return u.jsxs("div",{className:"advanced-employee-dashboard",children:[u.jsx("div",{className:"dashboard-header",children:u.jsxs("div",{className:"employee-info-card",children:[u.jsx("div",{className:"employee-avatar",children:t.name.charAt(0).toUpperCase()}),u.jsxs("div",{className:"employee-details",children:[u.jsx("h2",{children:t.name}),u.jsx("div",{className:"employee-table-container",children:u.jsx("table",{className:"employee-info-table",children:u.jsxs("tbody",{children:[u.jsxs("tr",{children:[u.jsx("td",{className:"table-label",children:"Employee Name:"}),u.jsx("td",{className:"table-value",children:t.name})]}),u.jsxs("tr",{children:[u.jsx("td",{className:"table-label",children:"Employee ID:"}),u.jsx("td",{className:"table-value",children:t.employee_id})]}),u.jsxs("tr",{children:[u.jsx("td",{className:"table-label",children:"Mobile Number:"}),u.jsx("td",{className:"table-value",children:t.phone})]}),u.jsxs("tr",{children:[u.jsx("td",{className:"table-label",children:"Department:"}),u.jsx("td",{className:"table-value",children:t.department})]})]})})})]}),u.jsxs("div",{className:"header-actions",children:[u.jsxs("button",{className:"feedback-btn",onClick:()=>N(!0),children:[u.jsx("span",{children:"💬"}),u.jsx("span",{children:"Feedback"})]}),u.jsxs("button",{className:"logout-btn",onClick:e,children:[u.jsx("span",{children:"Sign Out"}),u.jsx("span",{className:"logout-icon",children:"⏻"})]})]})]})}),u.jsxs("div",{className:"stats-dashboard",children:[u.jsxs("div",{className:"stats-header",children:[u.jsx("h3",{children:"📊 Library Usage Statistics"}),u.jsx("p",{children:"Real-time tracking of your book borrowing activity"})]}),u.jsxs("div",{className:"book-stats-grid",children:[u.jsxs("div",{className:"stat-card currently-holding priority",children:[u.jsxs("div",{className:"stat-header",children:[u.jsx("div",{className:"stat-icon",children:"📚"}),u.jsx("div",{className:"stat-title",children:"Currently Holding"})]}),u.jsxs("div",{className:"stat-content",children:[u.jsxs("div",{className:"stat-number-large",children:[S.currentlyHolding,u.jsx("span",{className:"stat-unit",children:"/2"})]}),u.jsxs("div",{className:"stat-progress",children:[u.jsx("div",{className:"progress-bar professional",children:u.jsx("div",{className:"progress-fill blue-primary",style:{width:`${S.currentlyHolding/2*100}%`}})}),u.jsx("div",{className:"progress-label",children:"Books in possession"})]}),u.jsx("div",{className:"stat-status",children:S.currentlyHolding>=2?u.jsx("span",{className:"status-warning",children:"⚠️ Maximum capacity reached"}):u.jsxs("span",{className:"status-available",children:["✅ ",2-S.currentlyHolding," slot",2-S.currentlyHolding!==1?"s":""," available"]})})]})]}),u.jsxs("div",{className:"stat-card monthly-limit priority",children:[u.jsxs("div",{className:"stat-header",children:[u.jsx("div",{className:"stat-icon",children:"📖"}),u.jsx("div",{className:"stat-title",children:"Monthly Usage"})]}),u.jsxs("div",{className:"stat-content",children:[u.jsxs("div",{className:"stat-number-large",children:[S.totalTaken,u.jsx("span",{className:"stat-unit",children:"/2"})]}),u.jsxs("div",{className:"stat-progress",children:[u.jsx("div",{className:"progress-bar professional",children:u.jsx("div",{className:"progress-fill blue-secondary",style:{width:`${S.totalTaken/S.monthlyLimit*100}%`}})}),u.jsx("div",{className:"progress-label",children:"Books taken this month"})]}),u.jsx("div",{className:"stat-status",children:S.totalTaken>=S.monthlyLimit?u.jsx("span",{className:"status-warning",children:"⚠️ Monthly limit reached"}):u.jsxs("span",{className:"status-available",children:["✅ ",S.monthlyLimit-S.totalTaken," more book",S.monthlyLimit-S.totalTaken!==1?"s":""," this month"]})})]})]}),u.jsxs("div",{className:"stat-card due-renewal",children:[u.jsxs("div",{className:"stat-header",children:[u.jsx("div",{className:"stat-icon",children:"🔄"}),u.jsx("div",{className:"stat-title",children:"Due for Renewal"})]}),u.jsxs("div",{className:"stat-content",children:[u.jsx("div",{className:"stat-number-medium",children:S.dueForRenewal}),u.jsx("div",{className:"stat-description",children:S.dueForRenewal>0?u.jsxs("span",{className:"status-warning",children:["📅 ",S.dueForRenewal," book",S.dueForRenewal!==1?"s":""," due soon"]}):u.jsx("span",{className:"status-success",children:"✅ All books current"})})]})]}),u.jsxs("div",{className:"stat-card books-returned",children:[u.jsxs("div",{className:"stat-header",children:[u.jsx("div",{className:"stat-icon",children:"✅"}),u.jsx("div",{className:"stat-title",children:"Total Returns"})]}),u.jsxs("div",{className:"stat-content",children:[u.jsx("div",{className:"stat-number-medium",children:S.returned}),u.jsx("div",{className:"stat-description",children:u.jsx("span",{className:"status-info",children:"📚 Lifetime returns"})})]})]})]})]}),u.jsxs("div",{className:"search-section",children:[u.jsxs("div",{className:"search-header",children:[u.jsx("h3",{children:"🔍 Find Your Perfect Book"}),u.jsx("p",{children:"Search in English and Tamil books with smart suggestions"}),u.jsx("div",{className:"search-tips",children:u.jsx("span",{className:"tip",children:"💡 Try: Book title, Author name, or Book ID"})})]}),u.jsx("div",{className:"language-filter",children:u.jsxs("div",{className:"filter-buttons",children:[u.jsx("button",{className:`filter-btn ${f==="all"?"active":""}`,onClick:()=>{p("all"),R("all"),r([]),s("")},children:"🌐 All Books"}),u.jsx("button",{className:`filter-btn ${f==="english"?"active":""}`,onClick:()=>{p("english"),R("english"),r([]),s("")},children:"🇬🇧 English"}),u.jsx("button",{className:`filter-btn ${f==="tamil"?"active":""}`,onClick:()=>{p("tamil"),R("tamil"),r([]),s("")},children:"🇮🇳 தமிழ்"})]})}),u.jsxs("div",{className:"search-container",children:[u.jsxs("div",{className:"search-bar",children:[u.jsx("input",{type:"text",placeholder:f==="tamil"?"🔍 தலைப்பு, ஆசிரியர் அல்லது புத்தக எண்...":"🔍 Search by title, author, or book ID...",value:n,onChange:j=>H(j.target.value),onKeyDown:j=>j.key==="Enter"&&z(),className:"search-input enhanced",onFocus:()=>n&&h(l.length>0),onBlur:()=>setTimeout(()=>h(!1),200)}),u.jsx("button",{onClick:z,className:"search-btn",disabled:v,children:v?"🔄":"🔍"})]}),d&&u.jsxs("div",{className:"search-dropdown",children:[u.jsx("div",{className:"dropdown-header",children:u.jsxs("span",{children:["📚 Found ",l.length," books"]})}),l.map(j=>u.jsx("div",{className:"dropdown-item",onClick:()=>U(j),children:u.jsxs("div",{className:"dropdown-book-info",children:[u.jsx("div",{className:"dropdown-title",children:j.title}),u.jsxs("div",{className:"dropdown-details",children:[u.jsxs("span",{className:"dropdown-author",children:["by ",j.author]}),u.jsxs("span",{className:"dropdown-id",children:["ID: ",j.book_no]}),u.jsx("span",{className:`dropdown-status ${j.status}`,children:j.status==="available"?"✅ Available":"❌ Taken"})]})]})},j.id))]})]})]}),x&&u.jsxs("div",{className:"message error-message",children:[u.jsx("span",{className:"message-icon",children:"⚠️"}),u.jsx("span",{children:x}),u.jsx("button",{onClick:()=>b(""),className:"close-btn",children:"×"})]}),k&&u.jsxs("div",{className:"message success-message",children:[u.jsx("span",{className:"message-icon",children:"✅"}),u.jsx("span",{children:k}),u.jsx("button",{onClick:()=>_(""),className:"close-btn",children:"×"})]}),i.length>0&&u.jsxs("div",{className:"search-results",children:[u.jsxs("h4",{children:["Search Results (",i.length," books found)"]}),u.jsx("div",{className:"books-grid",children:i.map(j=>u.jsxs("div",{className:"book-card",children:[u.jsxs("div",{className:"book-header",children:[u.jsx("h5",{children:j.title}),u.jsx("span",{className:`book-status ${j.status}`,children:j.status==="available"?"✅ Available":"❌ Taken"})]}),u.jsxs("div",{className:"book-details",children:[u.jsxs("p",{children:[u.jsx("strong",{children:"Author:"})," ",j.author]}),u.jsxs("p",{children:[u.jsx("strong",{children:"Genre:"})," ",j.genre]}),u.jsxs("p",{children:[u.jsx("strong",{children:"Book ID:"})," ",j.book_no]}),u.jsxs("p",{children:[u.jsx("strong",{children:"Rack:"})," ",j.rack_no]}),j.status==="taken"&&j.taken_by_name&&u.jsxs("p",{children:[u.jsx("strong",{children:"Taken by:"})," ",j.taken_by_name]})]}),j.status==="available"&&u.jsx("button",{onClick:()=>K(j.id),className:"take-book-btn",disabled:v||S.currentlyHolding>=2||S.totalTaken>=S.monthlyLimit,children:"Take Book"})]},j.id))})]}),m.length>0&&u.jsxs("div",{className:"my-books-section",children:[u.jsxs("h4",{children:["My Books History (",m.length,")"]}),u.jsx("div",{className:"books-grid",children:m.map(j=>u.jsxs("div",{className:`book-card my-book ${j.status}`,children:[u.jsxs("div",{className:"book-header",children:[u.jsx("h5",{children:j.title}),u.jsx("span",{className:`book-status ${j.status}`,children:j.status==="taken"?"📖 Currently Borrowed":"✅ Previously Returned"})]}),u.jsxs("div",{className:"book-details",children:[u.jsxs("p",{children:[u.jsx("strong",{children:"Author:"})," ",j.author]}),u.jsxs("p",{children:[u.jsx("strong",{children:"Book ID:"})," ",j.book_no]}),j.taken_date&&u.jsxs("p",{children:[u.jsx("strong",{children:"Last Taken:"})," ",new Date(j.taken_date).toLocaleDateString()]}),j.return_date&&j.status!=="taken"&&u.jsxs("p",{children:[u.jsx("strong",{children:"Returned:"})," ",new Date(j.return_date).toLocaleDateString()]}),j.due_date&&j.status==="taken"&&u.jsxs("div",{className:"due-date-info",children:[u.jsxs("p",{children:[u.jsx("strong",{children:"Due:"})," ",new Date(j.due_date).toLocaleDateString()]}),u.jsx("div",{className:"days-remaining",children:(()=>{const $=new Date(j.due_date)-new Date,Q=Math.ceil($/(1e3*60*60*24));return Q<0?u.jsxs("span",{className:"overdue",children:["⚠️ ",Math.abs(Q)," days overdue"]}):Q<=3?u.jsxs("span",{className:"due-soon",children:["🔔 Due in ",Q," days"]}):u.jsxs("span",{className:"due-later",children:["📅 ",Q," days remaining"]})})()})]})]}),j.status==="taken"?u.jsxs("div",{className:"book-actions",children:[u.jsx("button",{onClick:()=>ee(j.id),className:"return-book-btn",disabled:v,children:"Return Book"}),u.jsx("button",{onClick:()=>D(j.id),className:"renew-book-btn",disabled:v,children:"Renew"})]}):u.jsxs("div",{className:"book-actions",children:[u.jsx("button",{onClick:()=>K(j.id),className:"retake-book-btn",disabled:v||S.currentlyHolding>=2||S.totalTaken>=S.monthlyLimit,children:"Take Again"}),u.jsx("div",{className:"retake-info",children:u.jsx("span",{children:"📚 You can borrow this book again!"})})]})]},j.id))})]}),C&&u.jsx("div",{className:"feedback-modal-overlay",children:u.jsxs("div",{className:"feedback-modal",children:[u.jsxs("div",{className:"feedback-header",children:[u.jsx("h3",{children:"Submit Feedback"}),u.jsx("button",{className:"close-feedback-btn",onClick:()=>N(!1),children:"×"})]}),u.jsx(ob,{employee:t,onClose:()=>N(!1),onSuccess:j=>{_(j),N(!1),setTimeout(()=>_(""),3e3)}})]})})]})},lb=()=>{const[t,e]=P.useState(null),n=xa();P.useEffect(()=>{const r=localStorage.getItem("employee");if(!r){n("/login");return}try{const o=JSON.parse(r);e(o)}catch{n("/login")}},[n]);const s=()=>{localStorage.removeItem("employee"),n("/login")};return t?t&&(t.role==="admin"||t.name==="System Administrator"||t.employee_id==="ADMIN001")?u.jsx(rb,{employee:t,onLogout:s}):u.jsx(ab,{employee:t,onLogout:s}):u.jsx("div",{className:"loading",children:u.jsx("div",{className:"spinner"})})};/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function dr(t){return t+.5|0}const cn=(t,e,n)=>Math.max(Math.min(t,n),e);function hi(t){return cn(dr(t*2.55),0,255)}function _n(t){return cn(dr(t*255),0,255)}function $t(t){return cn(dr(t/2.55)/100,0,1)}function ph(t){return cn(dr(t*100),0,100)}const lt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},mc=[..."0123456789ABCDEF"],cb=t=>mc[t&15],ub=t=>mc[(t&240)>>4]+mc[t&15],Rr=t=>(t&240)>>4===(t&15),db=t=>Rr(t.r)&&Rr(t.g)&&Rr(t.b)&&Rr(t.a);function hb(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&lt[t[1]]*17,g:255&lt[t[2]]*17,b:255&lt[t[3]]*17,a:e===5?lt[t[4]]*17:255}:(e===7||e===9)&&(n={r:lt[t[1]]<<4|lt[t[2]],g:lt[t[3]]<<4|lt[t[4]],b:lt[t[5]]<<4|lt[t[6]],a:e===9?lt[t[7]]<<4|lt[t[8]]:255})),n}const fb=(t,e)=>t<255?e(t):"";function pb(t){var e=db(t)?cb:ub;return t?"#"+e(t.r)+e(t.g)+e(t.b)+fb(t.a,e):void 0}const mb=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function mg(t,e,n){const s=e*Math.min(n,1-n),i=(r,o=(r+t/30)%12)=>n-s*Math.max(Math.min(o-3,9-o,1),-1);return[i(0),i(8),i(4)]}function gb(t,e,n){const s=(i,r=(i+t/60)%6)=>n-n*e*Math.max(Math.min(r,4-r,1),0);return[s(5),s(3),s(1)]}function yb(t,e,n){const s=mg(t,1,.5);let i;for(e+n>1&&(i=1/(e+n),e*=i,n*=i),i=0;i<3;i++)s[i]*=1-e-n,s[i]+=e;return s}function xb(t,e,n,s,i){return t===i?(e-n)/s+(e<n?6:0):e===i?(n-t)/s+2:(t-e)/s+4}function Nu(t){const n=t.r/255,s=t.g/255,i=t.b/255,r=Math.max(n,s,i),o=Math.min(n,s,i),a=(r+o)/2;let l,c,d;return r!==o&&(d=r-o,c=a>.5?d/(2-r-o):d/(r+o),l=xb(n,s,i,d,r),l=l*60+.5),[l|0,c||0,a]}function Eu(t,e,n,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,s)).map(_n)}function Pu(t,e,n){return Eu(mg,t,e,n)}function vb(t,e,n){return Eu(yb,t,e,n)}function bb(t,e,n){return Eu(gb,t,e,n)}function gg(t){return(t%360+360)%360}function _b(t){const e=mb.exec(t);let n=255,s;if(!e)return;e[5]!==s&&(n=e[6]?hi(+e[5]):_n(+e[5]));const i=gg(+e[2]),r=+e[3]/100,o=+e[4]/100;return e[1]==="hwb"?s=vb(i,r,o):e[1]==="hsv"?s=bb(i,r,o):s=Pu(i,r,o),{r:s[0],g:s[1],b:s[2],a:n}}function kb(t,e){var n=Nu(t);n[0]=gg(n[0]+e),n=Pu(n),t.r=n[0],t.g=n[1],t.b=n[2]}function wb(t){if(!t)return;const e=Nu(t),n=e[0],s=ph(e[1]),i=ph(e[2]);return t.a<255?`hsla(${n}, ${s}%, ${i}%, ${$t(t.a)})`:`hsl(${n}, ${s}%, ${i}%)`}const mh={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},gh={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Sb(){const t={},e=Object.keys(gh),n=Object.keys(mh);let s,i,r,o,a;for(s=0;s<e.length;s++){for(o=a=e[s],i=0;i<n.length;i++)r=n[i],a=a.replace(r,mh[r]);r=parseInt(gh[o],16),t[a]=[r>>16&255,r>>8&255,r&255]}return t}let Or;function jb(t){Or||(Or=Sb(),Or.transparent=[0,0,0,0]);const e=Or[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const Cb=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Nb(t){const e=Cb.exec(t);let n=255,s,i,r;if(e){if(e[7]!==s){const o=+e[7];n=e[8]?hi(o):cn(o*255,0,255)}return s=+e[1],i=+e[3],r=+e[5],s=255&(e[2]?hi(s):cn(s,0,255)),i=255&(e[4]?hi(i):cn(i,0,255)),r=255&(e[6]?hi(r):cn(r,0,255)),{r:s,g:i,b:r,a:n}}}function Eb(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${$t(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const rl=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,cs=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function Pb(t,e,n){const s=cs($t(t.r)),i=cs($t(t.g)),r=cs($t(t.b));return{r:_n(rl(s+n*(cs($t(e.r))-s))),g:_n(rl(i+n*(cs($t(e.g))-i))),b:_n(rl(r+n*(cs($t(e.b))-r))),a:t.a+n*(e.a-t.a)}}function Dr(t,e,n){if(t){let s=Nu(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*n,e===0?360:1)),s=Pu(s),t.r=s[0],t.g=s[1],t.b=s[2]}}function yg(t,e){return t&&Object.assign(e||{},t)}function yh(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=_n(t[3]))):(e=yg(t,{r:0,g:0,b:0,a:1}),e.a=_n(e.a)),e}function Mb(t){return t.charAt(0)==="r"?Nb(t):_b(t)}class qi{constructor(e){if(e instanceof qi)return e;const n=typeof e;let s;n==="object"?s=yh(e):n==="string"&&(s=hb(e)||jb(e)||Mb(e)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var e=yg(this._rgb);return e&&(e.a=$t(e.a)),e}set rgb(e){this._rgb=yh(e)}rgbString(){return this._valid?Eb(this._rgb):void 0}hexString(){return this._valid?pb(this._rgb):void 0}hslString(){return this._valid?wb(this._rgb):void 0}mix(e,n){if(e){const s=this.rgb,i=e.rgb;let r;const o=n===r?.5:n,a=2*o-1,l=s.a-i.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;r=1-c,s.r=255&c*s.r+r*i.r+.5,s.g=255&c*s.g+r*i.g+.5,s.b=255&c*s.b+r*i.b+.5,s.a=o*s.a+(1-o)*i.a,this.rgb=s}return this}interpolate(e,n){return e&&(this._rgb=Pb(this._rgb,e._rgb,n)),this}clone(){return new qi(this.rgb)}alpha(e){return this._rgb.a=_n(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=dr(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return Dr(this._rgb,2,e),this}darken(e){return Dr(this._rgb,2,-e),this}saturate(e){return Dr(this._rgb,1,e),this}desaturate(e){return Dr(this._rgb,1,-e),this}rotate(e){return kb(this._rgb,e),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Ft(){}const Tb=(()=>{let t=0;return()=>t++})();function J(t){return t==null}function he(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function Z(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function Pe(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function tt(t,e){return Pe(t)?t:e}function X(t,e){return typeof t>"u"?e:t}const Rb=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,xg=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function oe(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function se(t,e,n,s){let i,r,o;if(he(t))if(r=t.length,s)for(i=r-1;i>=0;i--)e.call(n,t[i],i);else for(i=0;i<r;i++)e.call(n,t[i],i);else if(Z(t))for(o=Object.keys(t),r=o.length,i=0;i<r;i++)e.call(n,t[o[i]],o[i])}function zo(t,e){let n,s,i,r;if(!t||!e||t.length!==e.length)return!1;for(n=0,s=t.length;n<s;++n)if(i=t[n],r=e[n],i.datasetIndex!==r.datasetIndex||i.index!==r.index)return!1;return!0}function Wo(t){if(he(t))return t.map(Wo);if(Z(t)){const e=Object.create(null),n=Object.keys(t),s=n.length;let i=0;for(;i<s;++i)e[n[i]]=Wo(t[n[i]]);return e}return t}function vg(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function Ob(t,e,n,s){if(!vg(t))return;const i=e[t],r=n[t];Z(i)&&Z(r)?Qi(i,r,s):e[t]=Wo(r)}function Qi(t,e,n){const s=he(e)?e:[e],i=s.length;if(!Z(t))return t;n=n||{};const r=n.merger||Ob;let o;for(let a=0;a<i;++a){if(o=s[a],!Z(o))continue;const l=Object.keys(o);for(let c=0,d=l.length;c<d;++c)r(l[c],t,o,n)}return t}function ji(t,e){return Qi(t,e,{merger:Db})}function Db(t,e,n){if(!vg(t))return;const s=e[t],i=n[t];Z(s)&&Z(i)?ji(s,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=Wo(i))}const xh={"":t=>t,x:t=>t.x,y:t=>t.y};function Ab(t){const e=t.split("."),n=[];let s="";for(const i of e)s+=i,s.endsWith("\\")?s=s.slice(0,-1)+".":(n.push(s),s="");return n}function Lb(t){const e=Ab(t);return n=>{for(const s of e){if(s==="")break;n=n&&n[s]}return n}}function es(t,e){return(xh[e]||(xh[e]=Lb(e)))(t)}function Mu(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Gi=t=>typeof t<"u",Sn=t=>typeof t=="function",vh=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function Fb(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const ne=Math.PI,fe=2*ne,Bb=fe+ne,$o=Number.POSITIVE_INFINITY,Ib=ne/180,_e=ne/2,Pn=ne/4,bh=ne*2/3,un=Math.log10,Lt=Math.sign;function Ci(t,e,n){return Math.abs(t-e)<n}function _h(t){const e=Math.round(t);t=Ci(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(un(t))),s=t/n;return(s<=1?1:s<=2?2:s<=5?5:10)*n}function zb(t){const e=[],n=Math.sqrt(t);let s;for(s=1;s<n;s++)t%s===0&&(e.push(s),e.push(t/s));return n===(n|0)&&e.push(n),e.sort((i,r)=>i-r).pop(),e}function Wb(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Ji(t){return!Wb(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function $b(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function bg(t,e,n){let s,i,r;for(s=0,i=t.length;s<i;s++)r=t[s][n],isNaN(r)||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}function Ot(t){return t*(ne/180)}function Tu(t){return t*(180/ne)}function kh(t){if(!Pe(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function _g(t,e){const n=e.x-t.x,s=e.y-t.y,i=Math.sqrt(n*n+s*s);let r=Math.atan2(s,n);return r<-.5*ne&&(r+=fe),{angle:r,distance:i}}function gc(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Hb(t,e){return(t-e+Bb)%fe-ne}function Xe(t){return(t%fe+fe)%fe}function Zi(t,e,n,s){const i=Xe(t),r=Xe(e),o=Xe(n),a=Xe(r-i),l=Xe(o-i),c=Xe(i-r),d=Xe(i-o);return i===r||i===o||s&&r===o||a>l&&c<d}function Ie(t,e,n){return Math.max(e,Math.min(n,t))}function Ub(t){return Ie(t,-32768,32767)}function dn(t,e,n,s=1e-6){return t>=Math.min(e,n)-s&&t<=Math.max(e,n)+s}function Ru(t,e,n){n=n||(o=>t[o]<e);let s=t.length-1,i=0,r;for(;s-i>1;)r=i+s>>1,n(r)?i=r:s=r;return{lo:i,hi:s}}const Wn=(t,e,n,s)=>Ru(t,n,s?i=>{const r=t[i][e];return r<n||r===n&&t[i+1][e]===n}:i=>t[i][e]<n),Vb=(t,e,n)=>Ru(t,n,s=>t[s][e]>=n);function Yb(t,e,n){let s=0,i=t.length;for(;s<i&&t[s]<e;)s++;for(;i>s&&t[i-1]>n;)i--;return s>0||i<t.length?t.slice(s,i):t}const kg=["push","pop","shift","splice","unshift"];function Xb(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),kg.forEach(n=>{const s="_onData"+Mu(n),i=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...r){const o=i.apply(this,r);return t._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...r)}),o}})})}function wh(t,e){const n=t._chartjs;if(!n)return;const s=n.listeners,i=s.indexOf(e);i!==-1&&s.splice(i,1),!(s.length>0)&&(kg.forEach(r=>{delete t[r]}),delete t._chartjs)}function wg(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const Sg=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function jg(t,e){let n=[],s=!1;return function(...i){n=i,s||(s=!0,Sg.call(window,()=>{s=!1,t.apply(e,n)}))}}function Kb(t,e){let n;return function(...s){return e?(clearTimeout(n),n=setTimeout(t,e,s)):t.apply(this,s),e}}const Ou=t=>t==="start"?"left":t==="end"?"right":"center",Le=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,qb=(t,e,n,s)=>t===(s?"left":"right")?n:t==="center"?(e+n)/2:e;function Qb(t,e,n){const s=e.length;let i=0,r=s;if(t._sorted){const{iScale:o,vScale:a,_parsed:l}=t,c=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,d=o.axis,{min:h,max:f,minDefined:p,maxDefined:m}=o.getUserBounds();if(p){if(i=Math.min(Wn(l,d,h).lo,n?s:Wn(e,d,o.getPixelForValue(h)).lo),c){const y=l.slice(0,i+1).reverse().findIndex(v=>!J(v[a.axis]));i-=Math.max(0,y)}i=Ie(i,0,s-1)}if(m){let y=Math.max(Wn(l,o.axis,f,!0).hi+1,n?0:Wn(e,d,o.getPixelForValue(f),!0).hi+1);if(c){const v=l.slice(y-1).findIndex(g=>!J(g[a.axis]));y+=Math.max(0,v)}r=Ie(y,i,s)-i}else r=s-i}return{start:i,count:r}}function Gb(t){const{xScale:e,yScale:n,_scaleRanges:s}=t,i={xmin:e.min,xmax:e.max,ymin:n.min,ymax:n.max};if(!s)return t._scaleRanges=i,!0;const r=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==n.min||s.ymax!==n.max;return Object.assign(s,i),r}const Ar=t=>t===0||t===1,Sh=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*fe/n)),jh=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*fe/n)+1,Ni={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*_e)+1,easeOutSine:t=>Math.sin(t*_e),easeInOutSine:t=>-.5*(Math.cos(ne*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>Ar(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Ar(t)?t:Sh(t,.075,.3),easeOutElastic:t=>Ar(t)?t:jh(t,.075,.3),easeInOutElastic(t){return Ar(t)?t:t<.5?.5*Sh(t*2,.1125,.45):.5+.5*jh(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-Ni.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?Ni.easeInBounce(t*2)*.5:Ni.easeOutBounce(t*2-1)*.5+.5};function Du(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function Ch(t){return Du(t)?t:new qi(t)}function ol(t){return Du(t)?t:new qi(t).saturate(.5).darken(.1).hexString()}const Jb=["x","y","borderWidth","radius","tension"],Zb=["color","borderColor","backgroundColor"];function e1(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:Zb},numbers:{type:"number",properties:Jb}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function t1(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Nh=new Map;function n1(t,e){e=e||{};const n=t+JSON.stringify(e);let s=Nh.get(n);return s||(s=new Intl.NumberFormat(t,e),Nh.set(n,s)),s}function va(t,e,n){return n1(e,n).format(t)}const Cg={values(t){return he(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const s=this.chart.options.locale;let i,r=t;if(n.length>1){const c=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(c<1e-4||c>1e15)&&(i="scientific"),r=s1(t,n)}const o=un(Math.abs(r)),a=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:i,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),va(t,s,l)},logarithmic(t,e,n){if(t===0)return"0";const s=n[e].significand||t/Math.pow(10,Math.floor(un(t)));return[1,2,3,5,10,15].includes(s)||e>.8*n.length?Cg.numeric.call(this,t,e,n):""}};function s1(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var ba={formatters:Cg};function i1(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ba.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const ts=Object.create(null),yc=Object.create(null);function Ei(t,e){if(!e)return t;const n=e.split(".");for(let s=0,i=n.length;s<i;++s){const r=n[s];t=t[r]||(t[r]=Object.create(null))}return t}function al(t,e,n){return typeof e=="string"?Qi(Ei(t,e),n):Qi(Ei(t,""),e)}class r1{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,i)=>ol(i.backgroundColor),this.hoverBorderColor=(s,i)=>ol(i.borderColor),this.hoverColor=(s,i)=>ol(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return al(this,e,n)}get(e){return Ei(this,e)}describe(e,n){return al(yc,e,n)}override(e,n){return al(ts,e,n)}route(e,n,s,i){const r=Ei(this,e),o=Ei(this,s),a="_"+n;Object.defineProperties(r,{[a]:{value:r[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],c=o[i];return Z(l)?Object.assign({},c,l):X(l,c)},set(l){this[a]=l}}})}apply(e){e.forEach(n=>n(this))}}var xe=new r1({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[e1,t1,i1]);function o1(t){return!t||J(t.size)||J(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function Ho(t,e,n,s,i){let r=e[i];return r||(r=e[i]=t.measureText(i).width,n.push(i)),r>s&&(s=r),s}function a1(t,e,n,s){s=s||{};let i=s.data=s.data||{},r=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(i=s.data={},r=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let o=0;const a=n.length;let l,c,d,h,f;for(l=0;l<a;l++)if(h=n[l],h!=null&&!he(h))o=Ho(t,i,r,o,h);else if(he(h))for(c=0,d=h.length;c<d;c++)f=h[c],f!=null&&!he(f)&&(o=Ho(t,i,r,o,f));t.restore();const p=r.length/2;if(p>n.length){for(l=0;l<p;l++)delete i[r[l]];r.splice(0,p)}return o}function Mn(t,e,n){const s=t.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((e-i)*s)/s+i}function Eh(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function xc(t,e,n,s){Ng(t,e,n,s,null)}function Ng(t,e,n,s,i){let r,o,a,l,c,d,h,f;const p=e.pointStyle,m=e.rotation,y=e.radius;let v=(m||0)*Ib;if(p&&typeof p=="object"&&(r=p.toString(),r==="[object HTMLImageElement]"||r==="[object HTMLCanvasElement]")){t.save(),t.translate(n,s),t.rotate(v),t.drawImage(p,-p.width/2,-p.height/2,p.width,p.height),t.restore();return}if(!(isNaN(y)||y<=0)){switch(t.beginPath(),p){default:i?t.ellipse(n,s,i/2,y,0,0,fe):t.arc(n,s,y,0,fe),t.closePath();break;case"triangle":d=i?i/2:y,t.moveTo(n+Math.sin(v)*d,s-Math.cos(v)*y),v+=bh,t.lineTo(n+Math.sin(v)*d,s-Math.cos(v)*y),v+=bh,t.lineTo(n+Math.sin(v)*d,s-Math.cos(v)*y),t.closePath();break;case"rectRounded":c=y*.516,l=y-c,o=Math.cos(v+Pn)*l,h=Math.cos(v+Pn)*(i?i/2-c:l),a=Math.sin(v+Pn)*l,f=Math.sin(v+Pn)*(i?i/2-c:l),t.arc(n-h,s-a,c,v-ne,v-_e),t.arc(n+f,s-o,c,v-_e,v),t.arc(n+h,s+a,c,v,v+_e),t.arc(n-f,s+o,c,v+_e,v+ne),t.closePath();break;case"rect":if(!m){l=Math.SQRT1_2*y,d=i?i/2:l,t.rect(n-d,s-l,2*d,2*l);break}v+=Pn;case"rectRot":h=Math.cos(v)*(i?i/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(i?i/2:y),t.moveTo(n-h,s-a),t.lineTo(n+f,s-o),t.lineTo(n+h,s+a),t.lineTo(n-f,s+o),t.closePath();break;case"crossRot":v+=Pn;case"cross":h=Math.cos(v)*(i?i/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(i?i/2:y),t.moveTo(n-h,s-a),t.lineTo(n+h,s+a),t.moveTo(n+f,s-o),t.lineTo(n-f,s+o);break;case"star":h=Math.cos(v)*(i?i/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(i?i/2:y),t.moveTo(n-h,s-a),t.lineTo(n+h,s+a),t.moveTo(n+f,s-o),t.lineTo(n-f,s+o),v+=Pn,h=Math.cos(v)*(i?i/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(i?i/2:y),t.moveTo(n-h,s-a),t.lineTo(n+h,s+a),t.moveTo(n+f,s-o),t.lineTo(n-f,s+o);break;case"line":o=i?i/2:Math.cos(v)*y,a=Math.sin(v)*y,t.moveTo(n-o,s-a),t.lineTo(n+o,s+a);break;case"dash":t.moveTo(n,s),t.lineTo(n+Math.cos(v)*(i?i/2:y),s+Math.sin(v)*y);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function Vt(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function Au(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function Lu(t){t.restore()}function l1(t,e,n,s,i){if(!e)return t.lineTo(n.x,n.y);if(i==="middle"){const r=(e.x+n.x)/2;t.lineTo(r,e.y),t.lineTo(r,n.y)}else i==="after"!=!!s?t.lineTo(e.x,n.y):t.lineTo(n.x,e.y);t.lineTo(n.x,n.y)}function c1(t,e,n,s){if(!e)return t.lineTo(n.x,n.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?n.cp2x:n.cp1x,s?n.cp2y:n.cp1y,n.x,n.y)}function u1(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),J(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function d1(t,e,n,s,i){if(i.strikethrough||i.underline){const r=t.measureText(s),o=e-r.actualBoundingBoxLeft,a=e+r.actualBoundingBoxRight,l=n-r.actualBoundingBoxAscent,c=n+r.actualBoundingBoxDescent,d=i.strikethrough?(l+c)/2:c;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(o,d),t.lineTo(a,d),t.stroke()}}function h1(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function ns(t,e,n,s,i,r={}){const o=he(e)?e:[e],a=r.strokeWidth>0&&r.strokeColor!=="";let l,c;for(t.save(),t.font=i.string,u1(t,r),l=0;l<o.length;++l)c=o[l],r.backdrop&&h1(t,r.backdrop),a&&(r.strokeColor&&(t.strokeStyle=r.strokeColor),J(r.strokeWidth)||(t.lineWidth=r.strokeWidth),t.strokeText(c,n,s,r.maxWidth)),t.fillText(c,n,s,r.maxWidth),d1(t,n,s,c,r),s+=Number(i.lineHeight);t.restore()}function er(t,e){const{x:n,y:s,w:i,h:r,radius:o}=e;t.arc(n+o.topLeft,s+o.topLeft,o.topLeft,1.5*ne,ne,!0),t.lineTo(n,s+r-o.bottomLeft),t.arc(n+o.bottomLeft,s+r-o.bottomLeft,o.bottomLeft,ne,_e,!0),t.lineTo(n+i-o.bottomRight,s+r),t.arc(n+i-o.bottomRight,s+r-o.bottomRight,o.bottomRight,_e,0,!0),t.lineTo(n+i,s+o.topRight),t.arc(n+i-o.topRight,s+o.topRight,o.topRight,0,-_e,!0),t.lineTo(n+o.topLeft,s)}const f1=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,p1=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function m1(t,e){const n=(""+t).match(f1);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const g1=t=>+t||0;function Fu(t,e){const n={},s=Z(e),i=s?Object.keys(e):e,r=Z(t)?s?o=>X(t[o],t[e[o]]):o=>t[o]:()=>t;for(const o of i)n[o]=g1(r(o));return n}function Eg(t){return Fu(t,{top:"y",right:"x",bottom:"y",left:"x"})}function Yn(t){return Fu(t,["topLeft","topRight","bottomLeft","bottomRight"])}function We(t){const e=Eg(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Ne(t,e){t=t||{},e=e||xe.font;let n=X(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let s=X(t.style,e.style);s&&!(""+s).match(p1)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const i={family:X(t.family,e.family),lineHeight:m1(X(t.lineHeight,e.lineHeight),n),size:n,style:s,weight:X(t.weight,e.weight),string:""};return i.string=o1(i),i}function Lr(t,e,n,s){let i=!0,r,o,a;for(r=0,o=t.length;r<o;++r)if(a=t[r],a!==void 0&&(e!==void 0&&typeof a=="function"&&(a=a(e),i=!1),n!==void 0&&he(a)&&(a=a[n%a.length],i=!1),a!==void 0))return s&&!i&&(s.cacheable=!1),a}function y1(t,e,n){const{min:s,max:i}=t,r=xg(e,(i-s)/2),o=(a,l)=>n&&a===0?0:a+l;return{min:o(s,-Math.abs(r)),max:o(i,r)}}function En(t,e){return Object.assign(Object.create(t),e)}function Bu(t,e=[""],n,s,i=()=>t[0]){const r=n||t;typeof s>"u"&&(s=Rg("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:r,_fallback:s,_getTarget:i,override:a=>Bu([a,...t],e,r,s)};return new Proxy(o,{deleteProperty(a,l){return delete a[l],delete a._keys,delete t[0][l],!0},get(a,l){return Mg(a,l,()=>j1(l,e,t,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(a,l){return Mh(a).includes(l)},ownKeys(a){return Mh(a)},set(a,l,c){const d=a._storage||(a._storage=i());return a[l]=d[l]=c,delete a._keys,!0}})}function zs(t,e,n,s){const i={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:Pg(t,s),setContext:r=>zs(t,r,n,s),override:r=>zs(t.override(r),e,n,s)};return new Proxy(i,{deleteProperty(r,o){return delete r[o],delete t[o],!0},get(r,o,a){return Mg(r,o,()=>v1(r,o,a))},getOwnPropertyDescriptor(r,o){return r._descriptors.allKeys?Reflect.has(t,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,o)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(r,o){return Reflect.has(t,o)},ownKeys(){return Reflect.ownKeys(t)},set(r,o,a){return t[o]=a,delete r[o],!0}})}function Pg(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:s=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:s,isScriptable:Sn(n)?n:()=>n,isIndexable:Sn(s)?s:()=>s}}const x1=(t,e)=>t?t+Mu(e):e,Iu=(t,e)=>Z(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function Mg(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const s=n();return t[e]=s,s}function v1(t,e,n){const{_proxy:s,_context:i,_subProxy:r,_descriptors:o}=t;let a=s[e];return Sn(a)&&o.isScriptable(e)&&(a=b1(e,a,t,n)),he(a)&&a.length&&(a=_1(e,a,t,o.isIndexable)),Iu(e,a)&&(a=zs(a,i,r&&r[e],o)),a}function b1(t,e,n,s){const{_proxy:i,_context:r,_subProxy:o,_stack:a}=n;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let l=e(r,o||s);return a.delete(t),Iu(t,l)&&(l=zu(i._scopes,i,t,l)),l}function _1(t,e,n,s){const{_proxy:i,_context:r,_subProxy:o,_descriptors:a}=n;if(typeof r.index<"u"&&s(t))return e[r.index%e.length];if(Z(e[0])){const l=e,c=i._scopes.filter(d=>d!==l);e=[];for(const d of l){const h=zu(c,i,t,d);e.push(zs(h,r,o&&o[t],a))}}return e}function Tg(t,e,n){return Sn(t)?t(e,n):t}const k1=(t,e)=>t===!0?e:typeof t=="string"?es(e,t):void 0;function w1(t,e,n,s,i){for(const r of e){const o=k1(n,r);if(o){t.add(o);const a=Tg(o._fallback,n,i);if(typeof a<"u"&&a!==n&&a!==s)return a}else if(o===!1&&typeof s<"u"&&n!==s)return null}return!1}function zu(t,e,n,s){const i=e._rootScopes,r=Tg(e._fallback,n,s),o=[...t,...i],a=new Set;a.add(s);let l=Ph(a,o,n,r||n,s);return l===null||typeof r<"u"&&r!==n&&(l=Ph(a,o,r,l,s),l===null)?!1:Bu(Array.from(a),[""],i,r,()=>S1(e,n,s))}function Ph(t,e,n,s,i){for(;n;)n=w1(t,e,n,s,i);return n}function S1(t,e,n){const s=t._getTarget();e in s||(s[e]={});const i=s[e];return he(i)&&Z(n)?n:i||{}}function j1(t,e,n,s){let i;for(const r of e)if(i=Rg(x1(r,t),n),typeof i<"u")return Iu(t,i)?zu(n,s,t,i):i}function Rg(t,e){for(const n of e){if(!n)continue;const s=n[t];if(typeof s<"u")return s}}function Mh(t){let e=t._keys;return e||(e=t._keys=C1(t._scopes)),e}function C1(t){const e=new Set;for(const n of t)for(const s of Object.keys(n).filter(i=>!i.startsWith("_")))e.add(s);return Array.from(e)}const N1=Number.EPSILON||1e-14,Ws=(t,e)=>e<t.length&&!t[e].skip&&t[e],Og=t=>t==="x"?"y":"x";function E1(t,e,n,s){const i=t.skip?e:t,r=e,o=n.skip?e:n,a=gc(r,i),l=gc(o,r);let c=a/(a+l),d=l/(a+l);c=isNaN(c)?0:c,d=isNaN(d)?0:d;const h=s*c,f=s*d;return{previous:{x:r.x-h*(o.x-i.x),y:r.y-h*(o.y-i.y)},next:{x:r.x+f*(o.x-i.x),y:r.y+f*(o.y-i.y)}}}function P1(t,e,n){const s=t.length;let i,r,o,a,l,c=Ws(t,0);for(let d=0;d<s-1;++d)if(l=c,c=Ws(t,d+1),!(!l||!c)){if(Ci(e[d],0,N1)){n[d]=n[d+1]=0;continue}i=n[d]/e[d],r=n[d+1]/e[d],a=Math.pow(i,2)+Math.pow(r,2),!(a<=9)&&(o=3/Math.sqrt(a),n[d]=i*o*e[d],n[d+1]=r*o*e[d])}}function M1(t,e,n="x"){const s=Og(n),i=t.length;let r,o,a,l=Ws(t,0);for(let c=0;c<i;++c){if(o=a,a=l,l=Ws(t,c+1),!a)continue;const d=a[n],h=a[s];o&&(r=(d-o[n])/3,a[`cp1${n}`]=d-r,a[`cp1${s}`]=h-r*e[c]),l&&(r=(l[n]-d)/3,a[`cp2${n}`]=d+r,a[`cp2${s}`]=h+r*e[c])}}function T1(t,e="x"){const n=Og(e),s=t.length,i=Array(s).fill(0),r=Array(s);let o,a,l,c=Ws(t,0);for(o=0;o<s;++o)if(a=l,l=c,c=Ws(t,o+1),!!l){if(c){const d=c[e]-l[e];i[o]=d!==0?(c[n]-l[n])/d:0}r[o]=a?c?Lt(i[o-1])!==Lt(i[o])?0:(i[o-1]+i[o])/2:i[o-1]:i[o]}P1(t,i,r),M1(t,r,e)}function Fr(t,e,n){return Math.max(Math.min(t,n),e)}function R1(t,e){let n,s,i,r,o,a=Vt(t[0],e);for(n=0,s=t.length;n<s;++n)o=r,r=a,a=n<s-1&&Vt(t[n+1],e),r&&(i=t[n],o&&(i.cp1x=Fr(i.cp1x,e.left,e.right),i.cp1y=Fr(i.cp1y,e.top,e.bottom)),a&&(i.cp2x=Fr(i.cp2x,e.left,e.right),i.cp2y=Fr(i.cp2y,e.top,e.bottom)))}function O1(t,e,n,s,i){let r,o,a,l;if(e.spanGaps&&(t=t.filter(c=>!c.skip)),e.cubicInterpolationMode==="monotone")T1(t,i);else{let c=s?t[t.length-1]:t[0];for(r=0,o=t.length;r<o;++r)a=t[r],l=E1(c,a,t[Math.min(r+1,o-(s?0:1))%o],e.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}e.capBezierPoints&&R1(t,n)}function Wu(){return typeof window<"u"&&typeof document<"u"}function $u(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function Uo(t,e,n){let s;return typeof t=="string"?(s=parseInt(t,10),t.indexOf("%")!==-1&&(s=s/100*e.parentNode[n])):s=t,s}const _a=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function D1(t,e){return _a(t).getPropertyValue(e)}const A1=["top","right","bottom","left"];function Xn(t,e,n){const s={};n=n?"-"+n:"";for(let i=0;i<4;i++){const r=A1[i];s[r]=parseFloat(t[e+"-"+r+n])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const L1=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function F1(t,e){const n=t.touches,s=n&&n.length?n[0]:t,{offsetX:i,offsetY:r}=s;let o=!1,a,l;if(L1(i,r,t.target))a=i,l=r;else{const c=e.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,o=!0}return{x:a,y:l,box:o}}function An(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:s}=e,i=_a(n),r=i.boxSizing==="border-box",o=Xn(i,"padding"),a=Xn(i,"border","width"),{x:l,y:c,box:d}=F1(t,n),h=o.left+(d&&a.left),f=o.top+(d&&a.top);let{width:p,height:m}=e;return r&&(p-=o.width+a.width,m-=o.height+a.height),{x:Math.round((l-h)/p*n.width/s),y:Math.round((c-f)/m*n.height/s)}}function B1(t,e,n){let s,i;if(e===void 0||n===void 0){const r=t&&$u(t);if(!r)e=t.clientWidth,n=t.clientHeight;else{const o=r.getBoundingClientRect(),a=_a(r),l=Xn(a,"border","width"),c=Xn(a,"padding");e=o.width-c.width-l.width,n=o.height-c.height-l.height,s=Uo(a.maxWidth,r,"clientWidth"),i=Uo(a.maxHeight,r,"clientHeight")}}return{width:e,height:n,maxWidth:s||$o,maxHeight:i||$o}}const Br=t=>Math.round(t*10)/10;function I1(t,e,n,s){const i=_a(t),r=Xn(i,"margin"),o=Uo(i.maxWidth,t,"clientWidth")||$o,a=Uo(i.maxHeight,t,"clientHeight")||$o,l=B1(t,e,n);let{width:c,height:d}=l;if(i.boxSizing==="content-box"){const f=Xn(i,"border","width"),p=Xn(i,"padding");c-=p.width+f.width,d-=p.height+f.height}return c=Math.max(0,c-r.width),d=Math.max(0,s?c/s:d-r.height),c=Br(Math.min(c,o,l.maxWidth)),d=Br(Math.min(d,a,l.maxHeight)),c&&!d&&(d=Br(c/2)),(e!==void 0||n!==void 0)&&s&&l.height&&d>l.height&&(d=l.height,c=Br(Math.floor(d*s))),{width:c,height:d}}function Th(t,e,n){const s=e||1,i=Math.floor(t.height*s),r=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const o=t.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),t.currentDevicePixelRatio!==s||o.height!==i||o.width!==r?(t.currentDevicePixelRatio=s,o.height=i,o.width=r,t.ctx.setTransform(s,0,0,s,0,0),!0):!1}const z1=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};Wu()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function Rh(t,e){const n=D1(t,e),s=n&&n.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Ln(t,e,n,s){return{x:t.x+n*(e.x-t.x),y:t.y+n*(e.y-t.y)}}function W1(t,e,n,s){return{x:t.x+n*(e.x-t.x),y:s==="middle"?n<.5?t.y:e.y:s==="after"?n<1?t.y:e.y:n>0?e.y:t.y}}function $1(t,e,n,s){const i={x:t.cp2x,y:t.cp2y},r={x:e.cp1x,y:e.cp1y},o=Ln(t,i,n),a=Ln(i,r,n),l=Ln(r,e,n),c=Ln(o,a,n),d=Ln(a,l,n);return Ln(c,d,n)}const H1=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,s){return n-s},leftForLtr(n,s){return n-s}}},U1=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function Rs(t,e,n){return t?H1(e,n):U1()}function Dg(t,e){let n,s;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,s=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=s)}function Ag(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Lg(t){return t==="angle"?{between:Zi,compare:Hb,normalize:Xe}:{between:dn,compare:(e,n)=>e-n,normalize:e=>e}}function Oh({start:t,end:e,count:n,loop:s,style:i}){return{start:t%n,end:e%n,loop:s&&(e-t+1)%n===0,style:i}}function V1(t,e,n){const{property:s,start:i,end:r}=n,{between:o,normalize:a}=Lg(s),l=e.length;let{start:c,end:d,loop:h}=t,f,p;if(h){for(c+=l,d+=l,f=0,p=l;f<p&&o(a(e[c%l][s]),i,r);++f)c--,d--;c%=l,d%=l}return d<c&&(d+=l),{start:c,end:d,loop:h,style:t.style}}function Y1(t,e,n){if(!n)return[t];const{property:s,start:i,end:r}=n,o=e.length,{compare:a,between:l,normalize:c}=Lg(s),{start:d,end:h,loop:f,style:p}=V1(t,e,n),m=[];let y=!1,v=null,g,x,b;const k=()=>l(i,b,g)&&a(i,b)!==0,_=()=>a(r,g)===0||l(r,b,g),C=()=>y||k(),N=()=>!y||_();for(let S=d,L=d;S<=h;++S)x=e[S%o],!x.skip&&(g=c(x[s]),g!==b&&(y=l(g,i,r),v===null&&C()&&(v=a(g,i)===0?S:L),v!==null&&N()&&(m.push(Oh({start:v,end:S,loop:f,count:o,style:p})),v=null),L=S,b=g));return v!==null&&m.push(Oh({start:v,end:h,loop:f,count:o,style:p})),m}function X1(t,e){const n=[],s=t.segments;for(let i=0;i<s.length;i++){const r=Y1(s[i],t.points,e);r.length&&n.push(...r)}return n}function K1(t,e,n,s){let i=0,r=e-1;if(n&&!s)for(;i<e&&!t[i].skip;)i++;for(;i<e&&t[i].skip;)i++;for(i%=e,n&&(r+=i);r>i&&t[r%e].skip;)r--;return r%=e,{start:i,end:r}}function q1(t,e,n,s){const i=t.length,r=[];let o=e,a=t[e],l;for(l=e+1;l<=n;++l){const c=t[l%i];c.skip||c.stop?a.skip||(s=!1,r.push({start:e%i,end:(l-1)%i,loop:s}),e=o=c.stop?l:null):(o=l,a.skip&&(e=l)),a=c}return o!==null&&r.push({start:e%i,end:o%i,loop:s}),r}function Q1(t,e){const n=t.points,s=t.options.spanGaps,i=n.length;if(!i)return[];const r=!!t._loop,{start:o,end:a}=K1(n,i,r,s);if(s===!0)return Dh(t,[{start:o,end:a,loop:r}],n,e);const l=a<o?a+i:a,c=!!t._fullLoop&&o===0&&a===i-1;return Dh(t,q1(n,o,l,c),n,e)}function Dh(t,e,n,s){return!s||!s.setContext||!n?e:G1(t,e,n,s)}function G1(t,e,n,s){const i=t._chart.getContext(),r=Ah(t.options),{_datasetIndex:o,options:{spanGaps:a}}=t,l=n.length,c=[];let d=r,h=e[0].start,f=h;function p(m,y,v,g){const x=a?-1:1;if(m!==y){for(m+=l;n[m%l].skip;)m-=x;for(;n[y%l].skip;)y+=x;m%l!==y%l&&(c.push({start:m%l,end:y%l,loop:v,style:g}),d=g,h=y%l)}}for(const m of e){h=a?h:m.start;let y=n[h%l],v;for(f=h+1;f<=m.end;f++){const g=n[f%l];v=Ah(s.setContext(En(i,{type:"segment",p0:y,p1:g,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:o}))),J1(v,d)&&p(h,f-1,m.loop,d),y=g,d=v}h<f-1&&p(h,f-1,m.loop,d)}return c}function Ah(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function J1(t,e){if(!e)return!1;const n=[],s=function(i,r){return Du(r)?(n.includes(r)||n.push(r),n.indexOf(r)):r};return JSON.stringify(t,s)!==JSON.stringify(e,s)}function Ir(t,e,n){return t.options.clip?t[n]:e[n]}function Z1(t,e){const{xScale:n,yScale:s}=t;return n&&s?{left:Ir(n,e,"left"),right:Ir(n,e,"right"),top:Ir(s,e,"top"),bottom:Ir(s,e,"bottom")}:e}function e_(t,e){const n=e._clip;if(n.disabled)return!1;const s=Z1(e,t.chartArea);return{left:n.left===!1?0:s.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:s.right+(n.right===!0?0:n.right),top:n.top===!1?0:s.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:s.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class t_{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,s,i){const r=n.listeners[i],o=n.duration;r.forEach(a=>a({chart:e,initial:n.initial,numSteps:o,currentStep:Math.min(s-n.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=Sg.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((s,i)=>{if(!s.running||!s.items.length)return;const r=s.items;let o=r.length-1,a=!1,l;for(;o>=0;--o)l=r[o],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(e),a=!0):(r[o]=r[r.length-1],r.pop());a&&(i.draw(),this._notify(i,s,e,"progress")),r.length||(s.running=!1,this._notify(i,s,e,"complete"),s.initial=!1),n+=r.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let s=n.get(e);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,s)),s}listen(e,n,s){this._getAnims(e).listeners[n].push(s)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((s,i)=>Math.max(s,i._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const s=n.items;let i=s.length-1;for(;i>=0;--i)s[i].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Bt=new t_;const Lh="transparent",n_={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const s=Ch(t||Lh),i=s.valid&&Ch(e||Lh);return i&&i.valid?i.mix(s,n).hexString():e},number(t,e,n){return t+(e-t)*n}};class s_{constructor(e,n,s,i){const r=n[s];i=Lr([e.to,i,r,e.from]);const o=Lr([e.from,r,i]);this._active=!0,this._fn=e.fn||n_[e.type||typeof o],this._easing=Ni[e.easing]||Ni.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=s,this._from=o,this._to=i,this._promises=void 0}active(){return this._active}update(e,n,s){if(this._active){this._notify(!1);const i=this._target[this._prop],r=s-this._start,o=this._duration-r;this._start=s,this._duration=Math.floor(Math.max(o,e.duration)),this._total+=r,this._loop=!!e.loop,this._to=Lr([e.to,n,i,e.from]),this._from=Lr([e.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,s=this._duration,i=this._prop,r=this._from,o=this._loop,a=this._to;let l;if(this._active=r!==a&&(o||n<s),!this._active){this._target[i]=a,this._notify(!0);return}if(n<0){this._target[i]=r;return}l=n/s%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(r,a,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,s)=>{e.push({res:n,rej:s})})}_notify(e){const n=e?"res":"rej",s=this._promises||[];for(let i=0;i<s.length;i++)s[i][n]()}}class Fg{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!Z(e))return;const n=Object.keys(xe.animation),s=this._properties;Object.getOwnPropertyNames(e).forEach(i=>{const r=e[i];if(!Z(r))return;const o={};for(const a of n)o[a]=r[a];(he(r.properties)&&r.properties||[i]).forEach(a=>{(a===i||!s.has(a))&&s.set(a,o)})})}_animateOptions(e,n){const s=n.options,i=r_(e,s);if(!i)return[];const r=this._createAnimations(i,s);return s.$shared&&i_(e.options.$animations,s).then(()=>{e.options=s},()=>{}),r}_createAnimations(e,n){const s=this._properties,i=[],r=e.$animations||(e.$animations={}),o=Object.keys(n),a=Date.now();let l;for(l=o.length-1;l>=0;--l){const c=o[l];if(c.charAt(0)==="$")continue;if(c==="options"){i.push(...this._animateOptions(e,n));continue}const d=n[c];let h=r[c];const f=s.get(c);if(h)if(f&&h.active()){h.update(f,d,a);continue}else h.cancel();if(!f||!f.duration){e[c]=d;continue}r[c]=h=new s_(f,e,c,d),i.push(h)}return i}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const s=this._createAnimations(e,n);if(s.length)return Bt.add(this._chart,s),!0}}function i_(t,e){const n=[],s=Object.keys(e);for(let i=0;i<s.length;i++){const r=t[s[i]];r&&r.active()&&n.push(r.wait())}return Promise.all(n)}function r_(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function Fh(t,e){const n=t&&t.options||{},s=n.reverse,i=n.min===void 0?e:0,r=n.max===void 0?e:0;return{start:s?r:i,end:s?i:r}}function o_(t,e,n){if(n===!1)return!1;const s=Fh(t,n),i=Fh(e,n);return{top:i.end,right:s.end,bottom:i.start,left:s.start}}function a_(t){let e,n,s,i;return Z(t)?(e=t.top,n=t.right,s=t.bottom,i=t.left):e=n=s=i=t,{top:e,right:n,bottom:s,left:i,disabled:t===!1}}function Bg(t,e){const n=[],s=t._getSortedDatasetMetas(e);let i,r;for(i=0,r=s.length;i<r;++i)n.push(s[i].index);return n}function Bh(t,e,n,s={}){const i=t.keys,r=s.mode==="single";let o,a,l,c;if(e===null)return;let d=!1;for(o=0,a=i.length;o<a;++o){if(l=+i[o],l===n){if(d=!0,s.all)continue;break}c=t.values[l],Pe(c)&&(r||e===0||Lt(e)===Lt(c))&&(e+=c)}return!d&&!s.all?0:e}function l_(t,e){const{iScale:n,vScale:s}=e,i=n.axis==="x"?"x":"y",r=s.axis==="x"?"x":"y",o=Object.keys(t),a=new Array(o.length);let l,c,d;for(l=0,c=o.length;l<c;++l)d=o[l],a[l]={[i]:d,[r]:t[d]};return a}function ll(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function c_(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function u_(t){const{min:e,max:n,minDefined:s,maxDefined:i}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function d_(t,e,n){const s=t[e]||(t[e]={});return s[n]||(s[n]={})}function Ih(t,e,n,s){for(const i of e.getMatchingVisibleMetas(s).reverse()){const r=t[i.index];if(n&&r>0||!n&&r<0)return i.index}return null}function zh(t,e){const{chart:n,_cachedMeta:s}=t,i=n._stacks||(n._stacks={}),{iScale:r,vScale:o,index:a}=s,l=r.axis,c=o.axis,d=c_(r,o,s),h=e.length;let f;for(let p=0;p<h;++p){const m=e[p],{[l]:y,[c]:v}=m,g=m._stacks||(m._stacks={});f=g[c]=d_(i,d,y),f[a]=v,f._top=Ih(f,o,!0,s.type),f._bottom=Ih(f,o,!1,s.type);const x=f._visualValues||(f._visualValues={});x[a]=v}}function cl(t,e){const n=t.scales;return Object.keys(n).filter(s=>n[s].axis===e).shift()}function h_(t,e){return En(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function f_(t,e,n){return En(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function ni(t,e){const n=t.controller.index,s=t.vScale&&t.vScale.axis;if(s){e=e||t._parsed;for(const i of e){const r=i._stacks;if(!r||r[s]===void 0||r[s][n]===void 0)return;delete r[s][n],r[s]._visualValues!==void 0&&r[s]._visualValues[n]!==void 0&&delete r[s]._visualValues[n]}}}const ul=t=>t==="reset"||t==="none",Wh=(t,e)=>e?t:Object.assign({},t),p_=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:Bg(n,!0),values:null};class Kn{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=ll(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&ni(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,s=this.getDataset(),i=(h,f,p,m)=>h==="x"?f:h==="r"?m:p,r=n.xAxisID=X(s.xAxisID,cl(e,"x")),o=n.yAxisID=X(s.yAxisID,cl(e,"y")),a=n.rAxisID=X(s.rAxisID,cl(e,"r")),l=n.indexAxis,c=n.iAxisID=i(l,r,o,a),d=n.vAxisID=i(l,o,r,a);n.xScale=this.getScaleForId(r),n.yScale=this.getScaleForId(o),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(c),n.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&wh(this._data,this),e._stacked&&ni(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),s=this._data;if(Z(n)){const i=this._cachedMeta;this._data=l_(n,i)}else if(s!==n){if(s){wh(s,this);const i=this._cachedMeta;ni(i),i._parsed=[]}n&&Object.isExtensible(n)&&Xb(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,s=this.getDataset();let i=!1;this._dataCheck();const r=n._stacked;n._stacked=ll(n.vScale,n),n.stack!==s.stack&&(i=!0,ni(n),n.stack=s.stack),this._resyncElements(e),(i||r!==n._stacked)&&(zh(this,n._parsed),n._stacked=ll(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),s=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:s,_data:i}=this,{iScale:r,_stacked:o}=s,a=r.axis;let l=e===0&&n===i.length?!0:s._sorted,c=e>0&&s._parsed[e-1],d,h,f;if(this._parsing===!1)s._parsed=i,s._sorted=!0,f=i;else{he(i[e])?f=this.parseArrayData(s,i,e,n):Z(i[e])?f=this.parseObjectData(s,i,e,n):f=this.parsePrimitiveData(s,i,e,n);const p=()=>h[a]===null||c&&h[a]<c[a];for(d=0;d<n;++d)s._parsed[d+e]=h=f[d],l&&(p()&&(l=!1),c=h);s._sorted=l}o&&zh(this,f)}parsePrimitiveData(e,n,s,i){const{iScale:r,vScale:o}=e,a=r.axis,l=o.axis,c=r.getLabels(),d=r===o,h=new Array(i);let f,p,m;for(f=0,p=i;f<p;++f)m=f+s,h[f]={[a]:d||r.parse(c[m],m),[l]:o.parse(n[m],m)};return h}parseArrayData(e,n,s,i){const{xScale:r,yScale:o}=e,a=new Array(i);let l,c,d,h;for(l=0,c=i;l<c;++l)d=l+s,h=n[d],a[l]={x:r.parse(h[0],d),y:o.parse(h[1],d)};return a}parseObjectData(e,n,s,i){const{xScale:r,yScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(i);let d,h,f,p;for(d=0,h=i;d<h;++d)f=d+s,p=n[f],c[d]={x:r.parse(es(p,a),f),y:o.parse(es(p,l),f)};return c}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,s){const i=this.chart,r=this._cachedMeta,o=n[e.axis],a={keys:Bg(i,!0),values:n._stacks[e.axis]._visualValues};return Bh(a,o,r.index,{mode:s})}updateRangeFromParsed(e,n,s,i){const r=s[n.axis];let o=r===null?NaN:r;const a=i&&s._stacks[n.axis];i&&a&&(i.values=a,o=Bh(i,r,this._cachedMeta.index)),e.min=Math.min(e.min,o),e.max=Math.max(e.max,o)}getMinMax(e,n){const s=this._cachedMeta,i=s._parsed,r=s._sorted&&e===s.iScale,o=i.length,a=this._getOtherScale(e),l=p_(n,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:h}=u_(a);let f,p;function m(){p=i[f];const y=p[a.axis];return!Pe(p[e.axis])||d>y||h<y}for(f=0;f<o&&!(!m()&&(this.updateRangeFromParsed(c,e,p,l),r));++f);if(r){for(f=o-1;f>=0;--f)if(!m()){this.updateRangeFromParsed(c,e,p,l);break}}return c}getAllParsedValues(e){const n=this._cachedMeta._parsed,s=[];let i,r,o;for(i=0,r=n.length;i<r;++i)o=n[i][e.axis],Pe(o)&&s.push(o);return s}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,s=n.iScale,i=n.vScale,r=this.getParsed(e);return{label:s?""+s.getLabelForValue(r[s.axis]):"",value:i?""+i.getLabelForValue(r[i.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=a_(X(this.options.clip,o_(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,s=this._cachedMeta,i=s.data||[],r=n.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||i.length-a,c=this.options.drawActiveElementsOnTop;let d;for(s.dataset&&s.dataset.draw(e,r,a,l),d=a;d<a+l;++d){const h=i[d];h.hidden||(h.active&&c?o.push(h):h.draw(e,r))}for(d=0;d<o.length;++d)o[d].draw(e,r)}getStyle(e,n){const s=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(e||0,s)}getContext(e,n,s){const i=this.getDataset();let r;if(e>=0&&e<this._cachedMeta.data.length){const o=this._cachedMeta.data[e];r=o.$context||(o.$context=f_(this.getContext(),e,o)),r.parsed=this.getParsed(e),r.raw=i.data[e],r.index=r.dataIndex=e}else r=this.$context||(this.$context=h_(this.chart.getContext(),this.index)),r.dataset=i,r.index=r.datasetIndex=this.index;return r.active=!!n,r.mode=s,r}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",s){const i=n==="active",r=this._cachedDataOpts,o=e+"-"+n,a=r[o],l=this.enableOptionSharing&&Gi(s);if(a)return Wh(a,l);const c=this.chart.config,d=c.datasetElementScopeKeys(this._type,e),h=i?[`${e}Hover`,"hover",e,""]:[e,""],f=c.getOptionScopes(this.getDataset(),d),p=Object.keys(xe.elements[e]),m=()=>this.getContext(s,i,n),y=c.resolveNamedOptions(f,p,m,h);return y.$shared&&(y.$shared=l,r[o]=Object.freeze(Wh(y,l))),y}_resolveAnimations(e,n,s){const i=this.chart,r=this._cachedDataOpts,o=`animation-${n}`,a=r[o];if(a)return a;let l;if(i.options.animation!==!1){const d=this.chart.config,h=d.datasetAnimationScopeKeys(this._type,n),f=d.getOptionScopes(this.getDataset(),h);l=d.createResolver(f,this.getContext(e,s,n))}const c=new Fg(i,l&&l.animations);return l&&l._cacheable&&(r[o]=Object.freeze(c)),c}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||ul(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const s=this.resolveDataElementOptions(e,n),i=this._sharedOptions,r=this.getSharedOptions(s),o=this.includeOptions(n,r)||r!==i;return this.updateSharedOptions(r,n,s),{sharedOptions:r,includeOptions:o}}updateElement(e,n,s,i){ul(i)?Object.assign(e,s):this._resolveAnimations(n,i).update(e,s)}updateSharedOptions(e,n,s){e&&!ul(n)&&this._resolveAnimations(void 0,n).update(e,s)}_setStyle(e,n,s,i){e.active=i;const r=this.getStyle(n,i);this._resolveAnimations(n,s,i).update(e,{options:!i&&this.getSharedOptions(r)||r})}removeHoverStyle(e,n,s){this._setStyle(e,s,"active",!1)}setHoverStyle(e,n,s){this._setStyle(e,s,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const i=s.length,r=n.length,o=Math.min(r,i);o&&this.parse(0,o),r>i?this._insertElements(i,r-i,e):r<i&&this._removeElements(r,i-r)}_insertElements(e,n,s=!0){const i=this._cachedMeta,r=i.data,o=e+n;let a;const l=c=>{for(c.length+=n,a=c.length-1;a>=o;a--)c[a]=c[a-n]};for(l(r),a=e;a<o;++a)r[a]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(e,n),s&&this.updateElements(r,e,n,"reset")}updateElements(e,n,s,i){}_removeElements(e,n){const s=this._cachedMeta;if(this._parsing){const i=s._parsed.splice(e,n);s._stacked&&ni(s,i)}s.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,s,i]=e;this[n](s,i)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const s=arguments.length-2;s&&this._sync(["_insertElements",e,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}F(Kn,"defaults",{}),F(Kn,"datasetElementType",null),F(Kn,"dataElementType",null);function m_(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let s=[];for(let i=0,r=n.length;i<r;i++)s=s.concat(n[i].controller.getAllParsedValues(t));t._cache.$bar=wg(s.sort((i,r)=>i-r))}return t._cache.$bar}function g_(t){const e=t.iScale,n=m_(e,t.type);let s=e._length,i,r,o,a;const l=()=>{o===32767||o===-32768||(Gi(a)&&(s=Math.min(s,Math.abs(o-a)||s)),a=o)};for(i=0,r=n.length;i<r;++i)o=e.getPixelForValue(n[i]),l();for(a=void 0,i=0,r=e.ticks.length;i<r;++i)o=e.getPixelForTick(i),l();return s}function y_(t,e,n,s){const i=n.barThickness;let r,o;return J(i)?(r=e.min*n.categoryPercentage,o=n.barPercentage):(r=i*s,o=1),{chunk:r/s,ratio:o,start:e.pixels[t]-r/2}}function x_(t,e,n,s){const i=e.pixels,r=i[t];let o=t>0?i[t-1]:null,a=t<i.length-1?i[t+1]:null;const l=n.categoryPercentage;o===null&&(o=r-(a===null?e.end-e.start:a-r)),a===null&&(a=r+r-o);const c=r-(r-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/s,ratio:n.barPercentage,start:c}}function v_(t,e,n,s){const i=n.parse(t[0],s),r=n.parse(t[1],s),o=Math.min(i,r),a=Math.max(i,r);let l=o,c=a;Math.abs(o)>Math.abs(a)&&(l=a,c=o),e[n.axis]=c,e._custom={barStart:l,barEnd:c,start:i,end:r,min:o,max:a}}function Ig(t,e,n,s){return he(t)?v_(t,e,n,s):e[n.axis]=n.parse(t,s),e}function $h(t,e,n,s){const i=t.iScale,r=t.vScale,o=i.getLabels(),a=i===r,l=[];let c,d,h,f;for(c=n,d=n+s;c<d;++c)f=e[c],h={},h[i.axis]=a||i.parse(o[c],c),l.push(Ig(f,h,r,c));return l}function dl(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function b_(t,e,n){return t!==0?Lt(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function __(t){let e,n,s,i,r;return t.horizontal?(e=t.base>t.x,n="left",s="right"):(e=t.base<t.y,n="bottom",s="top"),e?(i="end",r="start"):(i="start",r="end"),{start:n,end:s,reverse:e,top:i,bottom:r}}function k_(t,e,n,s){let i=e.borderSkipped;const r={};if(!i){t.borderSkipped=r;return}if(i===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:o,end:a,reverse:l,top:c,bottom:d}=__(t);i==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===s?i=c:(n._bottom||0)===s?i=d:(r[Hh(d,o,a,l)]=!0,i=c)),r[Hh(i,o,a,l)]=!0,t.borderSkipped=r}function Hh(t,e,n,s){return s?(t=w_(t,e,n),t=Uh(t,n,e)):t=Uh(t,e,n),t}function w_(t,e,n){return t===e?n:t===n?e:t}function Uh(t,e,n){return t==="start"?e:t==="end"?n:t}function S_(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class ro extends Kn{parsePrimitiveData(e,n,s,i){return $h(e,n,s,i)}parseArrayData(e,n,s,i){return $h(e,n,s,i)}parseObjectData(e,n,s,i){const{iScale:r,vScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=r.axis==="x"?a:l,d=o.axis==="x"?a:l,h=[];let f,p,m,y;for(f=s,p=s+i;f<p;++f)y=n[f],m={},m[r.axis]=r.parse(es(y,c),f),h.push(Ig(es(y,d),m,o,f));return h}updateRangeFromParsed(e,n,s,i){super.updateRangeFromParsed(e,n,s,i);const r=s._custom;r&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,r.min),e.max=Math.max(e.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:s,vScale:i}=n,r=this.getParsed(e),o=r._custom,a=dl(o)?"["+o.start+", "+o.end+"]":""+i.getLabelForValue(r[i.axis]);return{label:""+s.getLabelForValue(r[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,s,i){const r=i==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),d=this._getRuler(),{sharedOptions:h,includeOptions:f}=this._getSharedOptions(n,i);for(let p=n;p<n+s;p++){const m=this.getParsed(p),y=r||J(m[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(p),v=this._calculateBarIndexPixels(p,d),g=(m._stacks||{})[a.axis],x={horizontal:c,base:y.base,enableBorderRadius:!g||dl(m._custom)||o===g._top||o===g._bottom,x:c?y.head:v.center,y:c?v.center:y.head,height:c?v.size:Math.abs(y.size),width:c?Math.abs(y.size):v.size};f&&(x.options=h||this.resolveDataElementOptions(p,e[p].active?"active":i));const b=x.options||e[p].options;k_(x,b,g,o),S_(x,b,d.ratio),this.updateElement(e[p],p,x,i)}}_getStacks(e,n){const{iScale:s}=this._cachedMeta,i=s.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),r=s.options.stacked,o=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[s.axis],c=d=>{const h=d._parsed.find(p=>p[s.axis]===l),f=h&&h[d.vScale.axis];if(J(f)||isNaN(f))return!0};for(const d of i)if(!(n!==void 0&&c(d))&&((r===!1||o.indexOf(d.stack)===-1||r===void 0&&d.stack===void 0)&&o.push(d.stack),d.index===e))break;return o.length||o.push(void 0),o}_getStackCount(e){return this._getStacks(void 0,e).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const e=this.chart.scales,n=this.chart.options.indexAxis;return Object.keys(e).filter(s=>e[s].axis===n).shift()}_getAxis(){const e={},n=this.getFirstScaleIdForIndexAxis();for(const s of this.chart.data.datasets)e[X(this.chart.options.indexAxis==="x"?s.xAxisID:s.yAxisID,n)]=!0;return Object.keys(e)}_getStackIndex(e,n,s){const i=this._getStacks(e,s),r=n!==void 0?i.indexOf(n):-1;return r===-1?i.length-1:r}_getRuler(){const e=this.options,n=this._cachedMeta,s=n.iScale,i=[];let r,o;for(r=0,o=n.data.length;r<o;++r)i.push(s.getPixelForValue(this.getParsed(r)[s.axis],r));const a=e.barThickness;return{min:a||g_(n),pixels:i,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:e.grouped,ratio:a?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:s,index:i},options:{base:r,minBarLength:o}}=this,a=r||0,l=this.getParsed(e),c=l._custom,d=dl(c);let h=l[n.axis],f=0,p=s?this.applyStack(n,l,s):h,m,y;p!==h&&(f=p-h,p=h),d&&(h=c.barStart,p=c.barEnd-c.barStart,h!==0&&Lt(h)!==Lt(c.barEnd)&&(f=0),f+=h);const v=!J(r)&&!d?r:f;let g=n.getPixelForValue(v);if(this.chart.getDataVisibility(e)?m=n.getPixelForValue(f+p):m=g,y=m-g,Math.abs(y)<o){y=b_(y,n,a)*o,h===a&&(g-=y/2);const x=n.getPixelForDecimal(0),b=n.getPixelForDecimal(1),k=Math.min(x,b),_=Math.max(x,b);g=Math.max(Math.min(g,_),k),m=g+y,s&&!d&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(m)-n.getValueForPixel(g))}if(g===n.getPixelForValue(a)){const x=Lt(y)*n.getLineWidthForValue(a)/2;g+=x,y-=x}return{size:y,base:g,head:m,center:m+y/2}}_calculateBarIndexPixels(e,n){const s=n.scale,i=this.options,r=i.skipNull,o=X(i.maxBarThickness,1/0);let a,l;const c=this._getAxisCount();if(n.grouped){const d=r?this._getStackCount(e):n.stackCount,h=i.barThickness==="flex"?x_(e,n,i,d*c):y_(e,n,i,d*c),f=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,p=this._getAxis().indexOf(X(f,this.getFirstScaleIdForIndexAxis())),m=this._getStackIndex(this.index,this._cachedMeta.stack,r?e:void 0)+p;a=h.start+h.chunk*m+h.chunk/2,l=Math.min(o,h.chunk*h.ratio)}else a=s.getPixelForValue(this.getParsed(e)[s.axis],e),l=Math.min(o,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const e=this._cachedMeta,n=e.vScale,s=e.data,i=s.length;let r=0;for(;r<i;++r)this.getParsed(r)[n.axis]!==null&&!s[r].hidden&&s[r].draw(this._ctx)}}F(ro,"id","bar"),F(ro,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),F(ro,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function j_(t,e,n){let s=1,i=1,r=0,o=0;if(e<fe){const a=t,l=a+e,c=Math.cos(a),d=Math.sin(a),h=Math.cos(l),f=Math.sin(l),p=(b,k,_)=>Zi(b,a,l,!0)?1:Math.max(k,k*n,_,_*n),m=(b,k,_)=>Zi(b,a,l,!0)?-1:Math.min(k,k*n,_,_*n),y=p(0,c,h),v=p(_e,d,f),g=m(ne,c,h),x=m(ne+_e,d,f);s=(y-g)/2,i=(v-x)/2,r=-(y+g)/2,o=-(v+x)/2}return{ratioX:s,ratioY:i,offsetX:r,offsetY:o}}class Ss extends Kn{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const s=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=s;else{let r=l=>+s[l];if(Z(s[e])){const{key:l="value"}=this._parsing;r=c=>+es(s[c],l)}let o,a;for(o=e,a=e+n;o<a;++o)i._parsed[o]=r(o)}}_getRotation(){return Ot(this.options.rotation-90)}_getCircumference(){return Ot(this.options.circumference)}_getRotationExtents(){let e=fe,n=-fe;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const i=this.chart.getDatasetMeta(s).controller,r=i._getRotation(),o=i._getCircumference();e=Math.min(e,r),n=Math.max(n,r+o)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:s}=n,i=this._cachedMeta,r=i.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-o)/2,0),l=Math.min(Rb(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:p,offsetX:m,offsetY:y}=j_(h,d,l),v=(s.width-o)/f,g=(s.height-o)/p,x=Math.max(Math.min(v,g)/2,0),b=xg(this.options.radius,x),k=Math.max(b*l,0),_=(b-k)/this._getVisibleDatasetWeightTotal();this.offsetX=m*b,this.offsetY=y*b,i.total=this.calculateTotal(),this.outerRadius=b-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*c,0),this.updateElements(r,0,r.length,e)}_circumference(e,n){const s=this.options,i=this._cachedMeta,r=this._getCircumference();return n&&s.animation.animateRotate||!this.chart.getDataVisibility(e)||i._parsed[e]===null||i.data[e].hidden?0:this.calculateCircumference(i._parsed[e]*r/fe)}updateElements(e,n,s,i){const r=i==="reset",o=this.chart,a=o.chartArea,c=o.options.animation,d=(a.left+a.right)/2,h=(a.top+a.bottom)/2,f=r&&c.animateScale,p=f?0:this.innerRadius,m=f?0:this.outerRadius,{sharedOptions:y,includeOptions:v}=this._getSharedOptions(n,i);let g=this._getRotation(),x;for(x=0;x<n;++x)g+=this._circumference(x,r);for(x=n;x<n+s;++x){const b=this._circumference(x,r),k=e[x],_={x:d+this.offsetX,y:h+this.offsetY,startAngle:g,endAngle:g+b,circumference:b,outerRadius:m,innerRadius:p};v&&(_.options=y||this.resolveDataElementOptions(x,k.active?"active":i)),g+=b,this.updateElement(k,x,_,i)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let s=0,i;for(i=0;i<n.length;i++){const r=e._parsed[i];r!==null&&!isNaN(r)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(s+=Math.abs(r))}return s}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?fe*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,s=this.chart,i=s.data.labels||[],r=va(n._parsed[e],s.options.locale);return{label:i[e]||"",value:r}}getMaxBorderWidth(e){let n=0;const s=this.chart;let i,r,o,a,l;if(!e){for(i=0,r=s.data.datasets.length;i<r;++i)if(s.isDatasetVisible(i)){o=s.getDatasetMeta(i),e=o.data,a=o.controller;break}}if(!e)return 0;for(i=0,r=e.length;i<r;++i)l=a.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let s=0,i=e.length;s<i;++s){const r=this.resolveDataElementOptions(s);n=Math.max(n,r.offset||0,r.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let s=0;s<e;++s)this.chart.isDatasetVisible(s)&&(n+=this._getRingWeight(s));return n}_getRingWeight(e){return Math.max(X(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}F(Ss,"id","doughnut"),F(Ss,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),F(Ss,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),F(Ss,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:s,color:i}}=e.legend.options;return n.labels.map((r,o)=>{const l=e.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:s,hidden:!e.getDataVisibility(o),index:o}})}return[]}},onClick(e,n,s){s.chart.toggleDataVisibility(n.index),s.chart.update()}}}});class oo extends Kn{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(e){const n=this._cachedMeta,{dataset:s,data:i=[],_dataset:r}=n,o=this.chart._animationsDisabled;let{start:a,count:l}=Qb(n,i,o);this._drawStart=a,this._drawCount=l,Gb(n)&&(a=0,l=i.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!r._decimated,s.points=i;const c=this.resolveDatasetElementOptions(e);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!o,options:c},e),this.updateElements(i,a,l,e)}updateElements(e,n,s,i){const r=i==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:d,includeOptions:h}=this._getSharedOptions(n,i),f=o.axis,p=a.axis,{spanGaps:m,segment:y}=this.options,v=Ji(m)?m:Number.POSITIVE_INFINITY,g=this.chart._animationsDisabled||r||i==="none",x=n+s,b=e.length;let k=n>0&&this.getParsed(n-1);for(let _=0;_<b;++_){const C=e[_],N=g?C:{};if(_<n||_>=x){N.skip=!0;continue}const S=this.getParsed(_),L=J(S[p]),R=N[f]=o.getPixelForValue(S[f],_),I=N[p]=r||L?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,S,l):S[p],_);N.skip=isNaN(R)||isNaN(I)||L,N.stop=_>0&&Math.abs(S[f]-k[f])>v,y&&(N.parsed=S,N.raw=c.data[_]),h&&(N.options=d||this.resolveDataElementOptions(_,C.active?"active":i)),g||this.updateElement(C,_,N,i),k=S}}getMaxOverflow(){const e=this._cachedMeta,n=e.dataset,s=n.options&&n.options.borderWidth||0,i=e.data||[];if(!i.length)return s;const r=i[0].size(this.resolveDataElementOptions(0)),o=i[i.length-1].size(this.resolveDataElementOptions(i.length-1));return Math.max(s,r,o)/2}draw(){const e=this._cachedMeta;e.dataset.updateControlPoints(this.chart.chartArea,e.iScale.axis),super.draw()}}F(oo,"id","line"),F(oo,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),F(oo,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class vc extends Ss{}F(vc,"id","pie"),F(vc,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});function Tn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Hu{constructor(e){F(this,"options");this.options=e||{}}static override(e){Object.assign(Hu.prototype,e)}init(){}formats(){return Tn()}parse(){return Tn()}format(){return Tn()}add(){return Tn()}diff(){return Tn()}startOf(){return Tn()}endOf(){return Tn()}}var C_={_date:Hu};function N_(t,e,n,s){const{controller:i,data:r,_sorted:o}=t,a=i._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(a&&e===a.axis&&e!=="r"&&o&&r.length){const c=a._reversePixels?Vb:Wn;if(s){if(i._sharedOptions){const d=r[0],h=typeof d.getRange=="function"&&d.getRange(e);if(h){const f=c(r,e,n-h),p=c(r,e,n+h);return{lo:f.lo,hi:p.hi}}}}else{const d=c(r,e,n);if(l){const{vScale:h}=i._cachedMeta,{_parsed:f}=t,p=f.slice(0,d.lo+1).reverse().findIndex(y=>!J(y[h.axis]));d.lo-=Math.max(0,p);const m=f.slice(d.hi).findIndex(y=>!J(y[h.axis]));d.hi+=Math.max(0,m)}return d}}return{lo:0,hi:r.length-1}}function hr(t,e,n,s,i){const r=t.getSortedVisibleDatasetMetas(),o=n[e];for(let a=0,l=r.length;a<l;++a){const{index:c,data:d}=r[a],{lo:h,hi:f}=N_(r[a],e,o,i);for(let p=h;p<=f;++p){const m=d[p];m.skip||s(m,c,p)}}}function E_(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(s,i){const r=e?Math.abs(s.x-i.x):0,o=n?Math.abs(s.y-i.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}function hl(t,e,n,s,i){const r=[];return!i&&!t.isPointInArea(e)||hr(t,n,e,function(a,l,c){!i&&!Vt(a,t.chartArea,0)||a.inRange(e.x,e.y,s)&&r.push({element:a,datasetIndex:l,index:c})},!0),r}function P_(t,e,n,s){let i=[];function r(o,a,l){const{startAngle:c,endAngle:d}=o.getProps(["startAngle","endAngle"],s),{angle:h}=_g(o,{x:e.x,y:e.y});Zi(h,c,d)&&i.push({element:o,datasetIndex:a,index:l})}return hr(t,n,e,r),i}function M_(t,e,n,s,i,r){let o=[];const a=E_(n);let l=Number.POSITIVE_INFINITY;function c(d,h,f){const p=d.inRange(e.x,e.y,i);if(s&&!p)return;const m=d.getCenterPoint(i);if(!(!!r||t.isPointInArea(m))&&!p)return;const v=a(e,m);v<l?(o=[{element:d,datasetIndex:h,index:f}],l=v):v===l&&o.push({element:d,datasetIndex:h,index:f})}return hr(t,n,e,c),o}function fl(t,e,n,s,i,r){return!r&&!t.isPointInArea(e)?[]:n==="r"&&!s?P_(t,e,n,i):M_(t,e,n,s,i,r)}function Vh(t,e,n,s,i){const r=[],o=n==="x"?"inXRange":"inYRange";let a=!1;return hr(t,n,e,(l,c,d)=>{l[o]&&l[o](e[n],i)&&(r.push({element:l,datasetIndex:c,index:d}),a=a||l.inRange(e.x,e.y,i))}),s&&!a?[]:r}var T_={evaluateInteractionItems:hr,modes:{index(t,e,n,s){const i=An(e,t),r=n.axis||"x",o=n.includeInvisible||!1,a=n.intersect?hl(t,i,r,s,o):fl(t,i,r,!1,s,o),l=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach(c=>{const d=a[0].index,h=c.data[d];h&&!h.skip&&l.push({element:h,datasetIndex:c.index,index:d})}),l):[]},dataset(t,e,n,s){const i=An(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;let a=n.intersect?hl(t,i,r,s,o):fl(t,i,r,!1,s,o);if(a.length>0){const l=a[0].datasetIndex,c=t.getDatasetMeta(l).data;a=[];for(let d=0;d<c.length;++d)a.push({element:c[d],datasetIndex:l,index:d})}return a},point(t,e,n,s){const i=An(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;return hl(t,i,r,s,o)},nearest(t,e,n,s){const i=An(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;return fl(t,i,r,n.intersect,s,o)},x(t,e,n,s){const i=An(e,t);return Vh(t,i,"x",n.intersect,s)},y(t,e,n,s){const i=An(e,t);return Vh(t,i,"y",n.intersect,s)}}};const zg=["left","top","right","bottom"];function si(t,e){return t.filter(n=>n.pos===e)}function Yh(t,e){return t.filter(n=>zg.indexOf(n.pos)===-1&&n.box.axis===e)}function ii(t,e){return t.sort((n,s)=>{const i=e?s:n,r=e?n:s;return i.weight===r.weight?i.index-r.index:i.weight-r.weight})}function R_(t){const e=[];let n,s,i,r,o,a;for(n=0,s=(t||[]).length;n<s;++n)i=t[n],{position:r,options:{stack:o,stackWeight:a=1}}=i,e.push({index:n,box:i,pos:r,horizontal:i.isHorizontal(),weight:i.weight,stack:o&&r+o,stackWeight:a});return e}function O_(t){const e={};for(const n of t){const{stack:s,pos:i,stackWeight:r}=n;if(!s||!zg.includes(i))continue;const o=e[s]||(e[s]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return e}function D_(t,e){const n=O_(t),{vBoxMaxWidth:s,hBoxMaxHeight:i}=e;let r,o,a;for(r=0,o=t.length;r<o;++r){a=t[r];const{fullSize:l}=a.box,c=n[a.stack],d=c&&a.stackWeight/c.weight;a.horizontal?(a.width=d?d*s:l&&e.availableWidth,a.height=i):(a.width=s,a.height=d?d*i:l&&e.availableHeight)}return n}function A_(t){const e=R_(t),n=ii(e.filter(c=>c.box.fullSize),!0),s=ii(si(e,"left"),!0),i=ii(si(e,"right")),r=ii(si(e,"top"),!0),o=ii(si(e,"bottom")),a=Yh(e,"x"),l=Yh(e,"y");return{fullSize:n,leftAndTop:s.concat(r),rightAndBottom:i.concat(l).concat(o).concat(a),chartArea:si(e,"chartArea"),vertical:s.concat(i).concat(l),horizontal:r.concat(o).concat(a)}}function Xh(t,e,n,s){return Math.max(t[n],e[n])+Math.max(t[s],e[s])}function Wg(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function L_(t,e,n,s){const{pos:i,box:r}=n,o=t.maxPadding;if(!Z(i)){n.size&&(t[i]-=n.size);const h=s[n.stack]||{size:0,count:1};h.size=Math.max(h.size,n.horizontal?r.height:r.width),n.size=h.size/h.count,t[i]+=n.size}r.getPadding&&Wg(o,r.getPadding());const a=Math.max(0,e.outerWidth-Xh(o,t,"left","right")),l=Math.max(0,e.outerHeight-Xh(o,t,"top","bottom")),c=a!==t.w,d=l!==t.h;return t.w=a,t.h=l,n.horizontal?{same:c,other:d}:{same:d,other:c}}function F_(t){const e=t.maxPadding;function n(s){const i=Math.max(e[s]-t[s],0);return t[s]+=i,i}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function B_(t,e){const n=e.maxPadding;function s(i){const r={left:0,top:0,right:0,bottom:0};return i.forEach(o=>{r[o]=Math.max(e[o],n[o])}),r}return s(t?["left","right"]:["top","bottom"])}function fi(t,e,n,s){const i=[];let r,o,a,l,c,d;for(r=0,o=t.length,c=0;r<o;++r){a=t[r],l=a.box,l.update(a.width||e.w,a.height||e.h,B_(a.horizontal,e));const{same:h,other:f}=L_(e,n,a,s);c|=h&&i.length,d=d||f,l.fullSize||i.push(a)}return c&&fi(i,e,n,s)||d}function zr(t,e,n,s,i){t.top=n,t.left=e,t.right=e+s,t.bottom=n+i,t.width=s,t.height=i}function Kh(t,e,n,s){const i=n.padding;let{x:r,y:o}=e;for(const a of t){const l=a.box,c=s[a.stack]||{count:1,placed:0,weight:1},d=a.stackWeight/c.weight||1;if(a.horizontal){const h=e.w*d,f=c.size||l.height;Gi(c.start)&&(o=c.start),l.fullSize?zr(l,i.left,o,n.outerWidth-i.right-i.left,f):zr(l,e.left+c.placed,o,h,f),c.start=o,c.placed+=h,o=l.bottom}else{const h=e.h*d,f=c.size||l.width;Gi(c.start)&&(r=c.start),l.fullSize?zr(l,r,i.top,f,n.outerHeight-i.bottom-i.top):zr(l,r,e.top+c.placed,f,h),c.start=r,c.placed+=h,r=l.right}}e.x=r,e.y=o}var ht={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,s){if(!t)return;const i=We(t.options.layout.padding),r=Math.max(e-i.width,0),o=Math.max(n-i.height,0),a=A_(t.boxes),l=a.vertical,c=a.horizontal;se(t.boxes,y=>{typeof y.beforeLayout=="function"&&y.beforeLayout()});const d=l.reduce((y,v)=>v.box.options&&v.box.options.display===!1?y:y+1,0)||1,h=Object.freeze({outerWidth:e,outerHeight:n,padding:i,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/d,hBoxMaxHeight:o/2}),f=Object.assign({},i);Wg(f,We(s));const p=Object.assign({maxPadding:f,w:r,h:o,x:i.left,y:i.top},i),m=D_(l.concat(c),h);fi(a.fullSize,p,h,m),fi(l,p,h,m),fi(c,p,h,m)&&fi(l,p,h,m),F_(p),Kh(a.leftAndTop,p,h,m),p.x+=p.w,p.y+=p.h,Kh(a.rightAndBottom,p,h,m),t.chartArea={left:p.left,top:p.top,right:p.left+p.w,bottom:p.top+p.h,height:p.h,width:p.w},se(a.chartArea,y=>{const v=y.box;Object.assign(v,t.chartArea),v.update(p.w,p.h,{left:0,top:0,right:0,bottom:0})})}};class $g{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,s){}removeEventListener(e,n,s){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,s,i){return n=Math.max(0,n||e.width),s=s||e.height,{width:n,height:Math.max(0,i?Math.floor(n/i):s)}}isAttached(e){return!0}updateConfig(e){}}class I_ extends $g{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const ao="$chartjs",z_={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},qh=t=>t===null||t==="";function W_(t,e){const n=t.style,s=t.getAttribute("height"),i=t.getAttribute("width");if(t[ao]={initial:{height:s,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",qh(i)){const r=Rh(t,"width");r!==void 0&&(t.width=r)}if(qh(s))if(t.style.height==="")t.height=t.width/(e||2);else{const r=Rh(t,"height");r!==void 0&&(t.height=r)}return t}const Hg=z1?{passive:!0}:!1;function $_(t,e,n){t&&t.addEventListener(e,n,Hg)}function H_(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,Hg)}function U_(t,e){const n=z_[t.type]||t.type,{x:s,y:i}=An(t,e);return{type:n,chart:e,native:t,x:s!==void 0?s:null,y:i!==void 0?i:null}}function Vo(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function V_(t,e,n){const s=t.canvas,i=new MutationObserver(r=>{let o=!1;for(const a of r)o=o||Vo(a.addedNodes,s),o=o&&!Vo(a.removedNodes,s);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function Y_(t,e,n){const s=t.canvas,i=new MutationObserver(r=>{let o=!1;for(const a of r)o=o||Vo(a.removedNodes,s),o=o&&!Vo(a.addedNodes,s);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const tr=new Map;let Qh=0;function Ug(){const t=window.devicePixelRatio;t!==Qh&&(Qh=t,tr.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function X_(t,e){tr.size||window.addEventListener("resize",Ug),tr.set(t,e)}function K_(t){tr.delete(t),tr.size||window.removeEventListener("resize",Ug)}function q_(t,e,n){const s=t.canvas,i=s&&$u(s);if(!i)return;const r=jg((a,l)=>{const c=i.clientWidth;n(a,l),c<i.clientWidth&&n()},window),o=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,d=l.contentRect.height;c===0&&d===0||r(c,d)});return o.observe(i),X_(t,r),o}function pl(t,e,n){n&&n.disconnect(),e==="resize"&&K_(t)}function Q_(t,e,n){const s=t.canvas,i=jg(r=>{t.ctx!==null&&n(U_(r,t))},t);return $_(s,e,i),i}class G_ extends $g{acquireContext(e,n){const s=e&&e.getContext&&e.getContext("2d");return s&&s.canvas===e?(W_(e,n),s):null}releaseContext(e){const n=e.canvas;if(!n[ao])return!1;const s=n[ao].initial;["height","width"].forEach(r=>{const o=s[r];J(o)?n.removeAttribute(r):n.setAttribute(r,o)});const i=s.style||{};return Object.keys(i).forEach(r=>{n.style[r]=i[r]}),n.width=n.width,delete n[ao],!0}addEventListener(e,n,s){this.removeEventListener(e,n);const i=e.$proxies||(e.$proxies={}),o={attach:V_,detach:Y_,resize:q_}[n]||Q_;i[n]=o(e,n,s)}removeEventListener(e,n){const s=e.$proxies||(e.$proxies={}),i=s[n];if(!i)return;({attach:pl,detach:pl,resize:pl}[n]||H_)(e,n,i),s[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,s,i){return I1(e,n,s,i)}isAttached(e){const n=e&&$u(e);return!!(n&&n.isConnected)}}function J_(t){return!Wu()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?I_:G_}class St{constructor(){F(this,"x");F(this,"y");F(this,"active",!1);F(this,"options");F(this,"$animations")}tooltipPosition(e){const{x:n,y:s}=this.getProps(["x","y"],e);return{x:n,y:s}}hasValue(){return Ji(this.x)&&Ji(this.y)}getProps(e,n){const s=this.$animations;if(!n||!s)return this;const i={};return e.forEach(r=>{i[r]=s[r]&&s[r].active()?s[r]._to:this[r]}),i}}F(St,"defaults",{}),F(St,"defaultRoutes");function Z_(t,e){const n=t.options.ticks,s=ek(t),i=Math.min(n.maxTicksLimit||s,s),r=n.major.enabled?nk(e):[],o=r.length,a=r[0],l=r[o-1],c=[];if(o>i)return sk(e,c,r,o/i),c;const d=tk(r,e,i);if(o>0){let h,f;const p=o>1?Math.round((l-a)/(o-1)):null;for(Wr(e,c,d,J(p)?0:a-p,a),h=0,f=o-1;h<f;h++)Wr(e,c,d,r[h],r[h+1]);return Wr(e,c,d,l,J(p)?e.length:l+p),c}return Wr(e,c,d),c}function ek(t){const e=t.options.offset,n=t._tickSize(),s=t._length/n+(e?0:1),i=t._maxLength/n;return Math.floor(Math.min(s,i))}function tk(t,e,n){const s=ik(t),i=e.length/n;if(!s)return Math.max(i,1);const r=zb(s);for(let o=0,a=r.length-1;o<a;o++){const l=r[o];if(l>i)return l}return Math.max(i,1)}function nk(t){const e=[];let n,s;for(n=0,s=t.length;n<s;n++)t[n].major&&e.push(n);return e}function sk(t,e,n,s){let i=0,r=n[0],o;for(s=Math.ceil(s),o=0;o<t.length;o++)o===r&&(e.push(t[o]),i++,r=n[i*s])}function Wr(t,e,n,s,i){const r=X(s,0),o=Math.min(X(i,t.length),t.length);let a=0,l,c,d;for(n=Math.ceil(n),i&&(l=i-s,n=l/Math.floor(l/n)),d=r;d<0;)a++,d=Math.round(r+a*n);for(c=Math.max(r,0);c<o;c++)c===d&&(e.push(t[c]),a++,d=Math.round(r+a*n))}function ik(t){const e=t.length;let n,s;if(e<2)return!1;for(s=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==s)return!1;return s}const rk=t=>t==="left"?"right":t==="right"?"left":t,Gh=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,Jh=(t,e)=>Math.min(e||t,t);function Zh(t,e){const n=[],s=t.length/e,i=t.length;let r=0;for(;r<i;r+=s)n.push(t[Math.floor(r)]);return n}function ok(t,e,n){const s=t.ticks.length,i=Math.min(e,s-1),r=t._startPixel,o=t._endPixel,a=1e-6;let l=t.getPixelForTick(i),c;if(!(n&&(s===1?c=Math.max(l-r,o-l):e===0?c=(t.getPixelForTick(1)-l)/2:c=(l-t.getPixelForTick(i-1))/2,l+=i<e?c:-c,l<r-a||l>o+a)))return l}function ak(t,e){se(t,n=>{const s=n.gc,i=s.length/2;let r;if(i>e){for(r=0;r<i;++r)delete n.data[s[r]];s.splice(0,i)}})}function ri(t){return t.drawTicks?t.tickLength:0}function ef(t,e){if(!t.display)return 0;const n=Ne(t.font,e),s=We(t.padding);return(he(t.text)?t.text.length:1)*n.lineHeight+s.height}function lk(t,e){return En(t,{scale:e,type:"scale"})}function ck(t,e,n){return En(t,{tick:n,index:e,type:"tick"})}function uk(t,e,n){let s=Ou(t);return(n&&e!=="right"||!n&&e==="right")&&(s=rk(s)),s}function dk(t,e,n,s){const{top:i,left:r,bottom:o,right:a,chart:l}=t,{chartArea:c,scales:d}=l;let h=0,f,p,m;const y=o-i,v=a-r;if(t.isHorizontal()){if(p=Le(s,r,a),Z(n)){const g=Object.keys(n)[0],x=n[g];m=d[g].getPixelForValue(x)+y-e}else n==="center"?m=(c.bottom+c.top)/2+y-e:m=Gh(t,n,e);f=a-r}else{if(Z(n)){const g=Object.keys(n)[0],x=n[g];p=d[g].getPixelForValue(x)-v+e}else n==="center"?p=(c.left+c.right)/2-v+e:p=Gh(t,n,e);m=Le(s,o,i),h=n==="left"?-_e:_e}return{titleX:p,titleY:m,maxWidth:f,rotation:h}}class as extends St{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:s,_suggestedMax:i}=this;return e=tt(e,Number.POSITIVE_INFINITY),n=tt(n,Number.NEGATIVE_INFINITY),s=tt(s,Number.POSITIVE_INFINITY),i=tt(i,Number.NEGATIVE_INFINITY),{min:tt(e,s),max:tt(n,i),minDefined:Pe(e),maxDefined:Pe(n)}}getMinMax(e){let{min:n,max:s,minDefined:i,maxDefined:r}=this.getUserBounds(),o;if(i&&r)return{min:n,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)o=a[l].controller.getMinMax(this,e),i||(n=Math.min(n,o.min)),r||(s=Math.max(s,o.max));return n=r&&n>s?s:n,s=i&&n>s?n:s,{min:tt(n,tt(s,n)),max:tt(s,tt(n,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){oe(this.options.beforeUpdate,[this])}update(e,n,s){const{beginAtZero:i,grace:r,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=y1(this,r,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Zh(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=Z_(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,s;this.isHorizontal()?(n=this.left,s=this.right):(n=this.top,s=this.bottom,e=!e),this._startPixel=n,this._endPixel=s,this._reversePixels=e,this._length=s-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){oe(this.options.afterUpdate,[this])}beforeSetDimensions(){oe(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){oe(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),oe(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){oe(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let s,i,r;for(s=0,i=e.length;s<i;s++)r=e[s],r.label=oe(n.callback,[r.value,s,e],this)}afterTickToLabelConversion(){oe(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){oe(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,s=Jh(this.ticks.length,e.ticks.maxTicksLimit),i=n.minRotation||0,r=n.maxRotation;let o=i,a,l,c;if(!this._isVisible()||!n.display||i>=r||s<=1||!this.isHorizontal()){this.labelRotation=i;return}const d=this._getLabelSizes(),h=d.widest.width,f=d.highest.height,p=Ie(this.chart.width-h,0,this.maxWidth);a=e.offset?this.maxWidth/s:p/(s-1),h+6>a&&(a=p/(s-(e.offset?.5:1)),l=this.maxHeight-ri(e.grid)-n.padding-ef(e.title,this.chart.options.font),c=Math.sqrt(h*h+f*f),o=Tu(Math.min(Math.asin(Ie((d.highest.height+6)/a,-1,1)),Math.asin(Ie(l/c,-1,1))-Math.asin(Ie(f/c,-1,1)))),o=Math.max(i,Math.min(r,o))),this.labelRotation=o}afterCalculateLabelRotation(){oe(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){oe(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:s,title:i,grid:r}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){const l=ef(i,n.options.font);if(a?(e.width=this.maxWidth,e.height=ri(r)+l):(e.height=this.maxHeight,e.width=ri(r)+l),s.display&&this.ticks.length){const{first:c,last:d,widest:h,highest:f}=this._getLabelSizes(),p=s.padding*2,m=Ot(this.labelRotation),y=Math.cos(m),v=Math.sin(m);if(a){const g=s.mirror?0:v*h.width+y*f.height;e.height=Math.min(this.maxHeight,e.height+g+p)}else{const g=s.mirror?0:y*h.width+v*f.height;e.width=Math.min(this.maxWidth,e.width+g+p)}this._calculatePadding(c,d,v,y)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,s,i){const{ticks:{align:r,padding:o},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,p=0;l?c?(f=i*e.width,p=s*n.height):(f=s*e.height,p=i*n.width):r==="start"?p=n.width:r==="end"?f=e.width:r!=="inner"&&(f=e.width/2,p=n.width/2),this.paddingLeft=Math.max((f-d+o)*this.width/(this.width-d),0),this.paddingRight=Math.max((p-h+o)*this.width/(this.width-h),0)}else{let d=n.height/2,h=e.height/2;r==="start"?(d=0,h=e.height):r==="end"&&(d=n.height,h=0),this.paddingTop=d+o,this.paddingBottom=h+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){oe(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,s;for(n=0,s=e.length;n<s;n++)J(e[n].label)&&(e.splice(n,1),s--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let s=this.ticks;n<s.length&&(s=Zh(s,n)),this._labelSizes=e=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,s){const{ctx:i,_longestTextCache:r}=this,o=[],a=[],l=Math.floor(n/Jh(n,s));let c=0,d=0,h,f,p,m,y,v,g,x,b,k,_;for(h=0;h<n;h+=l){if(m=e[h].label,y=this._resolveTickFontOptions(h),i.font=v=y.string,g=r[v]=r[v]||{data:{},gc:[]},x=y.lineHeight,b=k=0,!J(m)&&!he(m))b=Ho(i,g.data,g.gc,b,m),k=x;else if(he(m))for(f=0,p=m.length;f<p;++f)_=m[f],!J(_)&&!he(_)&&(b=Ho(i,g.data,g.gc,b,_),k+=x);o.push(b),a.push(k),c=Math.max(b,c),d=Math.max(k,d)}ak(r,n);const C=o.indexOf(c),N=a.indexOf(d),S=L=>({width:o[L]||0,height:a[L]||0});return{first:S(0),last:S(n-1),widest:S(C),highest:S(N),widths:o,heights:a}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return Ub(this._alignToPixels?Mn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const s=n[e];return s.$context||(s.$context=ck(this.getContext(),e,s))}return this.$context||(this.$context=lk(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=Ot(this.labelRotation),s=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),r=this._getLabelSizes(),o=e.autoSkipPadding||0,a=r?r.widest.width+o:0,l=r?r.highest.height+o:0;return this.isHorizontal()?l*s>a*i?a/s:l/i:l*i<a*s?l/s:a/i}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,s=this.chart,i=this.options,{grid:r,position:o,border:a}=i,l=r.offset,c=this.isHorizontal(),h=this.ticks.length+(l?1:0),f=ri(r),p=[],m=a.setContext(this.getContext()),y=m.display?m.width:0,v=y/2,g=function(K){return Mn(s,K,y)};let x,b,k,_,C,N,S,L,R,I,M,H;if(o==="top")x=g(this.bottom),N=this.bottom-f,L=x-v,I=g(e.top)+v,H=e.bottom;else if(o==="bottom")x=g(this.top),I=e.top,H=g(e.bottom)-v,N=x+v,L=this.top+f;else if(o==="left")x=g(this.right),C=this.right-f,S=x-v,R=g(e.left)+v,M=e.right;else if(o==="right")x=g(this.left),R=e.left,M=g(e.right)-v,C=x+v,S=this.left+f;else if(n==="x"){if(o==="center")x=g((e.top+e.bottom)/2+.5);else if(Z(o)){const K=Object.keys(o)[0],ee=o[K];x=g(this.chart.scales[K].getPixelForValue(ee))}I=e.top,H=e.bottom,N=x+v,L=N+f}else if(n==="y"){if(o==="center")x=g((e.left+e.right)/2);else if(Z(o)){const K=Object.keys(o)[0],ee=o[K];x=g(this.chart.scales[K].getPixelForValue(ee))}C=x-v,S=C-f,R=e.left,M=e.right}const U=X(i.ticks.maxTicksLimit,h),z=Math.max(1,Math.ceil(h/U));for(b=0;b<h;b+=z){const K=this.getContext(b),ee=r.setContext(K),D=a.setContext(K),j=ee.lineWidth,O=ee.color,W=D.dash||[],$=D.dashOffset,Q=ee.tickWidth,re=ee.tickColor,ve=ee.tickBorderDash||[],w=ee.tickBorderDashOffset;k=ok(this,b,l),k!==void 0&&(_=Mn(s,k,j),c?C=S=R=M=_:N=L=I=H=_,p.push({tx1:C,ty1:N,tx2:S,ty2:L,x1:R,y1:I,x2:M,y2:H,width:j,color:O,borderDash:W,borderDashOffset:$,tickWidth:Q,tickColor:re,tickBorderDash:ve,tickBorderDashOffset:w}))}return this._ticksLength=h,this._borderValue=x,p}_computeLabelItems(e){const n=this.axis,s=this.options,{position:i,ticks:r}=s,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:d,mirror:h}=r,f=ri(s.grid),p=f+d,m=h?-d:p,y=-Ot(this.labelRotation),v=[];let g,x,b,k,_,C,N,S,L,R,I,M,H="middle";if(i==="top")C=this.bottom-m,N=this._getXAxisLabelAlignment();else if(i==="bottom")C=this.top+m,N=this._getXAxisLabelAlignment();else if(i==="left"){const z=this._getYAxisLabelAlignment(f);N=z.textAlign,_=z.x}else if(i==="right"){const z=this._getYAxisLabelAlignment(f);N=z.textAlign,_=z.x}else if(n==="x"){if(i==="center")C=(e.top+e.bottom)/2+p;else if(Z(i)){const z=Object.keys(i)[0],K=i[z];C=this.chart.scales[z].getPixelForValue(K)+p}N=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")_=(e.left+e.right)/2-p;else if(Z(i)){const z=Object.keys(i)[0],K=i[z];_=this.chart.scales[z].getPixelForValue(K)}N=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(l==="start"?H="top":l==="end"&&(H="bottom"));const U=this._getLabelSizes();for(g=0,x=a.length;g<x;++g){b=a[g],k=b.label;const z=r.setContext(this.getContext(g));S=this.getPixelForTick(g)+r.labelOffset,L=this._resolveTickFontOptions(g),R=L.lineHeight,I=he(k)?k.length:1;const K=I/2,ee=z.color,D=z.textStrokeColor,j=z.textStrokeWidth;let O=N;o?(_=S,N==="inner"&&(g===x-1?O=this.options.reverse?"left":"right":g===0?O=this.options.reverse?"right":"left":O="center"),i==="top"?c==="near"||y!==0?M=-I*R+R/2:c==="center"?M=-U.highest.height/2-K*R+R:M=-U.highest.height+R/2:c==="near"||y!==0?M=R/2:c==="center"?M=U.highest.height/2-K*R:M=U.highest.height-I*R,h&&(M*=-1),y!==0&&!z.showLabelBackdrop&&(_+=R/2*Math.sin(y))):(C=S,M=(1-I)*R/2);let W;if(z.showLabelBackdrop){const $=We(z.backdropPadding),Q=U.heights[g],re=U.widths[g];let ve=M-$.top,w=0-$.left;switch(H){case"middle":ve-=Q/2;break;case"bottom":ve-=Q;break}switch(N){case"center":w-=re/2;break;case"right":w-=re;break;case"inner":g===x-1?w-=re:g>0&&(w-=re/2);break}W={left:w,top:ve,width:re+$.width,height:Q+$.height,color:z.backdropColor}}v.push({label:k,font:L,textOffset:M,options:{rotation:y,color:ee,strokeColor:D,strokeWidth:j,textAlign:O,textBaseline:H,translation:[_,C],backdrop:W}})}return v}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-Ot(this.labelRotation))return e==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:s,mirror:i,padding:r}}=this.options,o=this._getLabelSizes(),a=e+r,l=o.widest.width;let c,d;return n==="left"?i?(d=this.right+r,s==="near"?c="left":s==="center"?(c="center",d+=l/2):(c="right",d+=l)):(d=this.right-a,s==="near"?c="right":s==="center"?(c="center",d-=l/2):(c="left",d=this.left)):n==="right"?i?(d=this.left+r,s==="near"?c="right":s==="center"?(c="center",d-=l/2):(c="left",d-=l)):(d=this.left+a,s==="near"?c="left":s==="center"?(c="center",d+=l/2):(c="right",d=this.right)):c="right",{textAlign:c,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:s,top:i,width:r,height:o}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(s,i,r,o),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(r=>r.value===e);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(e){const n=this.options.grid,s=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let r,o;const a=(l,c,d)=>{!d.width||!d.color||(s.save(),s.lineWidth=d.width,s.strokeStyle=d.color,s.setLineDash(d.borderDash||[]),s.lineDashOffset=d.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(n.display)for(r=0,o=i.length;r<o;++r){const l=i[r];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:s,grid:i}}=this,r=s.setContext(this.getContext()),o=s.display?r.width:0;if(!o)return;const a=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,d,h,f;this.isHorizontal()?(c=Mn(e,this.left,o)-o/2,d=Mn(e,this.right,a)+a/2,h=f=l):(h=Mn(e,this.top,o)-o/2,f=Mn(e,this.bottom,a)+a/2,c=d=l),n.save(),n.lineWidth=r.width,n.strokeStyle=r.color,n.beginPath(),n.moveTo(c,h),n.lineTo(d,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const s=this.ctx,i=this._computeLabelArea();i&&Au(s,i);const r=this.getLabelItems(e);for(const o of r){const a=o.options,l=o.font,c=o.label,d=o.textOffset;ns(s,c,0,d,l,a)}i&&Lu(s)}drawTitle(){const{ctx:e,options:{position:n,title:s,reverse:i}}=this;if(!s.display)return;const r=Ne(s.font),o=We(s.padding),a=s.align;let l=r.lineHeight/2;n==="bottom"||n==="center"||Z(n)?(l+=o.bottom,he(s.text)&&(l+=r.lineHeight*(s.text.length-1))):l+=o.top;const{titleX:c,titleY:d,maxWidth:h,rotation:f}=dk(this,l,n,a);ns(e,s.text,0,0,r,{color:s.color,maxWidth:h,rotation:f,textAlign:uk(a,n,i),textBaseline:"middle",translation:[c,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,s=X(e.grid&&e.grid.z,-1),i=X(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==as.prototype.draw?[{z:n,draw:r=>{this.draw(r)}}]:[{z:s,draw:r=>{this.drawBackground(),this.drawGrid(r),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:r=>{this.drawLabels(r)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",i=[];let r,o;for(r=0,o=n.length;r<o;++r){const a=n[r];a[s]===this.id&&(!e||a.type===e)&&i.push(a)}return i}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return Ne(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class $r{constructor(e,n,s){this.type=e,this.scope=n,this.override=s,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let s;pk(n)&&(s=this.register(n));const i=this.items,r=e.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+e);return r in i||(i[r]=e,hk(e,o,s),this.override&&xe.override(e.id,e.overrides)),o}get(e){return this.items[e]}unregister(e){const n=this.items,s=e.id,i=this.scope;s in n&&delete n[s],i&&s in xe[i]&&(delete xe[i][s],this.override&&delete ts[s])}}function hk(t,e,n){const s=Qi(Object.create(null),[n?xe.get(n):{},xe.get(e),t.defaults]);xe.set(e,s),t.defaultRoutes&&fk(e,t.defaultRoutes),t.descriptors&&xe.describe(e,t.descriptors)}function fk(t,e){Object.keys(e).forEach(n=>{const s=n.split("."),i=s.pop(),r=[t].concat(s).join("."),o=e[n].split("."),a=o.pop(),l=o.join(".");xe.route(r,i,l,a)})}function pk(t){return"id"in t&&"defaults"in t}class mk{constructor(){this.controllers=new $r(Kn,"datasets",!0),this.elements=new $r(St,"elements"),this.plugins=new $r(Object,"plugins"),this.scales=new $r(as,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,s){[...n].forEach(i=>{const r=s||this._getRegistryForType(i);s||r.isForType(i)||r===this.plugins&&i.id?this._exec(e,r,i):se(i,o=>{const a=s||this._getRegistryForType(o);this._exec(e,a,o)})})}_exec(e,n,s){const i=Mu(e);oe(s["before"+i],[],s),n[e](s),oe(s["after"+i],[],s)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const s=this._typedRegistries[n];if(s.isForType(e))return s}return this.plugins}_get(e,n,s){const i=n.get(e);if(i===void 0)throw new Error('"'+e+'" is not a registered '+s+".");return i}}var Tt=new mk;class gk{constructor(){this._init=[]}notify(e,n,s,i){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const r=i?this._descriptors(e).filter(i):this._descriptors(e),o=this._notify(r,e,n,s);return n==="afterDestroy"&&(this._notify(r,e,"stop"),this._notify(this._init,e,"uninstall")),o}_notify(e,n,s,i){i=i||{};for(const r of e){const o=r.plugin,a=o[s],l=[n,i,r.options];if(oe(a,l,o)===!1&&i.cancelable)return!1}return!0}invalidate(){J(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const s=e&&e.config,i=X(s.options&&s.options.plugins,{}),r=yk(s);return i===!1&&!n?[]:vk(e,r,i,n)}_notifyStateChanges(e){const n=this._oldCache||[],s=this._cache,i=(r,o)=>r.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(i(n,s),e,"stop"),this._notify(i(s,n),e,"start")}}function yk(t){const e={},n=[],s=Object.keys(Tt.plugins.items);for(let r=0;r<s.length;r++)n.push(Tt.getPlugin(s[r]));const i=t.plugins||[];for(let r=0;r<i.length;r++){const o=i[r];n.indexOf(o)===-1&&(n.push(o),e[o.id]=!0)}return{plugins:n,localIds:e}}function xk(t,e){return!e&&t===!1?null:t===!0?{}:t}function vk(t,{plugins:e,localIds:n},s,i){const r=[],o=t.getContext();for(const a of e){const l=a.id,c=xk(s[l],i);c!==null&&r.push({plugin:a,options:bk(t.config,{plugin:a,local:n[l]},c,o)})}return r}function bk(t,{plugin:e,local:n},s,i){const r=t.pluginScopeKeys(e),o=t.getOptionScopes(s,r);return n&&e.defaults&&o.push(e.defaults),t.createResolver(o,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function bc(t,e){const n=xe.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function _k(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function kk(t,e){return t===e?"_index_":"_value_"}function tf(t){if(t==="x"||t==="y"||t==="r")return t}function wk(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function _c(t,...e){if(tf(t))return t;for(const n of e){const s=n.axis||wk(n.position)||t.length>1&&tf(t[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function nf(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function Sk(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(s=>s.xAxisID===t||s.yAxisID===t);if(n.length)return nf(t,"x",n[0])||nf(t,"y",n[0])}return{}}function jk(t,e){const n=ts[t.type]||{scales:{}},s=e.scales||{},i=bc(t.type,e),r=Object.create(null);return Object.keys(s).forEach(o=>{const a=s[o];if(!Z(a))return console.error(`Invalid scale configuration for scale: ${o}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const l=_c(o,a,Sk(o,t),xe.scales[a.type]),c=kk(l,i),d=n.scales||{};r[o]=ji(Object.create(null),[{axis:l},a,d[l],d[c]])}),t.data.datasets.forEach(o=>{const a=o.type||t.type,l=o.indexAxis||bc(a,e),d=(ts[a]||{}).scales||{};Object.keys(d).forEach(h=>{const f=_k(h,l),p=o[f+"AxisID"]||f;r[p]=r[p]||Object.create(null),ji(r[p],[{axis:f},s[p],d[h]])})}),Object.keys(r).forEach(o=>{const a=r[o];ji(a,[xe.scales[a.type],xe.scale])}),r}function Vg(t){const e=t.options||(t.options={});e.plugins=X(e.plugins,{}),e.scales=jk(t,e)}function Yg(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function Ck(t){return t=t||{},t.data=Yg(t.data),Vg(t),t}const sf=new Map,Xg=new Set;function Hr(t,e){let n=sf.get(t);return n||(n=e(),sf.set(t,n),Xg.add(n)),n}const oi=(t,e,n)=>{const s=es(e,n);s!==void 0&&t.add(s)};class Nk{constructor(e){this._config=Ck(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=Yg(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),Vg(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Hr(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return Hr(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return Hr(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,s=this.type;return Hr(`${s}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const s=this._scopeCache;let i=s.get(e);return(!i||n)&&(i=new Map,s.set(e,i)),i}getOptionScopes(e,n,s){const{options:i,type:r}=this,o=this._cachedScopes(e,s),a=o.get(n);if(a)return a;const l=new Set;n.forEach(d=>{e&&(l.add(e),d.forEach(h=>oi(l,e,h))),d.forEach(h=>oi(l,i,h)),d.forEach(h=>oi(l,ts[r]||{},h)),d.forEach(h=>oi(l,xe,h)),d.forEach(h=>oi(l,yc,h))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Xg.has(n)&&o.set(n,c),c}chartOptionScopes(){const{options:e,type:n}=this;return[e,ts[n]||{},xe.datasets[n]||{},{type:n},xe,yc]}resolveNamedOptions(e,n,s,i=[""]){const r={$shared:!0},{resolver:o,subPrefixes:a}=rf(this._resolverCache,e,i);let l=o;if(Pk(o,n)){r.$shared=!1,s=Sn(s)?s():s;const c=this.createResolver(e,s,a);l=zs(o,s,c)}for(const c of n)r[c]=l[c];return r}createResolver(e,n,s=[""],i){const{resolver:r}=rf(this._resolverCache,e,s);return Z(n)?zs(r,n,void 0,i):r}}function rf(t,e,n){let s=t.get(e);s||(s=new Map,t.set(e,s));const i=n.join();let r=s.get(i);return r||(r={resolver:Bu(e,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},s.set(i,r)),r}const Ek=t=>Z(t)&&Object.getOwnPropertyNames(t).some(e=>Sn(t[e]));function Pk(t,e){const{isScriptable:n,isIndexable:s}=Pg(t);for(const i of e){const r=n(i),o=s(i),a=(o||r)&&t[i];if(r&&(Sn(a)||Ek(a))||o&&he(a))return!0}return!1}var Mk="4.5.0";const Tk=["top","bottom","left","right","chartArea"];function of(t,e){return t==="top"||t==="bottom"||Tk.indexOf(t)===-1&&e==="x"}function af(t,e){return function(n,s){return n[t]===s[t]?n[e]-s[e]:n[t]-s[t]}}function lf(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),oe(n&&n.onComplete,[t],e)}function Rk(t){const e=t.chart,n=e.options.animation;oe(n&&n.onProgress,[t],e)}function Kg(t){return Wu()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const lo={},cf=t=>{const e=Kg(t);return Object.values(lo).filter(n=>n.canvas===e).pop()};function Ok(t,e,n){const s=Object.keys(t);for(const i of s){const r=+i;if(r>=e){const o=t[i];delete t[i],(n>0||r>e)&&(t[r+n]=o)}}}function Dk(t,e,n,s){return!n||t.type==="mouseout"?null:s?e:t}var en;let fr=(en=class{static register(...e){Tt.add(...e),uf()}static unregister(...e){Tt.remove(...e),uf()}constructor(e,n){const s=this.config=new Nk(n),i=Kg(e),r=cf(i);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const o=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||J_(i)),this.platform.updateConfig(s);const a=this.platform.acquireContext(i,o.aspectRatio),l=a&&a.canvas,c=l&&l.height,d=l&&l.width;if(this.id=Tb(),this.ctx=a,this.canvas=l,this.width=d,this.height=c,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new gk,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Kb(h=>this.update(h),o.resizeDelay||0),this._dataChanges=[],lo[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Bt.listen(this,"complete",lf),Bt.listen(this,"progress",Rk),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:s,height:i,_aspectRatio:r}=this;return J(e)?n&&r?r:i?s/i:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return Tt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Th(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Eh(this.canvas,this.ctx),this}stop(){return Bt.stop(this),this}resize(e,n){Bt.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const s=this.options,i=this.canvas,r=s.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(i,e,n,r),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,Th(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),oe(s.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};se(n,(s,i)=>{s.id=i})}buildOrUpdateScales(){const e=this.options,n=e.scales,s=this.scales,i=Object.keys(s).reduce((o,a)=>(o[a]=!1,o),{});let r=[];n&&(r=r.concat(Object.keys(n).map(o=>{const a=n[o],l=_c(o,a),c=l==="r",d=l==="x";return{options:a,dposition:c?"chartArea":d?"bottom":"left",dtype:c?"radialLinear":d?"category":"linear"}}))),se(r,o=>{const a=o.options,l=a.id,c=_c(l,a),d=X(a.type,o.dtype);(a.position===void 0||of(a.position,c)!==of(o.dposition))&&(a.position=o.dposition),i[l]=!0;let h=null;if(l in s&&s[l].type===d)h=s[l];else{const f=Tt.getScale(d);h=new f({id:l,type:d,ctx:this.ctx,chart:this}),s[h.id]=h}h.init(a,e)}),se(i,(o,a)=>{o||delete s[a]}),se(s,o=>{ht.configure(this,o,o.options),ht.addBox(this,o)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,s=e.length;if(e.sort((i,r)=>i.index-r.index),s>n){for(let i=n;i<s;++i)this._destroyDatasetMeta(i);e.splice(n,s-n)}this._sortedMetasets=e.slice(0).sort(af("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((s,i)=>{n.filter(r=>r===s._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let s,i;for(this._removeUnreferencedMetasets(),s=0,i=n.length;s<i;s++){const r=n[s];let o=this.getDatasetMeta(s);const a=r.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(s),o=this.getDatasetMeta(s)),o.type=a,o.indexAxis=r.indexAxis||bc(a,this.options),o.order=r.order||0,o.index=s,o.label=""+r.label,o.visible=this.isDatasetVisible(s),o.controller)o.controller.updateIndex(s),o.controller.linkScales();else{const l=Tt.getController(a),{datasetElementType:c,dataElementType:d}=xe.datasets[a];Object.assign(l,{dataElementType:Tt.getElement(d),datasetElementType:c&&Tt.getElement(c)}),o.controller=new l(this,s),e.push(o.controller)}}return this._updateMetasets(),e}_resetElements(){se(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const s=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let c=0,d=this.data.datasets.length;c<d;c++){const{controller:h}=this.getDatasetMeta(c),f=!i&&r.indexOf(h)===-1;h.buildOrUpdateElements(f),o=Math.max(+h.getMaxOverflow(),o)}o=this._minPadding=s.layout.autoPadding?o:0,this._updateLayout(o),i||se(r,c=>{c.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(af("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){se(this.scales,e=>{ht.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),s=new Set(e.events);(!vh(n,s)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:s,start:i,count:r}of n){const o=s==="_removeElements"?-r:r;Ok(e,i,o)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,s=r=>new Set(e.filter(o=>o[0]===r).map((o,a)=>a+","+o.splice(1).join(","))),i=s(0);for(let r=1;r<n;r++)if(!vh(i,s(r)))return;return Array.from(i).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;ht.update(this,this.width,this.height,e);const n=this.chartArea,s=n.width<=0||n.height<=0;this._layers=[],se(this.boxes,i=>{s&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,r)=>{i._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,s=this.data.datasets.length;n<s;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,s=this.data.datasets.length;n<s;++n)this._updateDataset(n,Sn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const s=this.getDatasetMeta(e),i={meta:s,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(s.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Bt.has(this)?this.attached&&!Bt.running(this)&&Bt.start(this):(this.draw(),lf({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:s,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,s=[];let i,r;for(i=0,r=n.length;i<r;++i){const o=n[i];(!e||o.visible)&&s.push(o)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,s={meta:e,index:e.index,cancelable:!0},i=e_(this,e);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(i&&Au(n,i),e.controller.draw(),i&&Lu(n),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(e){return Vt(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,s,i){const r=T_.modes[n];return typeof r=="function"?r(this,e,s,i):[]}getDatasetMeta(e){const n=this.data.datasets[e],s=this._metasets;let i=s.filter(r=>r&&r._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},s.push(i)),i}getContext(){return this.$context||(this.$context=En(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const s=this.getDatasetMeta(e);return typeof s.hidden=="boolean"?!s.hidden:!n.hidden}setDatasetVisibility(e,n){const s=this.getDatasetMeta(e);s.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,s){const i=s?"show":"hide",r=this.getDatasetMeta(e),o=r.controller._resolveAnimations(void 0,i);Gi(n)?(r.data[n].hidden=!s,this.update()):(this.setDatasetVisibility(e,s),o.update(r,{visible:s}),this.update(a=>a.datasetIndex===e?i:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Bt.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),Eh(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete lo[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,s=(r,o)=>{n.addEventListener(this,r,o),e[r]=o},i=(r,o,a)=>{r.offsetX=o,r.offsetY=a,this._eventHandler(r)};se(this.options.events,r=>s(r,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,s=(l,c)=>{n.addEventListener(this,l,c),e[l]=c},i=(l,c)=>{e[l]&&(n.removeEventListener(this,l,c),delete e[l])},r=(l,c)=>{this.canvas&&this.resize(l,c)};let o;const a=()=>{i("attach",a),this.attached=!0,this.resize(),s("resize",r),s("detach",o)};o=()=>{this.attached=!1,i("resize",r),this._stop(),this._resize(0,0),s("attach",a)},n.isAttached(this.canvas)?a():o()}unbindEvents(){se(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},se(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,s){const i=s?"set":"remove";let r,o,a,l;for(n==="dataset"&&(r=this.getDatasetMeta(e[0].datasetIndex),r.controller["_"+i+"DatasetHoverStyle"]()),a=0,l=e.length;a<l;++a){o=e[a];const c=o&&this.getDatasetMeta(o.datasetIndex).controller;c&&c[i+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],s=e.map(({datasetIndex:r,index:o})=>{const a=this.getDatasetMeta(r);if(!a)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:a.data[o],index:o}});!zo(s,n)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,n))}notifyPlugins(e,n,s){return this._plugins.notify(this,e,n,s)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,s){const i=this.options.hover,r=(l,c)=>l.filter(d=>!c.some(h=>d.datasetIndex===h.datasetIndex&&d.index===h.index)),o=r(n,e),a=s?e:r(e,n);o.length&&this.updateHoverStyle(o,i.mode,!1),a.length&&i.mode&&this.updateHoverStyle(a,i.mode,!0)}_eventHandler(e,n){const s={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},i=o=>(o.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",s,i)===!1)return;const r=this._handleEvent(e,n,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,i),(r||s.changed)&&this.render(),this}_handleEvent(e,n,s){const{_active:i=[],options:r}=this,o=n,a=this._getActiveElements(e,i,s,o),l=Fb(e),c=Dk(e,this._lastEvent,s,l);s&&(this._lastEvent=null,oe(r.onHover,[e,a,this],this),l&&oe(r.onClick,[e,a,this],this));const d=!zo(a,i);return(d||n)&&(this._active=a,this._updateHoverStyles(a,i,n)),this._lastEvent=c,d}_getActiveElements(e,n,s,i){if(e.type==="mouseout")return[];if(!s)return n;const r=this.options.hover;return this.getElementsAtEventForMode(e,r.mode,r,i)}},F(en,"defaults",xe),F(en,"instances",lo),F(en,"overrides",ts),F(en,"registry",Tt),F(en,"version",Mk),F(en,"getChart",cf),en);function uf(){return se(fr.instances,t=>t._plugins.invalidate())}function Ak(t,e,n){const{startAngle:s,x:i,y:r,outerRadius:o,innerRadius:a,options:l}=e,{borderWidth:c,borderJoinStyle:d}=l,h=Math.min(c/o,Xe(s-n));if(t.beginPath(),t.arc(i,r,o-c/2,s+h/2,n-h/2),a>0){const f=Math.min(c/a,Xe(s-n));t.arc(i,r,a+c/2,n-f/2,s+f/2,!0)}else{const f=Math.min(c/2,o*Xe(s-n));if(d==="round")t.arc(i,r,f,n-ne/2,s+ne/2,!0);else if(d==="bevel"){const p=2*f*f,m=-p*Math.cos(n+ne/2)+i,y=-p*Math.sin(n+ne/2)+r,v=p*Math.cos(s+ne/2)+i,g=p*Math.sin(s+ne/2)+r;t.lineTo(m,y),t.lineTo(v,g)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}function Lk(t,e,n){const{startAngle:s,pixelMargin:i,x:r,y:o,outerRadius:a,innerRadius:l}=e;let c=i/a;t.beginPath(),t.arc(r,o,a,s-c,n+c),l>i?(c=i/l,t.arc(r,o,l,n+c,s-c,!0)):t.arc(r,o,i,n+_e,s-_e),t.closePath(),t.clip()}function Fk(t){return Fu(t,["outerStart","outerEnd","innerStart","innerEnd"])}function Bk(t,e,n,s){const i=Fk(t.options.borderRadius),r=(n-e)/2,o=Math.min(r,s*e/2),a=l=>{const c=(n-Math.min(r,l))*s/2;return Ie(l,0,Math.min(r,c))};return{outerStart:a(i.outerStart),outerEnd:a(i.outerEnd),innerStart:Ie(i.innerStart,0,o),innerEnd:Ie(i.innerEnd,0,o)}}function us(t,e,n,s){return{x:n+t*Math.cos(e),y:s+t*Math.sin(e)}}function Yo(t,e,n,s,i,r){const{x:o,y:a,startAngle:l,pixelMargin:c,innerRadius:d}=e,h=Math.max(e.outerRadius+s+n-c,0),f=d>0?d+s+n+c:0;let p=0;const m=i-l;if(s){const z=d>0?d-s:0,K=h>0?h-s:0,ee=(z+K)/2,D=ee!==0?m*ee/(ee+s):m;p=(m-D)/2}const y=Math.max(.001,m*h-n/ne)/h,v=(m-y)/2,g=l+v+p,x=i-v-p,{outerStart:b,outerEnd:k,innerStart:_,innerEnd:C}=Bk(e,f,h,x-g),N=h-b,S=h-k,L=g+b/N,R=x-k/S,I=f+_,M=f+C,H=g+_/I,U=x-C/M;if(t.beginPath(),r){const z=(L+R)/2;if(t.arc(o,a,h,L,z),t.arc(o,a,h,z,R),k>0){const j=us(S,R,o,a);t.arc(j.x,j.y,k,R,x+_e)}const K=us(M,x,o,a);if(t.lineTo(K.x,K.y),C>0){const j=us(M,U,o,a);t.arc(j.x,j.y,C,x+_e,U+Math.PI)}const ee=(x-C/f+(g+_/f))/2;if(t.arc(o,a,f,x-C/f,ee,!0),t.arc(o,a,f,ee,g+_/f,!0),_>0){const j=us(I,H,o,a);t.arc(j.x,j.y,_,H+Math.PI,g-_e)}const D=us(N,g,o,a);if(t.lineTo(D.x,D.y),b>0){const j=us(N,L,o,a);t.arc(j.x,j.y,b,g-_e,L)}}else{t.moveTo(o,a);const z=Math.cos(L)*h+o,K=Math.sin(L)*h+a;t.lineTo(z,K);const ee=Math.cos(R)*h+o,D=Math.sin(R)*h+a;t.lineTo(ee,D)}t.closePath()}function Ik(t,e,n,s,i){const{fullCircles:r,startAngle:o,circumference:a}=e;let l=e.endAngle;if(r){Yo(t,e,n,s,l,i);for(let c=0;c<r;++c)t.fill();isNaN(a)||(l=o+(a%fe||fe))}return Yo(t,e,n,s,l,i),t.fill(),l}function zk(t,e,n,s,i){const{fullCircles:r,startAngle:o,circumference:a,options:l}=e,{borderWidth:c,borderJoinStyle:d,borderDash:h,borderDashOffset:f,borderRadius:p}=l,m=l.borderAlign==="inner";if(!c)return;t.setLineDash(h||[]),t.lineDashOffset=f,m?(t.lineWidth=c*2,t.lineJoin=d||"round"):(t.lineWidth=c,t.lineJoin=d||"bevel");let y=e.endAngle;if(r){Yo(t,e,n,s,y,i);for(let v=0;v<r;++v)t.stroke();isNaN(a)||(y=o+(a%fe||fe))}m&&Lk(t,e,y),l.selfJoin&&y-o>=ne&&p===0&&d!=="miter"&&Ak(t,e,y),r||(Yo(t,e,n,s,y,i),t.stroke())}class js extends St{constructor(n){super();F(this,"circumference");F(this,"endAngle");F(this,"fullCircles");F(this,"innerRadius");F(this,"outerRadius");F(this,"pixelMargin");F(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,s,i){const r=this.getProps(["x","y"],i),{angle:o,distance:a}=_g(r,{x:n,y:s}),{startAngle:l,endAngle:c,innerRadius:d,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),p=(this.options.spacing+this.options.borderWidth)/2,m=X(f,c-l),y=Zi(o,l,c)&&l!==c,v=m>=fe||y,g=dn(a,d+p,h+p);return v&&g}getCenterPoint(n){const{x:s,y:i,startAngle:r,endAngle:o,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:c,spacing:d}=this.options,h=(r+o)/2,f=(a+l+d+c)/2;return{x:s+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:s,circumference:i}=this,r=(s.offset||0)/4,o=(s.spacing||0)/2,a=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=i>fe?Math.floor(i/fe):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*r,Math.sin(l)*r);const c=1-Math.sin(Math.min(ne,i||0)),d=r*c;n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,Ik(n,this,d,o,a),zk(n,this,d,o,a),n.restore()}}F(js,"id","arc"),F(js,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1}),F(js,"defaultRoutes",{backgroundColor:"backgroundColor"}),F(js,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function qg(t,e,n=e){t.lineCap=X(n.borderCapStyle,e.borderCapStyle),t.setLineDash(X(n.borderDash,e.borderDash)),t.lineDashOffset=X(n.borderDashOffset,e.borderDashOffset),t.lineJoin=X(n.borderJoinStyle,e.borderJoinStyle),t.lineWidth=X(n.borderWidth,e.borderWidth),t.strokeStyle=X(n.borderColor,e.borderColor)}function Wk(t,e,n){t.lineTo(n.x,n.y)}function $k(t){return t.stepped?l1:t.tension||t.cubicInterpolationMode==="monotone"?c1:Wk}function Qg(t,e,n={}){const s=t.length,{start:i=0,end:r=s-1}=n,{start:o,end:a}=e,l=Math.max(i,o),c=Math.min(r,a),d=i<o&&r<o||i>a&&r>a;return{count:s,start:l,loop:e.loop,ilen:c<l&&!d?s+c-l:c-l}}function Hk(t,e,n,s){const{points:i,options:r}=e,{count:o,start:a,loop:l,ilen:c}=Qg(i,n,s),d=$k(r);let{move:h=!0,reverse:f}=s||{},p,m,y;for(p=0;p<=c;++p)m=i[(a+(f?c-p:p))%o],!m.skip&&(h?(t.moveTo(m.x,m.y),h=!1):d(t,y,m,f,r.stepped),y=m);return l&&(m=i[(a+(f?c:0))%o],d(t,y,m,f,r.stepped)),!!l}function Uk(t,e,n,s){const i=e.points,{count:r,start:o,ilen:a}=Qg(i,n,s),{move:l=!0,reverse:c}=s||{};let d=0,h=0,f,p,m,y,v,g;const x=k=>(o+(c?a-k:k))%r,b=()=>{y!==v&&(t.lineTo(d,v),t.lineTo(d,y),t.lineTo(d,g))};for(l&&(p=i[x(0)],t.moveTo(p.x,p.y)),f=0;f<=a;++f){if(p=i[x(f)],p.skip)continue;const k=p.x,_=p.y,C=k|0;C===m?(_<y?y=_:_>v&&(v=_),d=(h*d+k)/++h):(b(),t.lineTo(k,_),m=C,h=0,y=v=_),g=_}b()}function kc(t){const e=t.options,n=e.borderDash&&e.borderDash.length;return!t._decimated&&!t._loop&&!e.tension&&e.cubicInterpolationMode!=="monotone"&&!e.stepped&&!n?Uk:Hk}function Vk(t){return t.stepped?W1:t.tension||t.cubicInterpolationMode==="monotone"?$1:Ln}function Yk(t,e,n,s){let i=e._path;i||(i=e._path=new Path2D,e.path(i,n,s)&&i.closePath()),qg(t,e.options),t.stroke(i)}function Xk(t,e,n,s){const{segments:i,options:r}=e,o=kc(e);for(const a of i)qg(t,r,a.style),t.beginPath(),o(t,e,a,{start:n,end:n+s-1})&&t.closePath(),t.stroke()}const Kk=typeof Path2D=="function";function qk(t,e,n,s){Kk&&!e.options.segment?Yk(t,e,n,s):Xk(t,e,n,s)}class pi extends St{constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,n){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const i=s.spanGaps?this._loop:this._fullLoop;O1(this._points,s,e,i,n),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Q1(this,this.options.segment))}first(){const e=this.segments,n=this.points;return e.length&&n[e[0].start]}last(){const e=this.segments,n=this.points,s=e.length;return s&&n[e[s-1].end]}interpolate(e,n){const s=this.options,i=e[n],r=this.points,o=X1(this,{property:n,start:i,end:i});if(!o.length)return;const a=[],l=Vk(s);let c,d;for(c=0,d=o.length;c<d;++c){const{start:h,end:f}=o[c],p=r[h],m=r[f];if(p===m){a.push(p);continue}const y=Math.abs((i-p[n])/(m[n]-p[n])),v=l(p,m,y,s.stepped);v[n]=e[n],a.push(v)}return a.length===1?a[0]:a}pathSegment(e,n,s){return kc(this)(e,this,n,s)}path(e,n,s){const i=this.segments,r=kc(this);let o=this._loop;n=n||0,s=s||this.points.length-n;for(const a of i)o&=r(e,this,a,{start:n,end:n+s-1});return!!o}draw(e,n,s,i){const r=this.options||{};(this.points||[]).length&&r.borderWidth&&(e.save(),qk(e,this,s,i),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}F(pi,"id","line"),F(pi,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),F(pi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),F(pi,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"&&e!=="fill"});function df(t,e,n,s){const i=t.options,{[n]:r}=t.getProps([n],s);return Math.abs(e-r)<i.radius+i.hitRadius}class co extends St{constructor(n){super();F(this,"parsed");F(this,"skip");F(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,n&&Object.assign(this,n)}inRange(n,s,i){const r=this.options,{x:o,y:a}=this.getProps(["x","y"],i);return Math.pow(n-o,2)+Math.pow(s-a,2)<Math.pow(r.hitRadius+r.radius,2)}inXRange(n,s){return df(this,n,"x",s)}inYRange(n,s){return df(this,n,"y",s)}getCenterPoint(n){const{x:s,y:i}=this.getProps(["x","y"],n);return{x:s,y:i}}size(n){n=n||this.options||{};let s=n.radius||0;s=Math.max(s,s&&n.hoverRadius||0);const i=s&&n.borderWidth||0;return(s+i)*2}draw(n,s){const i=this.options;this.skip||i.radius<.1||!Vt(this,s,this.size(i)/2)||(n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.fillStyle=i.backgroundColor,xc(n,i,this.x,this.y))}getRange(){const n=this.options||{};return n.radius+n.hitRadius}}F(co,"id","point"),F(co,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),F(co,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Gg(t,e){const{x:n,y:s,base:i,width:r,height:o}=t.getProps(["x","y","base","width","height"],e);let a,l,c,d,h;return t.horizontal?(h=o/2,a=Math.min(n,i),l=Math.max(n,i),c=s-h,d=s+h):(h=r/2,a=n-h,l=n+h,c=Math.min(s,i),d=Math.max(s,i)),{left:a,top:c,right:l,bottom:d}}function hn(t,e,n,s){return t?0:Ie(e,n,s)}function Qk(t,e,n){const s=t.options.borderWidth,i=t.borderSkipped,r=Eg(s);return{t:hn(i.top,r.top,0,n),r:hn(i.right,r.right,0,e),b:hn(i.bottom,r.bottom,0,n),l:hn(i.left,r.left,0,e)}}function Gk(t,e,n){const{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),i=t.options.borderRadius,r=Yn(i),o=Math.min(e,n),a=t.borderSkipped,l=s||Z(i);return{topLeft:hn(!l||a.top||a.left,r.topLeft,0,o),topRight:hn(!l||a.top||a.right,r.topRight,0,o),bottomLeft:hn(!l||a.bottom||a.left,r.bottomLeft,0,o),bottomRight:hn(!l||a.bottom||a.right,r.bottomRight,0,o)}}function Jk(t){const e=Gg(t),n=e.right-e.left,s=e.bottom-e.top,i=Qk(t,n/2,s/2),r=Gk(t,n/2,s/2);return{outer:{x:e.left,y:e.top,w:n,h:s,radius:r},inner:{x:e.left+i.l,y:e.top+i.t,w:n-i.l-i.r,h:s-i.t-i.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,r.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(i.b,i.r))}}}}function ml(t,e,n,s){const i=e===null,r=n===null,a=t&&!(i&&r)&&Gg(t,s);return a&&(i||dn(e,a.left,a.right))&&(r||dn(n,a.top,a.bottom))}function Zk(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function ew(t,e){t.rect(e.x,e.y,e.w,e.h)}function gl(t,e,n={}){const s=t.x!==n.x?-e:0,i=t.y!==n.y?-e:0,r=(t.x+t.w!==n.x+n.w?e:0)-s,o=(t.y+t.h!==n.y+n.h?e:0)-i;return{x:t.x+s,y:t.y+i,w:t.w+r,h:t.h+o,radius:t.radius}}class Pi extends St{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:s,backgroundColor:i}}=this,{inner:r,outer:o}=Jk(this),a=Zk(o.radius)?er:ew;e.save(),(o.w!==r.w||o.h!==r.h)&&(e.beginPath(),a(e,gl(o,n,r)),e.clip(),a(e,gl(r,-n,o)),e.fillStyle=s,e.fill("evenodd")),e.beginPath(),a(e,gl(r,n)),e.fillStyle=i,e.fill(),e.restore()}inRange(e,n,s){return ml(this,e,n,s)}inXRange(e,n){return ml(this,e,null,n)}inYRange(e,n){return ml(this,null,e,n)}getCenterPoint(e){const{x:n,y:s,base:i,horizontal:r}=this.getProps(["x","y","base","horizontal"],e);return{x:r?(n+i)/2:n,y:r?s:(s+i)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}F(Pi,"id","bar"),F(Pi,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),F(Pi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const hf=(t,e)=>{let{boxHeight:n=e,boxWidth:s=e}=t;return t.usePointStyle&&(n=Math.min(n,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:n,itemHeight:Math.max(e,n)}},tw=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class ff extends St{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,s){this.maxWidth=e,this.maxHeight=n,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=oe(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(s=>e.filter(s,this.chart.data))),e.sort&&(n=n.sort((s,i)=>e.sort(s,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const s=e.labels,i=Ne(s.font),r=i.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=hf(s,r);let c,d;n.font=i.string,this.isHorizontal()?(c=this.maxWidth,d=this._fitRows(o,r,a,l)+10):(d=this.maxHeight,c=this._fitCols(o,i,a,l)+10),this.width=Math.min(c,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,n,s,i){const{ctx:r,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],d=i+a;let h=e;r.textAlign="left",r.textBaseline="middle";let f=-1,p=-d;return this.legendItems.forEach((m,y)=>{const v=s+n/2+r.measureText(m.text).width;(y===0||c[c.length-1]+v+2*a>o)&&(h+=d,c[c.length-(y>0?0:1)]=0,p+=d,f++),l[y]={left:0,top:p,row:f,width:v,height:i},c[c.length-1]+=v+a}),h}_fitCols(e,n,s,i){const{ctx:r,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],d=o-e;let h=a,f=0,p=0,m=0,y=0;return this.legendItems.forEach((v,g)=>{const{itemWidth:x,itemHeight:b}=nw(s,n,r,v,i);g>0&&p+b+2*a>d&&(h+=f+a,c.push({width:f,height:p}),m+=f+a,y++,f=p=0),l[g]={left:m,top:p,col:y,width:x,height:b},f=Math.max(f,x),p+=b+a}),h+=f,c.push({width:f,height:p}),h}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:s,labels:{padding:i},rtl:r}}=this,o=Rs(r,this.left,this.width);if(this.isHorizontal()){let a=0,l=Le(s,this.left+i,this.right-this.lineWidths[a]);for(const c of n)a!==c.row&&(a=c.row,l=Le(s,this.left+i,this.right-this.lineWidths[a])),c.top+=this.top+e+i,c.left=o.leftForLtr(o.x(l),c.width),l+=c.width+i}else{let a=0,l=Le(s,this.top+e+i,this.bottom-this.columnSizes[a].height);for(const c of n)c.col!==a&&(a=c.col,l=Le(s,this.top+e+i,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+i,c.left=o.leftForLtr(o.x(c.left),c.width),l+=c.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;Au(e,this),this._draw(),Lu(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:s,ctx:i}=this,{align:r,labels:o}=e,a=xe.color,l=Rs(e.rtl,this.left,this.width),c=Ne(o.font),{padding:d}=o,h=c.size,f=h/2;let p;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=c.string;const{boxWidth:m,boxHeight:y,itemHeight:v}=hf(o,h),g=function(C,N,S){if(isNaN(m)||m<=0||isNaN(y)||y<0)return;i.save();const L=X(S.lineWidth,1);if(i.fillStyle=X(S.fillStyle,a),i.lineCap=X(S.lineCap,"butt"),i.lineDashOffset=X(S.lineDashOffset,0),i.lineJoin=X(S.lineJoin,"miter"),i.lineWidth=L,i.strokeStyle=X(S.strokeStyle,a),i.setLineDash(X(S.lineDash,[])),o.usePointStyle){const R={radius:y*Math.SQRT2/2,pointStyle:S.pointStyle,rotation:S.rotation,borderWidth:L},I=l.xPlus(C,m/2),M=N+f;Ng(i,R,I,M,o.pointStyleWidth&&m)}else{const R=N+Math.max((h-y)/2,0),I=l.leftForLtr(C,m),M=Yn(S.borderRadius);i.beginPath(),Object.values(M).some(H=>H!==0)?er(i,{x:I,y:R,w:m,h:y,radius:M}):i.rect(I,R,m,y),i.fill(),L!==0&&i.stroke()}i.restore()},x=function(C,N,S){ns(i,S.text,C,N+v/2,c,{strikethrough:S.hidden,textAlign:l.textAlign(S.textAlign)})},b=this.isHorizontal(),k=this._computeTitleHeight();b?p={x:Le(r,this.left+d,this.right-s[0]),y:this.top+d+k,line:0}:p={x:this.left+d,y:Le(r,this.top+k+d,this.bottom-n[0].height),line:0},Dg(this.ctx,e.textDirection);const _=v+d;this.legendItems.forEach((C,N)=>{i.strokeStyle=C.fontColor,i.fillStyle=C.fontColor;const S=i.measureText(C.text).width,L=l.textAlign(C.textAlign||(C.textAlign=o.textAlign)),R=m+f+S;let I=p.x,M=p.y;l.setWidth(this.width),b?N>0&&I+R+d>this.right&&(M=p.y+=_,p.line++,I=p.x=Le(r,this.left+d,this.right-s[p.line])):N>0&&M+_>this.bottom&&(I=p.x=I+n[p.line].width+d,p.line++,M=p.y=Le(r,this.top+k+d,this.bottom-n[p.line].height));const H=l.x(I);if(g(H,M,C),I=qb(L,I+m+f,b?I+R:this.right,e.rtl),x(l.x(I),M,C),b)p.x+=R+d;else if(typeof C.text!="string"){const U=c.lineHeight;p.y+=Jg(C,U)+d}else p.y+=_}),Ag(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,s=Ne(n.font),i=We(n.padding);if(!n.display)return;const r=Rs(e.rtl,this.left,this.width),o=this.ctx,a=n.position,l=s.size/2,c=i.top+l;let d,h=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),d=this.top+c,h=Le(e.align,h,this.right-f);else{const m=this.columnSizes.reduce((y,v)=>Math.max(y,v.height),0);d=c+Le(e.align,this.top,this.bottom-m-e.labels.padding-this._computeTitleHeight())}const p=Le(a,h,h+f);o.textAlign=r.textAlign(Ou(a)),o.textBaseline="middle",o.strokeStyle=n.color,o.fillStyle=n.color,o.font=s.string,ns(o,n.text,p,d,s)}_computeTitleHeight(){const e=this.options.title,n=Ne(e.font),s=We(e.padding);return e.display?n.lineHeight+s.height:0}_getLegendItemAt(e,n){let s,i,r;if(dn(e,this.left,this.right)&&dn(n,this.top,this.bottom)){for(r=this.legendHitBoxes,s=0;s<r.length;++s)if(i=r[s],dn(e,i.left,i.left+i.width)&&dn(n,i.top,i.top+i.height))return this.legendItems[s]}return null}handleEvent(e){const n=this.options;if(!rw(e.type,n))return;const s=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const i=this._hoveredItem,r=tw(i,s);i&&!r&&oe(n.onLeave,[e,i,this],this),this._hoveredItem=s,s&&!r&&oe(n.onHover,[e,s,this],this)}else s&&oe(n.onClick,[e,s,this],this)}}function nw(t,e,n,s,i){const r=sw(s,t,e,n),o=iw(i,s,e.lineHeight);return{itemWidth:r,itemHeight:o}}function sw(t,e,n,s){let i=t.text;return i&&typeof i!="string"&&(i=i.reduce((r,o)=>r.length>o.length?r:o)),e+n.size/2+s.measureText(i).width}function iw(t,e,n){let s=t;return typeof e.text!="string"&&(s=Jg(e,n)),s}function Jg(t,e){const n=t.text?t.text.length:0;return e*n}function rw(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var Zg={id:"legend",_element:ff,start(t,e,n){const s=t.legend=new ff({ctx:t.ctx,options:n,chart:t});ht.configure(t,s,n),ht.addBox(t,s)},stop(t){ht.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const s=t.legend;ht.configure(t,s,n),s.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const s=e.datasetIndex,i=n.chart;i.isDatasetVisible(s)?(i.hide(s),e.hidden=!0):(i.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:s,textAlign:i,color:r,useBorderRadius:o,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(n?0:void 0),d=We(c.borderWidth);return{text:e[l.index].label,fillStyle:c.backgroundColor,fontColor:r,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:i||c.textAlign,borderRadius:o&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class ey extends St{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const i=he(s.text)?s.text.length:1;this._padding=We(s.padding);const r=i*Ne(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:s,bottom:i,right:r,options:o}=this,a=o.align;let l=0,c,d,h;return this.isHorizontal()?(d=Le(a,s,r),h=n+e,c=r-s):(o.position==="left"?(d=s+e,h=Le(a,i,n),l=ne*-.5):(d=r-e,h=Le(a,n,i),l=ne*.5),c=i-n),{titleX:d,titleY:h,maxWidth:c,rotation:l}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const s=Ne(n.font),r=s.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(r);ns(e,n.text,0,0,s,{color:n.color,maxWidth:l,rotation:c,textAlign:Ou(n.align),textBaseline:"middle",translation:[o,a]})}}function ow(t,e){const n=new ey({ctx:t.ctx,options:e,chart:t});ht.configure(t,n,e),ht.addBox(t,n),t.titleBlock=n}var ty={id:"title",_element:ey,start(t,e,n){ow(t,n)},stop(t){const e=t.titleBlock;ht.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const s=t.titleBlock;ht.configure(t,s,n),s.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const mi={average(t){if(!t.length)return!1;let e,n,s=new Set,i=0,r=0;for(e=0,n=t.length;e<n;++e){const a=t[e].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),i+=l.y,++r}}return r===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:i/r}},nearest(t,e){if(!t.length)return!1;let n=e.x,s=e.y,i=Number.POSITIVE_INFINITY,r,o,a;for(r=0,o=t.length;r<o;++r){const l=t[r].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),d=gc(e,c);d<i&&(i=d,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,s=l.y}return{x:n,y:s}}};function Pt(t,e){return e&&(he(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function It(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function aw(t,e){const{element:n,datasetIndex:s,index:i}=e,r=t.getDatasetMeta(s).controller,{label:o,value:a}=r.getLabelAndValue(i);return{chart:t,label:o,parsed:r.getParsed(i),raw:t.data.datasets[s].data[i],formattedValue:a,dataset:r.getDataset(),dataIndex:i,datasetIndex:s,element:n}}function pf(t,e){const n=t.chart.ctx,{body:s,footer:i,title:r}=t,{boxWidth:o,boxHeight:a}=e,l=Ne(e.bodyFont),c=Ne(e.titleFont),d=Ne(e.footerFont),h=r.length,f=i.length,p=s.length,m=We(e.padding);let y=m.height,v=0,g=s.reduce((k,_)=>k+_.before.length+_.lines.length+_.after.length,0);if(g+=t.beforeBody.length+t.afterBody.length,h&&(y+=h*c.lineHeight+(h-1)*e.titleSpacing+e.titleMarginBottom),g){const k=e.displayColors?Math.max(a,l.lineHeight):l.lineHeight;y+=p*k+(g-p)*l.lineHeight+(g-1)*e.bodySpacing}f&&(y+=e.footerMarginTop+f*d.lineHeight+(f-1)*e.footerSpacing);let x=0;const b=function(k){v=Math.max(v,n.measureText(k).width+x)};return n.save(),n.font=c.string,se(t.title,b),n.font=l.string,se(t.beforeBody.concat(t.afterBody),b),x=e.displayColors?o+2+e.boxPadding:0,se(s,k=>{se(k.before,b),se(k.lines,b),se(k.after,b)}),x=0,n.font=d.string,se(t.footer,b),n.restore(),v+=m.width,{width:v,height:y}}function lw(t,e){const{y:n,height:s}=e;return n<s/2?"top":n>t.height-s/2?"bottom":"center"}function cw(t,e,n,s){const{x:i,width:r}=s,o=n.caretSize+n.caretPadding;if(t==="left"&&i+r+o>e.width||t==="right"&&i-r-o<0)return!0}function uw(t,e,n,s){const{x:i,width:r}=n,{width:o,chartArea:{left:a,right:l}}=t;let c="center";return s==="center"?c=i<=(a+l)/2?"left":"right":i<=r/2?c="left":i>=o-r/2&&(c="right"),cw(c,t,e,n)&&(c="center"),c}function mf(t,e,n){const s=n.yAlign||e.yAlign||lw(t,n);return{xAlign:n.xAlign||e.xAlign||uw(t,e,n,s),yAlign:s}}function dw(t,e){let{x:n,width:s}=t;return e==="right"?n-=s:e==="center"&&(n-=s/2),n}function hw(t,e,n){let{y:s,height:i}=t;return e==="top"?s+=n:e==="bottom"?s-=i+n:s-=i/2,s}function gf(t,e,n,s){const{caretSize:i,caretPadding:r,cornerRadius:o}=t,{xAlign:a,yAlign:l}=n,c=i+r,{topLeft:d,topRight:h,bottomLeft:f,bottomRight:p}=Yn(o);let m=dw(e,a);const y=hw(e,l,c);return l==="center"?a==="left"?m+=c:a==="right"&&(m-=c):a==="left"?m-=Math.max(d,f)+i:a==="right"&&(m+=Math.max(h,p)+i),{x:Ie(m,0,s.width-e.width),y:Ie(y,0,s.height-e.height)}}function Ur(t,e,n){const s=We(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-s.right:t.x+s.left}function yf(t){return Pt([],It(t))}function fw(t,e,n){return En(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function xf(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const ny={beforeTitle:Ft,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,s=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return n[e.dataIndex]}return""},afterTitle:Ft,beforeBody:Ft,beforeLabel:Ft,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return J(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Ft,afterBody:Ft,beforeFooter:Ft,footer:Ft,afterFooter:Ft};function Ye(t,e,n,s){const i=t[e].call(n,s);return typeof i>"u"?ny[e].call(n,s):i}class wc extends St{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,s=this.options.setContext(this.getContext()),i=s.enabled&&n.options.animation&&s.animations,r=new Fg(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=fw(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:s}=n,i=Ye(s,"beforeTitle",this,e),r=Ye(s,"title",this,e),o=Ye(s,"afterTitle",this,e);let a=[];return a=Pt(a,It(i)),a=Pt(a,It(r)),a=Pt(a,It(o)),a}getBeforeBody(e,n){return yf(Ye(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:s}=n,i=[];return se(e,r=>{const o={before:[],lines:[],after:[]},a=xf(s,r);Pt(o.before,It(Ye(a,"beforeLabel",this,r))),Pt(o.lines,Ye(a,"label",this,r)),Pt(o.after,It(Ye(a,"afterLabel",this,r))),i.push(o)}),i}getAfterBody(e,n){return yf(Ye(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:s}=n,i=Ye(s,"beforeFooter",this,e),r=Ye(s,"footer",this,e),o=Ye(s,"afterFooter",this,e);let a=[];return a=Pt(a,It(i)),a=Pt(a,It(r)),a=Pt(a,It(o)),a}_createItems(e){const n=this._active,s=this.chart.data,i=[],r=[],o=[];let a=[],l,c;for(l=0,c=n.length;l<c;++l)a.push(aw(this.chart,n[l]));return e.filter&&(a=a.filter((d,h,f)=>e.filter(d,h,f,s))),e.itemSort&&(a=a.sort((d,h)=>e.itemSort(d,h,s))),se(a,d=>{const h=xf(e.callbacks,d);i.push(Ye(h,"labelColor",this,d)),r.push(Ye(h,"labelPointStyle",this,d)),o.push(Ye(h,"labelTextColor",this,d))}),this.labelColors=i,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=a,a}update(e,n){const s=this.options.setContext(this.getContext()),i=this._active;let r,o=[];if(!i.length)this.opacity!==0&&(r={opacity:0});else{const a=mi[s.position].call(this,i,this._eventPosition);o=this._createItems(s),this.title=this.getTitle(o,s),this.beforeBody=this.getBeforeBody(o,s),this.body=this.getBody(o,s),this.afterBody=this.getAfterBody(o,s),this.footer=this.getFooter(o,s);const l=this._size=pf(this,s),c=Object.assign({},a,l),d=mf(this.chart,s,c),h=gf(s,c,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,r={opacity:1,x:h.x,y:h.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),e&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,s,i){const r=this.getCaretPosition(e,s,i);n.lineTo(r.x1,r.y1),n.lineTo(r.x2,r.y2),n.lineTo(r.x3,r.y3)}getCaretPosition(e,n,s){const{xAlign:i,yAlign:r}=this,{caretSize:o,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:d,bottomRight:h}=Yn(a),{x:f,y:p}=e,{width:m,height:y}=n;let v,g,x,b,k,_;return r==="center"?(k=p+y/2,i==="left"?(v=f,g=v-o,b=k+o,_=k-o):(v=f+m,g=v+o,b=k-o,_=k+o),x=v):(i==="left"?g=f+Math.max(l,d)+o:i==="right"?g=f+m-Math.max(c,h)-o:g=this.caretX,r==="top"?(b=p,k=b-o,v=g-o,x=g+o):(b=p+y,k=b+o,v=g+o,x=g-o),_=b),{x1:v,x2:g,x3:x,y1:b,y2:k,y3:_}}drawTitle(e,n,s){const i=this.title,r=i.length;let o,a,l;if(r){const c=Rs(s.rtl,this.x,this.width);for(e.x=Ur(this,s.titleAlign,s),n.textAlign=c.textAlign(s.titleAlign),n.textBaseline="middle",o=Ne(s.titleFont),a=s.titleSpacing,n.fillStyle=s.titleColor,n.font=o.string,l=0;l<r;++l)n.fillText(i[l],c.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+a,l+1===r&&(e.y+=s.titleMarginBottom-a)}}_drawColorBox(e,n,s,i,r){const o=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=r,d=Ne(r.bodyFont),h=Ur(this,"left",r),f=i.x(h),p=l<d.lineHeight?(d.lineHeight-l)/2:0,m=n.y+p;if(r.usePointStyle){const y={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},v=i.leftForLtr(f,c)+c/2,g=m+l/2;e.strokeStyle=r.multiKeyBackground,e.fillStyle=r.multiKeyBackground,xc(e,y,v,g),e.strokeStyle=o.borderColor,e.fillStyle=o.backgroundColor,xc(e,y,v,g)}else{e.lineWidth=Z(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,e.strokeStyle=o.borderColor,e.setLineDash(o.borderDash||[]),e.lineDashOffset=o.borderDashOffset||0;const y=i.leftForLtr(f,c),v=i.leftForLtr(i.xPlus(f,1),c-2),g=Yn(o.borderRadius);Object.values(g).some(x=>x!==0)?(e.beginPath(),e.fillStyle=r.multiKeyBackground,er(e,{x:y,y:m,w:c,h:l,radius:g}),e.fill(),e.stroke(),e.fillStyle=o.backgroundColor,e.beginPath(),er(e,{x:v,y:m+1,w:c-2,h:l-2,radius:g}),e.fill()):(e.fillStyle=r.multiKeyBackground,e.fillRect(y,m,c,l),e.strokeRect(y,m,c,l),e.fillStyle=o.backgroundColor,e.fillRect(v,m+1,c-2,l-2))}e.fillStyle=this.labelTextColors[s]}drawBody(e,n,s){const{body:i}=this,{bodySpacing:r,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:d}=s,h=Ne(s.bodyFont);let f=h.lineHeight,p=0;const m=Rs(s.rtl,this.x,this.width),y=function(S){n.fillText(S,m.x(e.x+p),e.y+f/2),e.y+=f+r},v=m.textAlign(o);let g,x,b,k,_,C,N;for(n.textAlign=o,n.textBaseline="middle",n.font=h.string,e.x=Ur(this,v,s),n.fillStyle=s.bodyColor,se(this.beforeBody,y),p=a&&v!=="right"?o==="center"?c/2+d:c+2+d:0,k=0,C=i.length;k<C;++k){for(g=i[k],x=this.labelTextColors[k],n.fillStyle=x,se(g.before,y),b=g.lines,a&&b.length&&(this._drawColorBox(n,e,k,m,s),f=Math.max(h.lineHeight,l)),_=0,N=b.length;_<N;++_)y(b[_]),f=h.lineHeight;se(g.after,y)}p=0,f=h.lineHeight,se(this.afterBody,y),e.y-=r}drawFooter(e,n,s){const i=this.footer,r=i.length;let o,a;if(r){const l=Rs(s.rtl,this.x,this.width);for(e.x=Ur(this,s.footerAlign,s),e.y+=s.footerMarginTop,n.textAlign=l.textAlign(s.footerAlign),n.textBaseline="middle",o=Ne(s.footerFont),n.fillStyle=s.footerColor,n.font=o.string,a=0;a<r;++a)n.fillText(i[a],l.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+s.footerSpacing}}drawBackground(e,n,s,i){const{xAlign:r,yAlign:o}=this,{x:a,y:l}=e,{width:c,height:d}=s,{topLeft:h,topRight:f,bottomLeft:p,bottomRight:m}=Yn(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(a+h,l),o==="top"&&this.drawCaret(e,n,s,i),n.lineTo(a+c-f,l),n.quadraticCurveTo(a+c,l,a+c,l+f),o==="center"&&r==="right"&&this.drawCaret(e,n,s,i),n.lineTo(a+c,l+d-m),n.quadraticCurveTo(a+c,l+d,a+c-m,l+d),o==="bottom"&&this.drawCaret(e,n,s,i),n.lineTo(a+p,l+d),n.quadraticCurveTo(a,l+d,a,l+d-p),o==="center"&&r==="left"&&this.drawCaret(e,n,s,i),n.lineTo(a,l+h),n.quadraticCurveTo(a,l,a+h,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,s=this.$animations,i=s&&s.x,r=s&&s.y;if(i||r){const o=mi[e.position].call(this,this._active,this._eventPosition);if(!o)return;const a=this._size=pf(this,e),l=Object.assign({},o,this._size),c=mf(n,e,l),d=gf(e,l,c,n);(i._to!==d.x||r._to!==d.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},r={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const o=We(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(e.save(),e.globalAlpha=s,this.drawBackground(r,e,i,n),Dg(e,n.textDirection),r.y+=o.top,this.drawTitle(r,e,n),this.drawBody(r,e,n),this.drawFooter(r,e,n),Ag(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const s=this._active,i=e.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),r=!zo(s,i),o=this._positionChanged(i,n);(r||o)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,s=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,r=this._active||[],o=this._getActiveElements(e,r,n,s),a=this._positionChanged(o,e),l=n||!zo(o,r)||a;return l&&(this._active=o,(i.enabled||i.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),l}_getActiveElements(e,n,s,i){const r=this.options;if(e.type==="mouseout")return[];if(!i)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const o=this.chart.getElementsAtEventForMode(e,r.mode,r,s);return r.reverse&&o.reverse(),o}_positionChanged(e,n){const{caretX:s,caretY:i,options:r}=this,o=mi[r.position].call(this,e,n);return o!==!1&&(s!==o.x||i!==o.y)}}F(wc,"positioners",mi);var sy={id:"tooltip",_element:wc,positioners:mi,afterInit(t,e,n){n&&(t.tooltip=new wc({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:ny},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const pw=(t,e,n,s)=>(typeof e=="string"?(n=t.push(e)-1,s.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function mw(t,e,n,s){const i=t.indexOf(e);if(i===-1)return pw(t,e,n,s);const r=t.lastIndexOf(e);return i!==r?n:i}const gw=(t,e)=>t===null?null:Ie(Math.round(t),0,e);function vf(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class Xo extends as{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const s=this.getLabels();for(const{index:i,label:r}of n)s[i]===r&&s.splice(i,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(J(e))return null;const s=this.getLabels();return n=isFinite(n)&&s[n]===e?n:mw(s,e,X(n,e),this._addedLabels),gw(n,s.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:s,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(s=0),n||(i=this.getLabels().length-1)),this.min=s,this.max=i}buildTicks(){const e=this.min,n=this.max,s=this.options.offset,i=[];let r=this.getLabels();r=e===0&&n===r.length-1?r:r.slice(e,n+1),this._valueRange=Math.max(r.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let o=e;o<=n;o++)i.push({value:o});return i}getLabelForValue(e){return vf.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}F(Xo,"id","category"),F(Xo,"defaults",{ticks:{callback:vf}});function yw(t,e){const n=[],{bounds:i,step:r,min:o,max:a,precision:l,count:c,maxTicks:d,maxDigits:h,includeBounds:f}=t,p=r||1,m=d-1,{min:y,max:v}=e,g=!J(o),x=!J(a),b=!J(c),k=(v-y)/(h+1);let _=_h((v-y)/m/p)*p,C,N,S,L;if(_<1e-14&&!g&&!x)return[{value:y},{value:v}];L=Math.ceil(v/_)-Math.floor(y/_),L>m&&(_=_h(L*_/m/p)*p),J(l)||(C=Math.pow(10,l),_=Math.ceil(_*C)/C),i==="ticks"?(N=Math.floor(y/_)*_,S=Math.ceil(v/_)*_):(N=y,S=v),g&&x&&r&&$b((a-o)/r,_/1e3)?(L=Math.round(Math.min((a-o)/_,d)),_=(a-o)/L,N=o,S=a):b?(N=g?o:N,S=x?a:S,L=c-1,_=(S-N)/L):(L=(S-N)/_,Ci(L,Math.round(L),_/1e3)?L=Math.round(L):L=Math.ceil(L));const R=Math.max(kh(_),kh(N));C=Math.pow(10,J(l)?R:l),N=Math.round(N*C)/C,S=Math.round(S*C)/C;let I=0;for(g&&(f&&N!==o?(n.push({value:o}),N<o&&I++,Ci(Math.round((N+I*_)*C)/C,o,bf(o,k,t))&&I++):N<o&&I++);I<L;++I){const M=Math.round((N+I*_)*C)/C;if(x&&M>a)break;n.push({value:M})}return x&&f&&S!==a?n.length&&Ci(n[n.length-1].value,a,bf(a,k,t))?n[n.length-1].value=a:n.push({value:a}):(!x||S===a)&&n.push({value:S}),n}function bf(t,e,{horizontal:n,minRotation:s}){const i=Ot(s),r=(n?Math.sin(i):Math.cos(i))||.001,o=.75*e*(""+t).length;return Math.min(e/r,o)}class Ko extends as{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return J(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:s}=this.getUserBounds();let{min:i,max:r}=this;const o=l=>i=n?i:l,a=l=>r=s?r:l;if(e){const l=Lt(i),c=Lt(r);l<0&&c<0?a(0):l>0&&c>0&&o(0)}if(i===r){let l=r===0?1:Math.abs(r*.05);a(r+l),e||o(i-l)}this.min=i,this.max=r}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:s}=e,i;return s?(i=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let s=this.getTickLimit();s=Math.max(2,s);const i={maxTicks:s,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},r=this._range||this,o=yw(i,r);return e.bounds==="ticks"&&bg(o,this,"value"),e.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const e=this.ticks;let n=this.min,s=this.max;if(super.configure(),this.options.offset&&e.length){const i=(s-n)/Math.max(e.length-1,1)/2;n-=i,s+=i}this._startValue=n,this._endValue=s,this._valueRange=s-n}getLabelForValue(e){return va(e,this.chart.options.locale,this.options.ticks.format)}}class qo extends Ko{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=Pe(e)?e:0,this.max=Pe(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,s=Ot(this.options.ticks.minRotation),i=(e?Math.sin(s):Math.cos(s))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,r.lineHeight/i))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}F(qo,"id","linear"),F(qo,"defaults",{ticks:{callback:ba.formatters.numeric}});const nr=t=>Math.floor(un(t)),Rn=(t,e)=>Math.pow(10,nr(t)+e);function _f(t){return t/Math.pow(10,nr(t))===1}function kf(t,e,n){const s=Math.pow(10,n),i=Math.floor(t/s);return Math.ceil(e/s)-i}function xw(t,e){const n=e-t;let s=nr(n);for(;kf(t,e,s)>10;)s++;for(;kf(t,e,s)<10;)s--;return Math.min(s,nr(t))}function vw(t,{min:e,max:n}){e=tt(t.min,e);const s=[],i=nr(e);let r=xw(e,n),o=r<0?Math.pow(10,Math.abs(r)):1;const a=Math.pow(10,r),l=i>r?Math.pow(10,i):0,c=Math.round((e-l)*o)/o,d=Math.floor((e-l)/a/10)*a*10;let h=Math.floor((c-d)/Math.pow(10,r)),f=tt(t.min,Math.round((l+d+h*Math.pow(10,r))*o)/o);for(;f<n;)s.push({value:f,major:_f(f),significand:h}),h>=10?h=h<15?15:20:h++,h>=20&&(r++,h=2,o=r>=0?1:o),f=Math.round((l+d+h*Math.pow(10,r))*o)/o;const p=tt(t.max,f);return s.push({value:p,major:_f(p),significand:h}),s}class wf extends as{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(e,n){const s=Ko.prototype.parse.apply(this,[e,n]);if(s===0){this._zero=!0;return}return Pe(s)&&s>0?s:null}determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=Pe(e)?Math.max(0,e):null,this.max=Pe(n)?Math.max(0,n):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Pe(this._userMin)&&(this.min=e===Rn(this.min,0)?Rn(this.min,-1):Rn(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let s=this.min,i=this.max;const r=a=>s=e?s:a,o=a=>i=n?i:a;s===i&&(s<=0?(r(1),o(10)):(r(Rn(s,-1)),o(Rn(i,1)))),s<=0&&r(Rn(i,-1)),i<=0&&o(Rn(s,1)),this.min=s,this.max=i}buildTicks(){const e=this.options,n={min:this._userMin,max:this._userMax},s=vw(n,this);return e.bounds==="ticks"&&bg(s,this,"value"),e.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(e){return e===void 0?"0":va(e,this.chart.options.locale,this.options.ticks.format)}configure(){const e=this.min;super.configure(),this._startValue=un(e),this._valueRange=un(this.max)-un(e)}getPixelForValue(e){return(e===void 0||e===0)&&(e=this.min),e===null||isNaN(e)?NaN:this.getPixelForDecimal(e===this.min?0:(un(e)-this._startValue)/this._valueRange)}getValueForPixel(e){const n=this.getDecimalForPixel(e);return Math.pow(10,this._startValue+n*this._valueRange)}}F(wf,"id","logarithmic"),F(wf,"defaults",{ticks:{callback:ba.formatters.logarithmic,major:{enabled:!0}}});function Sc(t){const e=t.ticks;if(e.display&&t.display){const n=We(e.backdropPadding);return X(e.font&&e.font.size,xe.font.size)+n.height}return 0}function bw(t,e,n){return n=he(n)?n:[n],{w:a1(t,e.string,n),h:n.length*e.lineHeight}}function Sf(t,e,n,s,i){return t===s||t===i?{start:e-n/2,end:e+n/2}:t<s||t>i?{start:e-n,end:e}:{start:e,end:e+n}}function _w(t){const e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},n=Object.assign({},e),s=[],i=[],r=t._pointLabels.length,o=t.options.pointLabels,a=o.centerPointLabels?ne/r:0;for(let l=0;l<r;l++){const c=o.setContext(t.getPointLabelContext(l));i[l]=c.padding;const d=t.getPointPosition(l,t.drawingArea+i[l],a),h=Ne(c.font),f=bw(t.ctx,h,t._pointLabels[l]);s[l]=f;const p=Xe(t.getIndexAngle(l)+a),m=Math.round(Tu(p)),y=Sf(m,d.x,f.w,0,180),v=Sf(m,d.y,f.h,90,270);kw(n,e,p,y,v)}t.setCenterPoint(e.l-n.l,n.r-e.r,e.t-n.t,n.b-e.b),t._pointLabelItems=jw(t,s,i)}function kw(t,e,n,s,i){const r=Math.abs(Math.sin(n)),o=Math.abs(Math.cos(n));let a=0,l=0;s.start<e.l?(a=(e.l-s.start)/r,t.l=Math.min(t.l,e.l-a)):s.end>e.r&&(a=(s.end-e.r)/r,t.r=Math.max(t.r,e.r+a)),i.start<e.t?(l=(e.t-i.start)/o,t.t=Math.min(t.t,e.t-l)):i.end>e.b&&(l=(i.end-e.b)/o,t.b=Math.max(t.b,e.b+l))}function ww(t,e,n){const s=t.drawingArea,{extra:i,additionalAngle:r,padding:o,size:a}=n,l=t.getPointPosition(e,s+i+o,r),c=Math.round(Tu(Xe(l.angle+_e))),d=Ew(l.y,a.h,c),h=Cw(c),f=Nw(l.x,a.w,h);return{visible:!0,x:l.x,y:d,textAlign:h,left:f,top:d,right:f+a.w,bottom:d+a.h}}function Sw(t,e){if(!e)return!0;const{left:n,top:s,right:i,bottom:r}=t;return!(Vt({x:n,y:s},e)||Vt({x:n,y:r},e)||Vt({x:i,y:s},e)||Vt({x:i,y:r},e))}function jw(t,e,n){const s=[],i=t._pointLabels.length,r=t.options,{centerPointLabels:o,display:a}=r.pointLabels,l={extra:Sc(r)/2,additionalAngle:o?ne/i:0};let c;for(let d=0;d<i;d++){l.padding=n[d],l.size=e[d];const h=ww(t,d,l);s.push(h),a==="auto"&&(h.visible=Sw(h,c),h.visible&&(c=h))}return s}function Cw(t){return t===0||t===180?"center":t<180?"left":"right"}function Nw(t,e,n){return n==="right"?t-=e:n==="center"&&(t-=e/2),t}function Ew(t,e,n){return n===90||n===270?t-=e/2:(n>270||n<90)&&(t-=e),t}function Pw(t,e,n){const{left:s,top:i,right:r,bottom:o}=n,{backdropColor:a}=e;if(!J(a)){const l=Yn(e.borderRadius),c=We(e.backdropPadding);t.fillStyle=a;const d=s-c.left,h=i-c.top,f=r-s+c.width,p=o-i+c.height;Object.values(l).some(m=>m!==0)?(t.beginPath(),er(t,{x:d,y:h,w:f,h:p,radius:l}),t.fill()):t.fillRect(d,h,f,p)}}function Mw(t,e){const{ctx:n,options:{pointLabels:s}}=t;for(let i=e-1;i>=0;i--){const r=t._pointLabelItems[i];if(!r.visible)continue;const o=s.setContext(t.getPointLabelContext(i));Pw(n,o,r);const a=Ne(o.font),{x:l,y:c,textAlign:d}=r;ns(n,t._pointLabels[i],l,c+a.lineHeight/2,a,{color:o.color,textAlign:d,textBaseline:"middle"})}}function iy(t,e,n,s){const{ctx:i}=t;if(n)i.arc(t.xCenter,t.yCenter,e,0,fe);else{let r=t.getPointPosition(0,e);i.moveTo(r.x,r.y);for(let o=1;o<s;o++)r=t.getPointPosition(o,e),i.lineTo(r.x,r.y)}}function Tw(t,e,n,s,i){const r=t.ctx,o=e.circular,{color:a,lineWidth:l}=e;!o&&!s||!a||!l||n<0||(r.save(),r.strokeStyle=a,r.lineWidth=l,r.setLineDash(i.dash||[]),r.lineDashOffset=i.dashOffset,r.beginPath(),iy(t,n,o,s),r.closePath(),r.stroke(),r.restore())}function Rw(t,e,n){return En(t,{label:n,index:e,type:"pointLabel"})}class Vr extends Ko{constructor(e){super(e),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const e=this._padding=We(Sc(this.options)/2),n=this.width=this.maxWidth-e.width,s=this.height=this.maxHeight-e.height;this.xCenter=Math.floor(this.left+n/2+e.left),this.yCenter=Math.floor(this.top+s/2+e.top),this.drawingArea=Math.floor(Math.min(n,s)/2)}determineDataLimits(){const{min:e,max:n}=this.getMinMax(!1);this.min=Pe(e)&&!isNaN(e)?e:0,this.max=Pe(n)&&!isNaN(n)?n:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Sc(this.options))}generateTickLabels(e){Ko.prototype.generateTickLabels.call(this,e),this._pointLabels=this.getLabels().map((n,s)=>{const i=oe(this.options.pointLabels.callback,[n,s],this);return i||i===0?i:""}).filter((n,s)=>this.chart.getDataVisibility(s))}fit(){const e=this.options;e.display&&e.pointLabels.display?_w(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(e,n,s,i){this.xCenter+=Math.floor((e-n)/2),this.yCenter+=Math.floor((s-i)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(e,n,s,i))}getIndexAngle(e){const n=fe/(this._pointLabels.length||1),s=this.options.startAngle||0;return Xe(e*n+Ot(s))}getDistanceFromCenterForValue(e){if(J(e))return NaN;const n=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-e)*n:(e-this.min)*n}getValueForDistanceFromCenter(e){if(J(e))return NaN;const n=e/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-n:this.min+n}getPointLabelContext(e){const n=this._pointLabels||[];if(e>=0&&e<n.length){const s=n[e];return Rw(this.getContext(),e,s)}}getPointPosition(e,n,s=0){const i=this.getIndexAngle(e)-_e+s;return{x:Math.cos(i)*n+this.xCenter,y:Math.sin(i)*n+this.yCenter,angle:i}}getPointPositionForValue(e,n){return this.getPointPosition(e,this.getDistanceFromCenterForValue(n))}getBasePosition(e){return this.getPointPositionForValue(e||0,this.getBaseValue())}getPointLabelPosition(e){const{left:n,top:s,right:i,bottom:r}=this._pointLabelItems[e];return{left:n,top:s,right:i,bottom:r}}drawBackground(){const{backgroundColor:e,grid:{circular:n}}=this.options;if(e){const s=this.ctx;s.save(),s.beginPath(),iy(this,this.getDistanceFromCenterForValue(this._endValue),n,this._pointLabels.length),s.closePath(),s.fillStyle=e,s.fill(),s.restore()}}drawGrid(){const e=this.ctx,n=this.options,{angleLines:s,grid:i,border:r}=n,o=this._pointLabels.length;let a,l,c;if(n.pointLabels.display&&Mw(this,o),i.display&&this.ticks.forEach((d,h)=>{if(h!==0||h===0&&this.min<0){l=this.getDistanceFromCenterForValue(d.value);const f=this.getContext(h),p=i.setContext(f),m=r.setContext(f);Tw(this,p,l,o,m)}}),s.display){for(e.save(),a=o-1;a>=0;a--){const d=s.setContext(this.getPointLabelContext(a)),{color:h,lineWidth:f}=d;!f||!h||(e.lineWidth=f,e.strokeStyle=h,e.setLineDash(d.borderDash),e.lineDashOffset=d.borderDashOffset,l=this.getDistanceFromCenterForValue(n.reverse?this.min:this.max),c=this.getPointPosition(a,l),e.beginPath(),e.moveTo(this.xCenter,this.yCenter),e.lineTo(c.x,c.y),e.stroke())}e.restore()}}drawBorder(){}drawLabels(){const e=this.ctx,n=this.options,s=n.ticks;if(!s.display)return;const i=this.getIndexAngle(0);let r,o;e.save(),e.translate(this.xCenter,this.yCenter),e.rotate(i),e.textAlign="center",e.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!n.reverse)return;const c=s.setContext(this.getContext(l)),d=Ne(c.font);if(r=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){e.font=d.string,o=e.measureText(a.label).width,e.fillStyle=c.backdropColor;const h=We(c.backdropPadding);e.fillRect(-o/2-h.left,-r-d.size/2-h.top,o+h.width,d.size+h.height)}ns(e,a.label,0,-r,d,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),e.restore()}drawTitle(){}}F(Vr,"id","radialLinear"),F(Vr,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:ba.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(e){return e},padding:5,centerPointLabels:!1}}),F(Vr,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),F(Vr,"descriptors",{angleLines:{_fallback:"grid"}});const ka={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},qe=Object.keys(ka);function jf(t,e){return t-e}function Cf(t,e){if(J(e))return null;const n=t._adapter,{parser:s,round:i,isoWeekday:r}=t._parseOpts;let o=e;return typeof s=="function"&&(o=s(o)),Pe(o)||(o=typeof s=="string"?n.parse(o,s):n.parse(o)),o===null?null:(i&&(o=i==="week"&&(Ji(r)||r===!0)?n.startOf(o,"isoWeek",r):n.startOf(o,i)),+o)}function Nf(t,e,n,s){const i=qe.length;for(let r=qe.indexOf(t);r<i-1;++r){const o=ka[qe[r]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((n-e)/(a*o.size))<=s)return qe[r]}return qe[i-1]}function Ow(t,e,n,s,i){for(let r=qe.length-1;r>=qe.indexOf(n);r--){const o=qe[r];if(ka[o].common&&t._adapter.diff(i,s,o)>=e-1)return o}return qe[n?qe.indexOf(n):0]}function Dw(t){for(let e=qe.indexOf(t)+1,n=qe.length;e<n;++e)if(ka[qe[e]].common)return qe[e]}function Ef(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:s,hi:i}=Ru(n,e),r=n[s]>=e?n[s]:n[i];t[r]=!0}}function Aw(t,e,n,s){const i=t._adapter,r=+i.startOf(e[0].value,s),o=e[e.length-1].value;let a,l;for(a=r;a<=o;a=+i.add(a,1,s))l=n[a],l>=0&&(e[l].major=!0);return e}function Pf(t,e,n){const s=[],i={},r=e.length;let o,a;for(o=0;o<r;++o)a=e[o],i[a]=o,s.push({value:a,major:!1});return r===0||!n?s:Aw(t,s,i,n)}class Qo extends as{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const s=e.time||(e.time={}),i=this._adapter=new C_._date(e.adapters.date);i.init(n),ji(s.displayFormats,i.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:Cf(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,s=e.time.unit||"day";let{min:i,max:r,minDefined:o,maxDefined:a}=this.getUserBounds();function l(c){!o&&!isNaN(c.min)&&(i=Math.min(i,c.min)),!a&&!isNaN(c.max)&&(r=Math.max(r,c.max))}(!o||!a)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=Pe(i)&&!isNaN(i)?i:+n.startOf(Date.now(),s),r=Pe(r)&&!isNaN(r)?r:+n.endOf(Date.now(),s)+1,this.min=Math.min(i,r-1),this.max=Math.max(i+1,r)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],s=e[e.length-1]),{min:n,max:s}}buildTicks(){const e=this.options,n=e.time,s=e.ticks,i=s.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const r=this.min,o=this.max,a=Yb(i,r,o);return this._unit=n.unit||(s.autoSkip?Nf(n.minUnit,this.min,this.max,this._getLabelCapacity(r)):Ow(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Dw(this._unit),this.initOffsets(i),e.reverse&&a.reverse(),Pf(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,s=0,i,r;this.options.offset&&e.length&&(i=this.getDecimalForValue(e[0]),e.length===1?n=1-i:n=(this.getDecimalForValue(e[1])-i)/2,r=this.getDecimalForValue(e[e.length-1]),e.length===1?s=r:s=(r-this.getDecimalForValue(e[e.length-2]))/2);const o=e.length<3?.5:.25;n=Ie(n,0,o),s=Ie(s,0,o),this._offsets={start:n,end:s,factor:1/(n+1+s)}}_generate(){const e=this._adapter,n=this.min,s=this.max,i=this.options,r=i.time,o=r.unit||Nf(r.minUnit,n,s,this._getLabelCapacity(n)),a=X(i.ticks.stepSize,1),l=o==="week"?r.isoWeekday:!1,c=Ji(l)||l===!0,d={};let h=n,f,p;if(c&&(h=+e.startOf(h,"isoWeek",l)),h=+e.startOf(h,c?"day":o),e.diff(s,n,o)>1e5*a)throw new Error(n+" and "+s+" are too far apart with stepSize of "+a+" "+o);const m=i.ticks.source==="data"&&this.getDataTimestamps();for(f=h,p=0;f<s;f=+e.add(f,a,o),p++)Ef(d,f,m);return(f===s||i.bounds==="ticks"||p===1)&&Ef(d,f,m),Object.keys(d).sort(jf).map(y=>+y)}getLabelForValue(e){const n=this._adapter,s=this.options.time;return s.tooltipFormat?n.format(e,s.tooltipFormat):n.format(e,s.displayFormats.datetime)}format(e,n){const i=this.options.time.displayFormats,r=this._unit,o=n||i[r];return this._adapter.format(e,o)}_tickFormatFunction(e,n,s,i){const r=this.options,o=r.ticks.callback;if(o)return oe(o,[e,n,s],this);const a=r.time.displayFormats,l=this._unit,c=this._majorUnit,d=l&&a[l],h=c&&a[c],f=s[n],p=c&&h&&f&&f.major;return this._adapter.format(e,i||(p?h:d))}generateTickLabels(e){let n,s,i;for(n=0,s=e.length;n<s;++n)i=e[n],i.label=this._tickFormatFunction(i.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,s=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+s)*n.factor)}getValueForPixel(e){const n=this._offsets,s=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+s*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,s=this.ctx.measureText(e).width,i=Ot(this.isHorizontal()?n.maxRotation:n.minRotation),r=Math.cos(i),o=Math.sin(i),a=this._resolveTickFontOptions(0).size;return{w:s*r+a*o,h:s*o+a*r}}_getLabelCapacity(e){const n=this.options.time,s=n.displayFormats,i=s[n.unit]||s.millisecond,r=this._tickFormatFunction(e,0,Pf(this,[e],this._majorUnit),i),o=this._getLabelSize(r),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let e=this._cache.data||[],n,s;if(e.length)return e;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,s=i.length;n<s;++n)e=e.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,s;if(e.length)return e;const i=this.getLabels();for(n=0,s=i.length;n<s;++n)e.push(Cf(this,i[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return wg(e.sort(jf))}}F(Qo,"id","time"),F(Qo,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Yr(t,e,n){let s=0,i=t.length-1,r,o,a,l;n?(e>=t[s].pos&&e<=t[i].pos&&({lo:s,hi:i}=Wn(t,"pos",e)),{pos:r,time:a}=t[s],{pos:o,time:l}=t[i]):(e>=t[s].time&&e<=t[i].time&&({lo:s,hi:i}=Wn(t,"time",e)),{time:r,pos:a}=t[s],{time:o,pos:l}=t[i]);const c=o-r;return c?a+(l-a)*(e-r)/c:a}class Mf extends Qo{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=Yr(n,this.min),this._tableRange=Yr(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:s}=this,i=[],r=[];let o,a,l,c,d;for(o=0,a=e.length;o<a;++o)c=e[o],c>=n&&c<=s&&i.push(c);if(i.length<2)return[{time:n,pos:0},{time:s,pos:1}];for(o=0,a=i.length;o<a;++o)d=i[o+1],l=i[o-1],c=i[o],Math.round((d+l)/2)!==c&&r.push({time:c,pos:o/(a-1)});return r}_generate(){const e=this.min,n=this.max;let s=super.getDataTimestamps();return(!s.includes(e)||!s.length)&&s.splice(0,0,e),(!s.includes(n)||s.length===1)&&s.push(n),s.sort((i,r)=>i-r)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),s=this.getLabelTimestamps();return n.length&&s.length?e=this.normalize(n.concat(s)):e=n.length?n:s,e=this._cache.all=e,e}getDecimalForValue(e){return(Yr(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,s=this.getDecimalForPixel(e)/n.factor-n.end;return Yr(this._table,s*this._tableRange+this._minPos,!0)}}F(Mf,"id","timeseries"),F(Mf,"defaults",Qo.defaults);const ry="label";function Tf(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function Lw(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function oy(t,e){t.labels=e}function ay(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ry;const s=[];t.datasets=e.map(i=>{const r=t.datasets.find(o=>o[n]===i[n]);return!r||!i.data||s.includes(r)?{...i}:(s.push(r),Object.assign(r,i),r)})}function Fw(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ry;const n={labels:[],datasets:[]};return oy(n,t.labels),ay(n,t.datasets,e),n}function Bw(t,e){const{height:n=150,width:s=300,redraw:i=!1,datasetIdKey:r,type:o,data:a,options:l,plugins:c=[],fallbackContent:d,updateMode:h,...f}=t,p=P.useRef(null),m=P.useRef(null),y=()=>{p.current&&(m.current=new fr(p.current,{type:o,data:Fw(a,r),options:l&&{...l},plugins:c}),Tf(e,m.current))},v=()=>{Tf(e,null),m.current&&(m.current.destroy(),m.current=null)};return P.useEffect(()=>{!i&&m.current&&l&&Lw(m.current,l)},[i,l]),P.useEffect(()=>{!i&&m.current&&oy(m.current.config.data,a.labels)},[i,a.labels]),P.useEffect(()=>{!i&&m.current&&a.datasets&&ay(m.current.config.data,a.datasets,r)},[i,a.datasets]),P.useEffect(()=>{m.current&&(i?(v(),setTimeout(y)):m.current.update(h))},[i,l,a.labels,a.datasets,h]),P.useEffect(()=>{m.current&&(v(),setTimeout(y))},[o]),P.useEffect(()=>(y(),()=>v()),[]),ta.createElement("canvas",{ref:p,role:"img",height:n,width:s,...f},d)}const Iw=P.forwardRef(Bw);function wa(t,e){return fr.register(e),P.forwardRef((n,s)=>ta.createElement(Iw,{...n,ref:s,type:t}))}const zw=wa("line",oo),ly=wa("bar",ro),Fn=wa("doughnut",Ss),Zt=wa("pie",vc);function cy(t,e){return function(){return t.apply(e,arguments)}}const{toString:Ww}=Object.prototype,{getPrototypeOf:Uu}=Object,{iterator:Sa,toStringTag:uy}=Symbol,ja=(t=>e=>{const n=Ww.call(e);return t[n]||(t[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),jt=t=>(t=t.toLowerCase(),e=>ja(e)===t),Ca=t=>e=>typeof e===t,{isArray:Ys}=Array,sr=Ca("undefined");function $w(t){return t!==null&&!sr(t)&&t.constructor!==null&&!sr(t.constructor)&&et(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const dy=jt("ArrayBuffer");function Hw(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&dy(t.buffer),e}const Uw=Ca("string"),et=Ca("function"),hy=Ca("number"),Na=t=>t!==null&&typeof t=="object",Vw=t=>t===!0||t===!1,uo=t=>{if(ja(t)!=="object")return!1;const e=Uu(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(uy in t)&&!(Sa in t)},Yw=jt("Date"),Xw=jt("File"),Kw=jt("Blob"),qw=jt("FileList"),Qw=t=>Na(t)&&et(t.pipe),Gw=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||et(t.append)&&((e=ja(t))==="formdata"||e==="object"&&et(t.toString)&&t.toString()==="[object FormData]"))},Jw=jt("URLSearchParams"),[Zw,eS,tS,nS]=["ReadableStream","Request","Response","Headers"].map(jt),sS=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function pr(t,e,{allOwnKeys:n=!1}={}){if(t===null||typeof t>"u")return;let s,i;if(typeof t!="object"&&(t=[t]),Ys(t))for(s=0,i=t.length;s<i;s++)e.call(null,t[s],s,t);else{const r=n?Object.getOwnPropertyNames(t):Object.keys(t),o=r.length;let a;for(s=0;s<o;s++)a=r[s],e.call(null,t[a],a,t)}}function fy(t,e){e=e.toLowerCase();const n=Object.keys(t);let s=n.length,i;for(;s-- >0;)if(i=n[s],e===i.toLowerCase())return i;return null}const $n=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),py=t=>!sr(t)&&t!==$n;function jc(){const{caseless:t}=py(this)&&this||{},e={},n=(s,i)=>{const r=t&&fy(e,i)||i;uo(e[r])&&uo(s)?e[r]=jc(e[r],s):uo(s)?e[r]=jc({},s):Ys(s)?e[r]=s.slice():e[r]=s};for(let s=0,i=arguments.length;s<i;s++)arguments[s]&&pr(arguments[s],n);return e}const iS=(t,e,n,{allOwnKeys:s}={})=>(pr(e,(i,r)=>{n&&et(i)?t[r]=cy(i,n):t[r]=i},{allOwnKeys:s}),t),rS=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),oS=(t,e,n,s)=>{t.prototype=Object.create(e.prototype,s),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},aS=(t,e,n,s)=>{let i,r,o;const a={};if(e=e||{},t==null)return e;do{for(i=Object.getOwnPropertyNames(t),r=i.length;r-- >0;)o=i[r],(!s||s(o,t,e))&&!a[o]&&(e[o]=t[o],a[o]=!0);t=n!==!1&&Uu(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},lS=(t,e,n)=>{t=String(t),(n===void 0||n>t.length)&&(n=t.length),n-=e.length;const s=t.indexOf(e,n);return s!==-1&&s===n},cS=t=>{if(!t)return null;if(Ys(t))return t;let e=t.length;if(!hy(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},uS=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&Uu(Uint8Array)),dS=(t,e)=>{const s=(t&&t[Sa]).call(t);let i;for(;(i=s.next())&&!i.done;){const r=i.value;e.call(t,r[0],r[1])}},hS=(t,e)=>{let n;const s=[];for(;(n=t.exec(e))!==null;)s.push(n);return s},fS=jt("HTMLFormElement"),pS=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,i){return s.toUpperCase()+i}),Rf=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),mS=jt("RegExp"),my=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),s={};pr(n,(i,r)=>{let o;(o=e(i,r,t))!==!1&&(s[r]=o||i)}),Object.defineProperties(t,s)},gS=t=>{my(t,(e,n)=>{if(et(t)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=t[n];if(et(s)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},yS=(t,e)=>{const n={},s=i=>{i.forEach(r=>{n[r]=!0})};return Ys(t)?s(t):s(String(t).split(e)),n},xS=()=>{},vS=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e;function bS(t){return!!(t&&et(t.append)&&t[uy]==="FormData"&&t[Sa])}const _S=t=>{const e=new Array(10),n=(s,i)=>{if(Na(s)){if(e.indexOf(s)>=0)return;if(!("toJSON"in s)){e[i]=s;const r=Ys(s)?[]:{};return pr(s,(o,a)=>{const l=n(o,i+1);!sr(l)&&(r[a]=l)}),e[i]=void 0,r}}return s};return n(t,0)},kS=jt("AsyncFunction"),wS=t=>t&&(Na(t)||et(t))&&et(t.then)&&et(t.catch),gy=((t,e)=>t?setImmediate:e?((n,s)=>($n.addEventListener("message",({source:i,data:r})=>{i===$n&&r===n&&s.length&&s.shift()()},!1),i=>{s.push(i),$n.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",et($n.postMessage)),SS=typeof queueMicrotask<"u"?queueMicrotask.bind($n):typeof process<"u"&&process.nextTick||gy,jS=t=>t!=null&&et(t[Sa]),E={isArray:Ys,isArrayBuffer:dy,isBuffer:$w,isFormData:Gw,isArrayBufferView:Hw,isString:Uw,isNumber:hy,isBoolean:Vw,isObject:Na,isPlainObject:uo,isReadableStream:Zw,isRequest:eS,isResponse:tS,isHeaders:nS,isUndefined:sr,isDate:Yw,isFile:Xw,isBlob:Kw,isRegExp:mS,isFunction:et,isStream:Qw,isURLSearchParams:Jw,isTypedArray:uS,isFileList:qw,forEach:pr,merge:jc,extend:iS,trim:sS,stripBOM:rS,inherits:oS,toFlatObject:aS,kindOf:ja,kindOfTest:jt,endsWith:lS,toArray:cS,forEachEntry:dS,matchAll:hS,isHTMLForm:fS,hasOwnProperty:Rf,hasOwnProp:Rf,reduceDescriptors:my,freezeMethods:gS,toObjectSet:yS,toCamelCase:pS,noop:xS,toFiniteNumber:vS,findKey:fy,global:$n,isContextDefined:py,isSpecCompliantForm:bS,toJSONObject:_S,isAsyncFn:kS,isThenable:wS,setImmediate:gy,asap:SS,isIterable:jS};function Y(t,e,n,s,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),s&&(this.request=s),i&&(this.response=i,this.status=i.status?i.status:null)}E.inherits(Y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:E.toJSONObject(this.config),code:this.code,status:this.status}}});const yy=Y.prototype,xy={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{xy[t]={value:t}});Object.defineProperties(Y,xy);Object.defineProperty(yy,"isAxiosError",{value:!0});Y.from=(t,e,n,s,i,r)=>{const o=Object.create(yy);return E.toFlatObject(t,o,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),Y.call(o,t.message,e,n,s,i),o.cause=t,o.name=t.name,r&&Object.assign(o,r),o};const CS=null;function Cc(t){return E.isPlainObject(t)||E.isArray(t)}function vy(t){return E.endsWith(t,"[]")?t.slice(0,-2):t}function Of(t,e,n){return t?t.concat(e).map(function(i,r){return i=vy(i),!n&&r?"["+i+"]":i}).join(n?".":""):e}function NS(t){return E.isArray(t)&&!t.some(Cc)}const ES=E.toFlatObject(E,{},null,function(e){return/^is[A-Z]/.test(e)});function Ea(t,e,n){if(!E.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,n=E.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,v){return!E.isUndefined(v[y])});const s=n.metaTokens,i=n.visitor||d,r=n.dots,o=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&E.isSpecCompliantForm(e);if(!E.isFunction(i))throw new TypeError("visitor must be a function");function c(m){if(m===null)return"";if(E.isDate(m))return m.toISOString();if(E.isBoolean(m))return m.toString();if(!l&&E.isBlob(m))throw new Y("Blob is not supported. Use a Buffer instead.");return E.isArrayBuffer(m)||E.isTypedArray(m)?l&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function d(m,y,v){let g=m;if(m&&!v&&typeof m=="object"){if(E.endsWith(y,"{}"))y=s?y:y.slice(0,-2),m=JSON.stringify(m);else if(E.isArray(m)&&NS(m)||(E.isFileList(m)||E.endsWith(y,"[]"))&&(g=E.toArray(m)))return y=vy(y),g.forEach(function(b,k){!(E.isUndefined(b)||b===null)&&e.append(o===!0?Of([y],k,r):o===null?y:y+"[]",c(b))}),!1}return Cc(m)?!0:(e.append(Of(v,y,r),c(m)),!1)}const h=[],f=Object.assign(ES,{defaultVisitor:d,convertValue:c,isVisitable:Cc});function p(m,y){if(!E.isUndefined(m)){if(h.indexOf(m)!==-1)throw Error("Circular reference detected in "+y.join("."));h.push(m),E.forEach(m,function(g,x){(!(E.isUndefined(g)||g===null)&&i.call(e,g,E.isString(x)?x.trim():x,y,f))===!0&&p(g,y?y.concat(x):[x])}),h.pop()}}if(!E.isObject(t))throw new TypeError("data must be an object");return p(t),e}function Df(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(s){return e[s]})}function Vu(t,e){this._pairs=[],t&&Ea(t,this,e)}const by=Vu.prototype;by.append=function(e,n){this._pairs.push([e,n])};by.toString=function(e){const n=e?function(s){return e.call(this,s,Df)}:Df;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function PS(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function _y(t,e,n){if(!e)return t;const s=n&&n.encode||PS;E.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let r;if(i?r=i(e,n):r=E.isURLSearchParams(e)?e.toString():new Vu(e,n).toString(s),r){const o=t.indexOf("#");o!==-1&&(t=t.slice(0,o)),t+=(t.indexOf("?")===-1?"?":"&")+r}return t}class MS{constructor(){this.handlers=[]}use(e,n,s){return this.handlers.push({fulfilled:e,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){E.forEach(this.handlers,function(s){s!==null&&e(s)})}}const Af=MS,ky={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},TS=typeof URLSearchParams<"u"?URLSearchParams:Vu,RS=typeof FormData<"u"?FormData:null,OS=typeof Blob<"u"?Blob:null,DS={isBrowser:!0,classes:{URLSearchParams:TS,FormData:RS,Blob:OS},protocols:["http","https","file","blob","url","data"]},Yu=typeof window<"u"&&typeof document<"u",Nc=typeof navigator=="object"&&navigator||void 0,AS=Yu&&(!Nc||["ReactNative","NativeScript","NS"].indexOf(Nc.product)<0),LS=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),FS=Yu&&window.location.href||"http://localhost",BS=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Yu,hasStandardBrowserEnv:AS,hasStandardBrowserWebWorkerEnv:LS,navigator:Nc,origin:FS},Symbol.toStringTag,{value:"Module"})),Be={...BS,...DS};function IS(t,e){return Ea(t,new Be.classes.URLSearchParams,Object.assign({visitor:function(n,s,i,r){return Be.isNode&&E.isBuffer(n)?(this.append(s,n.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}function zS(t){return E.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function WS(t){const e={},n=Object.keys(t);let s;const i=n.length;let r;for(s=0;s<i;s++)r=n[s],e[r]=t[r];return e}function wy(t){function e(n,s,i,r){let o=n[r++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),l=r>=n.length;return o=!o&&E.isArray(i)?i.length:o,l?(E.hasOwnProp(i,o)?i[o]=[i[o],s]:i[o]=s,!a):((!i[o]||!E.isObject(i[o]))&&(i[o]=[]),e(n,s,i[o],r)&&E.isArray(i[o])&&(i[o]=WS(i[o])),!a)}if(E.isFormData(t)&&E.isFunction(t.entries)){const n={};return E.forEachEntry(t,(s,i)=>{e(zS(s),i,n,0)}),n}return null}function $S(t,e,n){if(E.isString(t))try{return(e||JSON.parse)(t),E.trim(t)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(t)}const Xu={transitional:ky,adapter:["xhr","http","fetch"],transformRequest:[function(e,n){const s=n.getContentType()||"",i=s.indexOf("application/json")>-1,r=E.isObject(e);if(r&&E.isHTMLForm(e)&&(e=new FormData(e)),E.isFormData(e))return i?JSON.stringify(wy(e)):e;if(E.isArrayBuffer(e)||E.isBuffer(e)||E.isStream(e)||E.isFile(e)||E.isBlob(e)||E.isReadableStream(e))return e;if(E.isArrayBufferView(e))return e.buffer;if(E.isURLSearchParams(e))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(r){if(s.indexOf("application/x-www-form-urlencoded")>-1)return IS(e,this.formSerializer).toString();if((a=E.isFileList(e))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Ea(a?{"files[]":e}:e,l&&new l,this.formSerializer)}}return r||i?(n.setContentType("application/json",!1),$S(e)):e}],transformResponse:[function(e){const n=this.transitional||Xu.transitional,s=n&&n.forcedJSONParsing,i=this.responseType==="json";if(E.isResponse(e)||E.isReadableStream(e))return e;if(e&&E.isString(e)&&(s&&!this.responseType||i)){const o=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(e)}catch(a){if(o)throw a.name==="SyntaxError"?Y.from(a,Y.ERR_BAD_RESPONSE,this,null,this.response):a}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Be.classes.FormData,Blob:Be.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};E.forEach(["delete","get","head","post","put","patch"],t=>{Xu.headers[t]={}});const Ku=Xu,HS=E.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),US=t=>{const e={};let n,s,i;return t&&t.split(`
`).forEach(function(o){i=o.indexOf(":"),n=o.substring(0,i).trim().toLowerCase(),s=o.substring(i+1).trim(),!(!n||e[n]&&HS[n])&&(n==="set-cookie"?e[n]?e[n].push(s):e[n]=[s]:e[n]=e[n]?e[n]+", "+s:s)}),e},Lf=Symbol("internals");function ai(t){return t&&String(t).trim().toLowerCase()}function ho(t){return t===!1||t==null?t:E.isArray(t)?t.map(ho):String(t)}function VS(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(t);)e[s[1]]=s[2];return e}const YS=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function yl(t,e,n,s,i){if(E.isFunction(s))return s.call(this,e,n);if(i&&(e=n),!!E.isString(e)){if(E.isString(s))return e.indexOf(s)!==-1;if(E.isRegExp(s))return s.test(e)}}function XS(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,n,s)=>n.toUpperCase()+s)}function KS(t,e){const n=E.toCamelCase(" "+e);["get","set","has"].forEach(s=>{Object.defineProperty(t,s+n,{value:function(i,r,o){return this[s].call(this,e,i,r,o)},configurable:!0})})}class Pa{constructor(e){e&&this.set(e)}set(e,n,s){const i=this;function r(a,l,c){const d=ai(l);if(!d)throw new Error("header name must be a non-empty string");const h=E.findKey(i,d);(!h||i[h]===void 0||c===!0||c===void 0&&i[h]!==!1)&&(i[h||l]=ho(a))}const o=(a,l)=>E.forEach(a,(c,d)=>r(c,d,l));if(E.isPlainObject(e)||e instanceof this.constructor)o(e,n);else if(E.isString(e)&&(e=e.trim())&&!YS(e))o(US(e),n);else if(E.isObject(e)&&E.isIterable(e)){let a={},l,c;for(const d of e){if(!E.isArray(d))throw TypeError("Object iterator must return a key-value pair");a[c=d[0]]=(l=a[c])?E.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}o(a,n)}else e!=null&&r(n,e,s);return this}get(e,n){if(e=ai(e),e){const s=E.findKey(this,e);if(s){const i=this[s];if(!n)return i;if(n===!0)return VS(i);if(E.isFunction(n))return n.call(this,i,s);if(E.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,n){if(e=ai(e),e){const s=E.findKey(this,e);return!!(s&&this[s]!==void 0&&(!n||yl(this,this[s],s,n)))}return!1}delete(e,n){const s=this;let i=!1;function r(o){if(o=ai(o),o){const a=E.findKey(s,o);a&&(!n||yl(s,s[a],a,n))&&(delete s[a],i=!0)}}return E.isArray(e)?e.forEach(r):r(e),i}clear(e){const n=Object.keys(this);let s=n.length,i=!1;for(;s--;){const r=n[s];(!e||yl(this,this[r],r,e,!0))&&(delete this[r],i=!0)}return i}normalize(e){const n=this,s={};return E.forEach(this,(i,r)=>{const o=E.findKey(s,r);if(o){n[o]=ho(i),delete n[r];return}const a=e?XS(r):String(r).trim();a!==r&&delete n[r],n[a]=ho(i),s[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const n=Object.create(null);return E.forEach(this,(s,i)=>{s!=null&&s!==!1&&(n[i]=e&&E.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,n])=>e+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...n){const s=new this(e);return n.forEach(i=>s.set(i)),s}static accessor(e){const s=(this[Lf]=this[Lf]={accessors:{}}).accessors,i=this.prototype;function r(o){const a=ai(o);s[a]||(KS(i,o),s[a]=!0)}return E.isArray(e)?e.forEach(r):r(e),this}}Pa.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);E.reduceDescriptors(Pa.prototype,({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(s){this[n]=s}}});E.freezeMethods(Pa);const kt=Pa;function xl(t,e){const n=this||Ku,s=e||n,i=kt.from(s.headers);let r=s.data;return E.forEach(t,function(a){r=a.call(n,r,i.normalize(),e?e.status:void 0)}),i.normalize(),r}function Sy(t){return!!(t&&t.__CANCEL__)}function Xs(t,e,n){Y.call(this,t??"canceled",Y.ERR_CANCELED,e,n),this.name="CanceledError"}E.inherits(Xs,Y,{__CANCEL__:!0});function jy(t,e,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?t(n):e(new Y("Request failed with status code "+n.status,[Y.ERR_BAD_REQUEST,Y.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function qS(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function QS(t,e){t=t||10;const n=new Array(t),s=new Array(t);let i=0,r=0,o;return e=e!==void 0?e:1e3,function(l){const c=Date.now(),d=s[r];o||(o=c),n[i]=l,s[i]=c;let h=r,f=0;for(;h!==i;)f+=n[h++],h=h%t;if(i=(i+1)%t,i===r&&(r=(r+1)%t),c-o<e)return;const p=d&&c-d;return p?Math.round(f*1e3/p):void 0}}function GS(t,e){let n=0,s=1e3/e,i,r;const o=(c,d=Date.now())=>{n=d,i=null,r&&(clearTimeout(r),r=null),t.apply(null,c)};return[(...c)=>{const d=Date.now(),h=d-n;h>=s?o(c,d):(i=c,r||(r=setTimeout(()=>{r=null,o(i)},s-h)))},()=>i&&o(i)]}const Go=(t,e,n=3)=>{let s=0;const i=QS(50,250);return GS(r=>{const o=r.loaded,a=r.lengthComputable?r.total:void 0,l=o-s,c=i(l),d=o<=a;s=o;const h={loaded:o,total:a,progress:a?o/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&d?(a-o)/c:void 0,event:r,lengthComputable:a!=null,[e?"download":"upload"]:!0};t(h)},n)},Ff=(t,e)=>{const n=t!=null;return[s=>e[0]({lengthComputable:n,total:t,loaded:s}),e[1]]},Bf=t=>(...e)=>E.asap(()=>t(...e)),JS=Be.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,Be.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(Be.origin),Be.navigator&&/(msie|trident)/i.test(Be.navigator.userAgent)):()=>!0,ZS=Be.hasStandardBrowserEnv?{write(t,e,n,s,i,r){const o=[t+"="+encodeURIComponent(e)];E.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),E.isString(s)&&o.push("path="+s),E.isString(i)&&o.push("domain="+i),r===!0&&o.push("secure"),document.cookie=o.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ej(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function tj(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function Cy(t,e,n){let s=!ej(e);return t&&(s||n==!1)?tj(t,e):e}const If=t=>t instanceof kt?{...t}:t;function ss(t,e){e=e||{};const n={};function s(c,d,h,f){return E.isPlainObject(c)&&E.isPlainObject(d)?E.merge.call({caseless:f},c,d):E.isPlainObject(d)?E.merge({},d):E.isArray(d)?d.slice():d}function i(c,d,h,f){if(E.isUndefined(d)){if(!E.isUndefined(c))return s(void 0,c,h,f)}else return s(c,d,h,f)}function r(c,d){if(!E.isUndefined(d))return s(void 0,d)}function o(c,d){if(E.isUndefined(d)){if(!E.isUndefined(c))return s(void 0,c)}else return s(void 0,d)}function a(c,d,h){if(h in e)return s(c,d);if(h in t)return s(void 0,c)}const l={url:r,method:r,data:r,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(c,d,h)=>i(If(c),If(d),h,!0)};return E.forEach(Object.keys(Object.assign({},t,e)),function(d){const h=l[d]||i,f=h(t[d],e[d],d);E.isUndefined(f)&&h!==a||(n[d]=f)}),n}const Ny=t=>{const e=ss({},t);let{data:n,withXSRFToken:s,xsrfHeaderName:i,xsrfCookieName:r,headers:o,auth:a}=e;e.headers=o=kt.from(o),e.url=_y(Cy(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(E.isFormData(n)){if(Be.hasStandardBrowserEnv||Be.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((l=o.getContentType())!==!1){const[c,...d]=l?l.split(";").map(h=>h.trim()).filter(Boolean):[];o.setContentType([c||"multipart/form-data",...d].join("; "))}}if(Be.hasStandardBrowserEnv&&(s&&E.isFunction(s)&&(s=s(e)),s||s!==!1&&JS(e.url))){const c=i&&r&&ZS.read(r);c&&o.set(i,c)}return e},nj=typeof XMLHttpRequest<"u",sj=nj&&function(t){return new Promise(function(n,s){const i=Ny(t);let r=i.data;const o=kt.from(i.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:c}=i,d,h,f,p,m;function y(){p&&p(),m&&m(),i.cancelToken&&i.cancelToken.unsubscribe(d),i.signal&&i.signal.removeEventListener("abort",d)}let v=new XMLHttpRequest;v.open(i.method.toUpperCase(),i.url,!0),v.timeout=i.timeout;function g(){if(!v)return;const b=kt.from("getAllResponseHeaders"in v&&v.getAllResponseHeaders()),_={data:!a||a==="text"||a==="json"?v.responseText:v.response,status:v.status,statusText:v.statusText,headers:b,config:t,request:v};jy(function(N){n(N),y()},function(N){s(N),y()},_),v=null}"onloadend"in v?v.onloadend=g:v.onreadystatechange=function(){!v||v.readyState!==4||v.status===0&&!(v.responseURL&&v.responseURL.indexOf("file:")===0)||setTimeout(g)},v.onabort=function(){v&&(s(new Y("Request aborted",Y.ECONNABORTED,t,v)),v=null)},v.onerror=function(){s(new Y("Network Error",Y.ERR_NETWORK,t,v)),v=null},v.ontimeout=function(){let k=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const _=i.transitional||ky;i.timeoutErrorMessage&&(k=i.timeoutErrorMessage),s(new Y(k,_.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,t,v)),v=null},r===void 0&&o.setContentType(null),"setRequestHeader"in v&&E.forEach(o.toJSON(),function(k,_){v.setRequestHeader(_,k)}),E.isUndefined(i.withCredentials)||(v.withCredentials=!!i.withCredentials),a&&a!=="json"&&(v.responseType=i.responseType),c&&([f,m]=Go(c,!0),v.addEventListener("progress",f)),l&&v.upload&&([h,p]=Go(l),v.upload.addEventListener("progress",h),v.upload.addEventListener("loadend",p)),(i.cancelToken||i.signal)&&(d=b=>{v&&(s(!b||b.type?new Xs(null,t,v):b),v.abort(),v=null)},i.cancelToken&&i.cancelToken.subscribe(d),i.signal&&(i.signal.aborted?d():i.signal.addEventListener("abort",d)));const x=qS(i.url);if(x&&Be.protocols.indexOf(x)===-1){s(new Y("Unsupported protocol "+x+":",Y.ERR_BAD_REQUEST,t));return}v.send(r||null)})},ij=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let s=new AbortController,i;const r=function(c){if(!i){i=!0,a();const d=c instanceof Error?c:this.reason;s.abort(d instanceof Y?d:new Xs(d instanceof Error?d.message:d))}};let o=e&&setTimeout(()=>{o=null,r(new Y(`timeout ${e} of ms exceeded`,Y.ETIMEDOUT))},e);const a=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach(c=>{c.unsubscribe?c.unsubscribe(r):c.removeEventListener("abort",r)}),t=null)};t.forEach(c=>c.addEventListener("abort",r));const{signal:l}=s;return l.unsubscribe=()=>E.asap(a),l}},rj=ij,oj=function*(t,e){let n=t.byteLength;if(!e||n<e){yield t;return}let s=0,i;for(;s<n;)i=s+e,yield t.slice(s,i),s=i},aj=async function*(t,e){for await(const n of lj(t))yield*oj(n,e)},lj=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const e=t.getReader();try{for(;;){const{done:n,value:s}=await e.read();if(n)break;yield s}}finally{await e.cancel()}},zf=(t,e,n,s)=>{const i=aj(t,e);let r=0,o,a=l=>{o||(o=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:c,value:d}=await i.next();if(c){a(),l.close();return}let h=d.byteLength;if(n){let f=r+=h;n(f)}l.enqueue(new Uint8Array(d))}catch(c){throw a(c),c}},cancel(l){return a(l),i.return()}},{highWaterMark:2})},Ma=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ey=Ma&&typeof ReadableStream=="function",cj=Ma&&(typeof TextEncoder=="function"?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),Py=(t,...e)=>{try{return!!t(...e)}catch{return!1}},uj=Ey&&Py(()=>{let t=!1;const e=new Request(Be.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),Wf=64*1024,Ec=Ey&&Py(()=>E.isReadableStream(new Response("").body)),Jo={stream:Ec&&(t=>t.body)};Ma&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Jo[e]&&(Jo[e]=E.isFunction(t[e])?n=>n[e]():(n,s)=>{throw new Y(`Response type '${e}' is not supported`,Y.ERR_NOT_SUPPORT,s)})})})(new Response);const dj=async t=>{if(t==null)return 0;if(E.isBlob(t))return t.size;if(E.isSpecCompliantForm(t))return(await new Request(Be.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(E.isArrayBufferView(t)||E.isArrayBuffer(t))return t.byteLength;if(E.isURLSearchParams(t)&&(t=t+""),E.isString(t))return(await cj(t)).byteLength},hj=async(t,e)=>{const n=E.toFiniteNumber(t.getContentLength());return n??dj(e)},fj=Ma&&(async t=>{let{url:e,method:n,data:s,signal:i,cancelToken:r,timeout:o,onDownloadProgress:a,onUploadProgress:l,responseType:c,headers:d,withCredentials:h="same-origin",fetchOptions:f}=Ny(t);c=c?(c+"").toLowerCase():"text";let p=rj([i,r&&r.toAbortSignal()],o),m;const y=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let v;try{if(l&&uj&&n!=="get"&&n!=="head"&&(v=await hj(d,s))!==0){let _=new Request(e,{method:"POST",body:s,duplex:"half"}),C;if(E.isFormData(s)&&(C=_.headers.get("content-type"))&&d.setContentType(C),_.body){const[N,S]=Ff(v,Go(Bf(l)));s=zf(_.body,Wf,N,S)}}E.isString(h)||(h=h?"include":"omit");const g="credentials"in Request.prototype;m=new Request(e,{...f,signal:p,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:s,duplex:"half",credentials:g?h:void 0});let x=await fetch(m,f);const b=Ec&&(c==="stream"||c==="response");if(Ec&&(a||b&&y)){const _={};["status","statusText","headers"].forEach(L=>{_[L]=x[L]});const C=E.toFiniteNumber(x.headers.get("content-length")),[N,S]=a&&Ff(C,Go(Bf(a),!0))||[];x=new Response(zf(x.body,Wf,N,()=>{S&&S(),y&&y()}),_)}c=c||"text";let k=await Jo[E.findKey(Jo,c)||"text"](x,t);return!b&&y&&y(),await new Promise((_,C)=>{jy(_,C,{data:k,headers:kt.from(x.headers),status:x.status,statusText:x.statusText,config:t,request:m})})}catch(g){throw y&&y(),g&&g.name==="TypeError"&&/Load failed|fetch/i.test(g.message)?Object.assign(new Y("Network Error",Y.ERR_NETWORK,t,m),{cause:g.cause||g}):Y.from(g,g&&g.code,t,m)}}),Pc={http:CS,xhr:sj,fetch:fj};E.forEach(Pc,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});const $f=t=>`- ${t}`,pj=t=>E.isFunction(t)||t===null||t===!1,My={getAdapter:t=>{t=E.isArray(t)?t:[t];const{length:e}=t;let n,s;const i={};for(let r=0;r<e;r++){n=t[r];let o;if(s=n,!pj(n)&&(s=Pc[(o=String(n)).toLowerCase()],s===void 0))throw new Y(`Unknown adapter '${o}'`);if(s)break;i[o||"#"+r]=s}if(!s){const r=Object.entries(i).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let o=e?r.length>1?`since :
`+r.map($f).join(`
`):" "+$f(r[0]):"as no adapter specified";throw new Y("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return s},adapters:Pc};function vl(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Xs(null,t)}function Hf(t){return vl(t),t.headers=kt.from(t.headers),t.data=xl.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),My.getAdapter(t.adapter||Ku.adapter)(t).then(function(s){return vl(t),s.data=xl.call(t,t.transformResponse,s),s.headers=kt.from(s.headers),s},function(s){return Sy(s)||(vl(t),s&&s.response&&(s.response.data=xl.call(t,t.transformResponse,s.response),s.response.headers=kt.from(s.response.headers))),Promise.reject(s)})}const Ty="1.10.0",Ta={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{Ta[t]=function(s){return typeof s===t||"a"+(e<1?"n ":" ")+t}});const Uf={};Ta.transitional=function(e,n,s){function i(r,o){return"[Axios v"+Ty+"] Transitional option '"+r+"'"+o+(s?". "+s:"")}return(r,o,a)=>{if(e===!1)throw new Y(i(o," has been removed"+(n?" in "+n:"")),Y.ERR_DEPRECATED);return n&&!Uf[o]&&(Uf[o]=!0,console.warn(i(o," has been deprecated since v"+n+" and will be removed in the near future"))),e?e(r,o,a):!0}};Ta.spelling=function(e){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${e}`),!0)};function mj(t,e,n){if(typeof t!="object")throw new Y("options must be an object",Y.ERR_BAD_OPTION_VALUE);const s=Object.keys(t);let i=s.length;for(;i-- >0;){const r=s[i],o=e[r];if(o){const a=t[r],l=a===void 0||o(a,r,t);if(l!==!0)throw new Y("option "+r+" must be "+l,Y.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Y("Unknown option "+r,Y.ERR_BAD_OPTION)}}const fo={assertOptions:mj,validators:Ta},Et=fo.validators;class Zo{constructor(e){this.defaults=e||{},this.interceptors={request:new Af,response:new Af}}async request(e,n){try{return await this._request(e,n)}catch(s){if(s instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const r=i.stack?i.stack.replace(/^.+\n/,""):"";try{s.stack?r&&!String(s.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+r):s.stack=r}catch{}}throw s}}_request(e,n){typeof e=="string"?(n=n||{},n.url=e):n=e||{},n=ss(this.defaults,n);const{transitional:s,paramsSerializer:i,headers:r}=n;s!==void 0&&fo.assertOptions(s,{silentJSONParsing:Et.transitional(Et.boolean),forcedJSONParsing:Et.transitional(Et.boolean),clarifyTimeoutError:Et.transitional(Et.boolean)},!1),i!=null&&(E.isFunction(i)?n.paramsSerializer={serialize:i}:fo.assertOptions(i,{encode:Et.function,serialize:Et.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),fo.assertOptions(n,{baseUrl:Et.spelling("baseURL"),withXsrfToken:Et.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=r&&E.merge(r.common,r[n.method]);r&&E.forEach(["delete","get","head","post","put","patch","common"],m=>{delete r[m]}),n.headers=kt.concat(o,r);const a=[];let l=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(l=l&&y.synchronous,a.unshift(y.fulfilled,y.rejected))});const c=[];this.interceptors.response.forEach(function(y){c.push(y.fulfilled,y.rejected)});let d,h=0,f;if(!l){const m=[Hf.bind(this),void 0];for(m.unshift.apply(m,a),m.push.apply(m,c),f=m.length,d=Promise.resolve(n);h<f;)d=d.then(m[h++],m[h++]);return d}f=a.length;let p=n;for(h=0;h<f;){const m=a[h++],y=a[h++];try{p=m(p)}catch(v){y.call(this,v);break}}try{d=Hf.call(this,p)}catch(m){return Promise.reject(m)}for(h=0,f=c.length;h<f;)d=d.then(c[h++],c[h++]);return d}getUri(e){e=ss(this.defaults,e);const n=Cy(e.baseURL,e.url,e.allowAbsoluteUrls);return _y(n,e.params,e.paramsSerializer)}}E.forEach(["delete","get","head","options"],function(e){Zo.prototype[e]=function(n,s){return this.request(ss(s||{},{method:e,url:n,data:(s||{}).data}))}});E.forEach(["post","put","patch"],function(e){function n(s){return function(r,o,a){return this.request(ss(a||{},{method:e,headers:s?{"Content-Type":"multipart/form-data"}:{},url:r,data:o}))}}Zo.prototype[e]=n(),Zo.prototype[e+"Form"]=n(!0)});const po=Zo;class qu{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(r){n=r});const s=this;this.promise.then(i=>{if(!s._listeners)return;let r=s._listeners.length;for(;r-- >0;)s._listeners[r](i);s._listeners=null}),this.promise.then=i=>{let r;const o=new Promise(a=>{s.subscribe(a),r=a}).then(i);return o.cancel=function(){s.unsubscribe(r)},o},e(function(r,o,a){s.reason||(s.reason=new Xs(r,o,a),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const n=this._listeners.indexOf(e);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const e=new AbortController,n=s=>{e.abort(s)};return this.subscribe(n),e.signal.unsubscribe=()=>this.unsubscribe(n),e.signal}static source(){let e;return{token:new qu(function(i){e=i}),cancel:e}}}const gj=qu;function yj(t){return function(n){return t.apply(null,n)}}function xj(t){return E.isObject(t)&&t.isAxiosError===!0}const Mc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Mc).forEach(([t,e])=>{Mc[e]=t});const vj=Mc;function Ry(t){const e=new po(t),n=cy(po.prototype.request,e);return E.extend(n,po.prototype,e,{allOwnKeys:!0}),E.extend(n,e,null,{allOwnKeys:!0}),n.create=function(i){return Ry(ss(t,i))},n}const Se=Ry(Ku);Se.Axios=po;Se.CanceledError=Xs;Se.CancelToken=gj;Se.isCancel=Sy;Se.VERSION=Ty;Se.toFormData=Ea;Se.AxiosError=Y;Se.Cancel=Se.CanceledError;Se.all=function(e){return Promise.all(e)};Se.spread=yj;Se.isAxiosError=xj;Se.mergeConfig=ss;Se.AxiosHeaders=kt;Se.formToJSON=t=>wy(E.isHTMLForm(t)?new FormData(t):t);Se.getAdapter=My.getAdapter;Se.HttpStatusCode=vj;Se.default=Se;const bj=Se,_j="http://localhost:5000",Ct=bj.create({baseURL:_j,headers:{"Content-Type":"application/json"},timeout:1e4});Ct.interceptors.request.use(t=>{var e;return console.log(`API Request: ${(e=t.method)==null?void 0:e.toUpperCase()} ${t.url}`),t},t=>(console.error("API Request Error:",t),Promise.reject(t)));Ct.interceptors.response.use(t=>(console.log(`API Response: ${t.status} ${t.config.url}`),t),t=>{var e;return console.error("API Response Error:",((e=t.response)==null?void 0:e.data)||t.message),Promise.reject(t)});const kj=async()=>{var t;try{return(await Ct.get("/admin/data")).data}catch(e){throw((t=e.response)==null?void 0:t.data)||{message:"Failed to get admin data"}}},wj=async()=>{var t;try{return(await Ct.get("/admin/employees")).data}catch(e){throw((t=e.response)==null?void 0:t.data)||{message:"Failed to get employees"}}},Sj=async t=>{var e;try{return(await Ct.post("/admin/employees",t)).data}catch(n){throw((e=n.response)==null?void 0:e.data)||{message:"Failed to create employee"}}},jj=async(t,e)=>{var n;try{return(await Ct.put(`/admin/employees/${t}`,e)).data}catch(s){throw((n=s.response)==null?void 0:n.data)||{message:"Failed to update employee"}}},Cj=async t=>{var e;try{return(await Ct.delete(`/admin/employees/${t}`)).data}catch(n){throw((e=n.response)==null?void 0:e.data)||{message:"Failed to delete employee"}}},Nj=async()=>{var t;try{return(await Ct.get("/admin/books")).data}catch(e){throw((t=e.response)==null?void 0:t.data)||{message:"Failed to get books"}}},Ej=async t=>{var e;try{return(await Ct.post("/admin/books",t)).data}catch(n){throw((e=n.response)==null?void 0:e.data)||{message:"Failed to create book"}}},Pj=async(t,e)=>{var n;try{return(await Ct.put(`/admin/books/${t}`,e)).data}catch(s){throw((n=s.response)==null?void 0:n.data)||{message:"Failed to update book"}}},Mj=async t=>{var e;try{return(await Ct.delete(`/admin/books/${t}`)).data}catch(n){throw((e=n.response)==null?void 0:e.data)||{message:"Failed to delete book"}}};fr.register(Xo,qo,Pi,ty,sy,Zg,js);const Tj=()=>{const[t,e]=P.useState("overview"),[n,s]=P.useState(null),[i,r]=P.useState([]),[o,a]=P.useState([]),[l,c]=P.useState(!0),[d,h]=P.useState(""),[f,p]=P.useState(""),[m,y]=P.useState("connecting"),[v,g]=P.useState(!1),[x,b]=P.useState(""),[k,_]=P.useState(null),[C,N]=P.useState(!1),[S,L]=P.useState(null),R=xa(),I=async()=>{try{console.log("Testing backend connection...");const w=await fetch("http://localhost:5000/api/admin/data");w.ok?(y("connected"),console.log("✅ Backend connection successful")):(y("error"),console.log("❌ Backend connection failed:",w.status))}catch(w){y("error"),console.log("❌ Backend connection error:",w)}};P.useEffect(()=>{const w=localStorage.getItem("employee");if(!w){R("/login");return}try{if(JSON.parse(w).username!=="admin"){R("/employee");return}}catch{R("/login");return}I(),M()},[R]);const M=async()=>{try{c(!0);const[w,A,V]=await Promise.all([kj(),wj(),Nj()]);w.status==="success"&&(s(w.data),console.log("Admin data loaded:",w.data)),A.status==="success"&&(r(A.employees),console.log("Employees loaded:",A.employees.length)),V.status==="success"&&(a(V.books),console.log("Books loaded:",V.books.length))}catch{h("Failed to load dashboard data")}finally{c(!1)}},H=()=>{localStorage.removeItem("employee"),R("/login")},U=(w,A=null)=>{b(w),_(A),g(!0)},z=()=>{g(!1),b(""),_(null)},K=w=>{L(w),N(!0)},ee=()=>{N(!1),L(null)},D=async w=>{try{h(""),p(""),x==="employee"?k?(await jj(k.id,w),p("Employee updated successfully")):(await Sj(w),p("Employee created successfully")):x==="book"&&(k?(await Pj(k.id,w),p("Book updated successfully")):(await Ej(w),p("Book created successfully"))),z(),await M()}catch(A){h(A.message||"Operation failed")}},j=async(w,A)=>{if(window.confirm("Are you sure you want to delete this item?"))try{h(""),p(""),w==="employee"?(await Cj(A),p("Employee deleted successfully")):w==="book"&&(await Mj(A),p("Book deleted successfully")),await M()}catch(V){h(V.message||"Delete failed")}};if(l)return u.jsx("div",{className:"loading",children:u.jsx("div",{className:"spinner"})});const O=n?{labels:Object.keys(n.department_stats),datasets:[{label:"Books Taken",data:Object.values(n.department_stats).map(w=>w.books_taken),backgroundColor:"rgba(54, 162, 235, 0.6)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:1},{label:"Total Employees",data:Object.values(n.department_stats).map(w=>w.total_employees),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]}:null,W=n?{labels:["Available","Taken"],datasets:[{data:[n.book_status.available,n.book_status.unavailable],backgroundColor:["#28a745","#dc3545"],borderColor:["#ffffff","#ffffff"],borderWidth:3,hoverBackgroundColor:["#34ce57","#e74c3c"],hoverBorderWidth:4}]}:null,$={labels:["English Books (161)","Tamil Books (213)"],datasets:[{data:[161,213],backgroundColor:["#007bff","#fd7e14"],borderColor:["#ffffff","#ffffff"],borderWidth:3,hoverBackgroundColor:["#0056b3","#e8590c"],hoverBorderWidth:4}]};console.log("Chart data created:",{bookStatusChartData:!!W,languageChartData:!!$,adminData:!!n});const Q=n?{labels:["Active Readers","Inactive Employees"],datasets:[{data:[Object.values(n.department_stats||{}).reduce((w,A)=>w+(A.books_taken||0),0),(n.total_employees||4595)-Object.values(n.department_stats||{}).reduce((w,A)=>w+(A.books_taken||0),0)],backgroundColor:["#17a2b8","#6c757d"],borderColor:["#ffffff","#ffffff"],borderWidth:3,hoverBackgroundColor:["#138496","#545b62"],hoverBorderWidth:4}]}:{labels:["Active Readers","Inactive Employees"],datasets:[{data:[245,4350],backgroundColor:["#17a2b8","#6c757d"],borderColor:["#ffffff","#ffffff"],borderWidth:3,hoverBackgroundColor:["#138496","#545b62"],hoverBorderWidth:4}]},re={labels:["Fiction","Science Fiction","History","Poetry","Children's","Technical","Religious","Others"],datasets:[{data:[85,45,38,42,35,28,55,46],backgroundColor:["#e83e8c","#6f42c1","#fd7e14","#20c997","#ffc107","#dc3545","#6610f2","#28a745"],borderColor:"#ffffff",borderWidth:3,hoverBorderWidth:4}]},ve=n?{labels:Object.keys(n.department_stats||{}),datasets:[{data:Object.values(n.department_stats||{}).map(w=>w.books_taken||0),backgroundColor:["#007bff","#28a745","#ffc107","#dc3545","#17a2b8","#6f42c1","#fd7e14","#20c997","#e83e8c","#6c757d"],borderColor:"#ffffff",borderWidth:3,hoverBorderWidth:4}]}:{labels:["IT","HR","Finance","Operations","Marketing","Sales"],datasets:[{data:[45,32,28,38,25,42],backgroundColor:["#007bff","#28a745","#ffc107","#dc3545","#17a2b8","#6f42c1"],borderColor:"#ffffff",borderWidth:3,hoverBorderWidth:4}]};return u.jsxs("div",{className:"container",children:[u.jsxs("div",{className:"dashboard-header",children:[u.jsxs("h1",{className:"dashboard-title",children:["Admin Dashboard",u.jsxs("span",{className:`connection-status ${m}`,children:[m==="connected"&&"🟢",m==="connecting"&&"🟡",m==="error"&&"🔴"]})]}),u.jsxs("div",{className:"header-actions",children:[u.jsx("button",{className:"btn btn-success",onClick:()=>U("book"),style:{marginRight:"1rem"},children:"📚 Add Book"}),u.jsx("button",{className:"btn btn-info",onClick:()=>U("employee"),style:{marginRight:"1rem"},children:"👥 Add Employee"}),u.jsx("button",{className:"btn btn-primary",onClick:()=>R("/admin/advanced"),style:{marginRight:"1rem"},children:"📊 Advanced Analytics"}),u.jsxs("div",{className:"user-info",children:[u.jsx("div",{className:"user-name",children:"Administrator"}),u.jsx("button",{onClick:H,className:"btn btn-secondary mt-1",children:"Logout"})]})]})]}),d&&u.jsx("div",{className:"alert alert-error",children:d}),f&&u.jsx("div",{className:"alert alert-success",children:f}),u.jsxs("div",{className:"admin-tabs",children:[u.jsx("button",{className:`admin-tab ${t==="overview"?"active":""}`,onClick:()=>e("overview"),children:"Overview"}),u.jsx("button",{className:`admin-tab ${t==="analytics"?"active":""}`,onClick:()=>e("analytics"),children:"📊 Analytics"}),u.jsx("button",{className:`admin-tab ${t==="employees"?"active":""}`,onClick:()=>e("employees"),children:"Employees"}),u.jsx("button",{className:`admin-tab ${t==="books"?"active":""}`,onClick:()=>e("books"),children:"Books"})]}),t==="overview"&&n&&u.jsxs("div",{children:[u.jsxs("div",{className:"stats-grid",children:[u.jsxs("div",{className:"stat-card",children:[u.jsx("div",{className:"stat-number",children:n.total_books_taken}),u.jsx("div",{className:"stat-label",children:"Total Books Taken"})]}),u.jsxs("div",{className:"stat-card",children:[u.jsx("div",{className:"stat-number",children:n.employees_with_books}),u.jsx("div",{className:"stat-label",children:"Active Readers"})]}),u.jsxs("div",{className:"stat-card",children:[u.jsx("div",{className:"stat-number",children:n.book_status.total}),u.jsx("div",{className:"stat-label",children:"Total Books"})]}),u.jsxs("div",{className:"stat-card",children:[u.jsx("div",{className:"stat-number",children:n.book_status.available}),u.jsx("div",{className:"stat-label",children:"Available Books"})]})]}),u.jsxs("div",{className:"grid grid-2",children:[O&&u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"Department Statistics"}),u.jsx(ly,{data:O,options:{responsive:!0,plugins:{legend:{position:"top"}}}})]}),W&&u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"📚 Book Status Distribution"}),u.jsx("div",{style:{height:"300px",width:"100%"},children:u.jsx(Fn,{data:W,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} (${V}%)`}}}}}})})]})]}),u.jsxs("div",{className:"grid grid-3",style:{marginTop:"2rem"},children:[u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"🌐 Language Distribution"}),u.jsx("div",{style:{height:"300px",width:"100%"},children:u.jsx(Fn,{data:$,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books (${V}%)`}}}}}})})]}),u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"👥 Employee Activity"}),u.jsx("div",{style:{height:"300px",width:"100%"},children:u.jsx(Fn,{data:Q,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} employees (${V}%)`}}}}}})})]}),u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"📖 Genre Distribution"}),u.jsx("div",{style:{height:"300px",width:"100%"},children:u.jsx(Fn,{data:re,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:12,weight:"bold"},padding:15}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books (${V}%)`}}}}}})})]})]}),u.jsxs("div",{className:"grid grid-2",style:{marginTop:"2rem"},children:[u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"🏢 Department Performance"}),u.jsx("div",{style:{height:"300px",width:"100%"},children:u.jsx(Fn,{data:ve,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books taken (${V}%)`}}}}}})})]}),u.jsxs("div",{style:{marginTop:"3rem"},children:[u.jsx("h2",{style:{textAlign:"center",color:"#2d3748",marginBottom:"2rem",fontSize:"1.8rem"},children:"🥧 Comprehensive Statistics - Pie Charts"}),u.jsxs("div",{className:"grid grid-2",style:{gap:"2rem"},children:[u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"📚 Books by Language (Pie Chart)"}),u.jsx("div",{style:{height:"350px",width:"100%"},children:u.jsx(Zt,{data:$,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books (${V}%)`}}}}}})})]}),u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"📖 Genre Distribution (Pie Chart)"}),u.jsx("div",{style:{height:"350px",width:"100%"},children:u.jsx(Zt,{data:re,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:12,weight:"bold"},padding:15}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books (${V}%)`}}}}}})})]})]}),u.jsxs("div",{className:"grid grid-2",style:{gap:"2rem",marginTop:"2rem"},children:[u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"👥 Employee Activity (Pie Chart)"}),u.jsx("div",{style:{height:"350px",width:"100%"},children:u.jsx(Zt,{data:Q,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} employees (${V}%)`}}}}}})})]}),u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"🏢 Department Performance (Pie Chart)"}),u.jsx("div",{style:{height:"350px",width:"100%"},children:u.jsx(Zt,{data:ve,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books taken (${V}%)`}}}}}})})]})]})]}),u.jsxs("div",{className:"stats-summary",children:[u.jsx("h3",{className:"chart-title",children:"📊 Library Statistics Summary"}),u.jsxs("div",{className:"summary-grid",children:[u.jsxs("div",{className:"summary-item",children:[u.jsx("div",{className:"summary-number",children:"374"}),u.jsx("div",{className:"summary-label",children:"Total Books"})]}),u.jsxs("div",{className:"summary-item",children:[u.jsx("div",{className:"summary-number",children:"4,595"}),u.jsx("div",{className:"summary-label",children:"Total Employees"})]}),u.jsxs("div",{className:"summary-item",children:[u.jsx("div",{className:"summary-number",children:"161"}),u.jsx("div",{className:"summary-label",children:"English Books"})]}),u.jsxs("div",{className:"summary-item",children:[u.jsx("div",{className:"summary-number",children:"213"}),u.jsx("div",{className:"summary-label",children:"Tamil Books"})]}),u.jsxs("div",{className:"summary-item",children:[u.jsx("div",{className:"summary-number",children:n?n.book_status.available:"---"}),u.jsx("div",{className:"summary-label",children:"Available Books"})]}),u.jsxs("div",{className:"summary-item",children:[u.jsx("div",{className:"summary-number",children:n?n.book_status.unavailable:"---"}),u.jsx("div",{className:"summary-label",children:"Books Taken"})]})]})]})]})]}),t==="analytics"&&u.jsx("div",{className:"analytics-section",children:u.jsxs("div",{className:"card",children:[u.jsx("h2",{style:{textAlign:"center",color:"#2d3748",marginBottom:"2rem",fontSize:"2rem"},children:"📊 Comprehensive Library Analytics"}),u.jsxs("div",{className:"grid grid-2",style:{gap:"2rem",marginBottom:"3rem"},children:[u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"📚 Books by Language Distribution"}),u.jsx("div",{style:{height:"400px",width:"100%"},children:u.jsx(Zt,{data:$,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20,usePointStyle:!0}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books (${V}%)`}}}}}})})]}),u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"📖 Genre Distribution"}),u.jsx("div",{style:{height:"400px",width:"100%"},children:u.jsx(Zt,{data:re,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:12,weight:"bold"},padding:15,usePointStyle:!0}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books (${V}%)`}}}}}})})]})]}),u.jsxs("div",{className:"grid grid-2",style:{gap:"2rem",marginBottom:"3rem"},children:[u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"👥 Employee Activity Status"}),u.jsx("div",{style:{height:"400px",width:"100%"},children:u.jsx(Zt,{data:Q,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20,usePointStyle:!0}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} employees (${V}%)`}}}}}})})]}),u.jsxs("div",{className:"chart-container",children:[u.jsx("h3",{className:"chart-title",children:"📚 Book Status Overview"}),u.jsx("div",{style:{height:"400px",width:"100%"},children:u.jsx(Zt,{data:W,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:14,weight:"bold"},padding:20,usePointStyle:!0}},tooltip:{callbacks:{label:function(w){const A=w.dataset.data.reduce((te,le)=>te+le,0),V=(w.parsed*100/A).toFixed(1);return`${w.label}: ${w.parsed} books (${V}%)`}}}}}})})]})]}),u.jsxs("div",{className:"analytics-summary",style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",padding:"2rem",borderRadius:"12px",marginTop:"2rem"},children:[u.jsx("h3",{style:{textAlign:"center",marginBottom:"1.5rem",fontSize:"1.5rem"},children:"📈 Key Performance Indicators"}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(4, 1fr)",gap:"1rem"},children:[u.jsxs("div",{style:{textAlign:"center"},children:[u.jsx("div",{style:{fontSize:"2.5rem",fontWeight:"bold"},children:"374"}),u.jsx("div",{style:{fontSize:"0.9rem",opacity:.9},children:"Total Books"})]}),u.jsxs("div",{style:{textAlign:"center"},children:[u.jsx("div",{style:{fontSize:"2.5rem",fontWeight:"bold"},children:"4,595"}),u.jsx("div",{style:{fontSize:"0.9rem",opacity:.9},children:"Total Employees"})]}),u.jsxs("div",{style:{textAlign:"center"},children:[u.jsx("div",{style:{fontSize:"2.5rem",fontWeight:"bold"},children:n?n.book_status.available:"---"}),u.jsx("div",{style:{fontSize:"0.9rem",opacity:.9},children:"Available Books"})]}),u.jsxs("div",{style:{textAlign:"center"},children:[u.jsxs("div",{style:{fontSize:"2.5rem",fontWeight:"bold"},children:[n?Math.round(n.book_status.available/374*100):"---","%"]}),u.jsx("div",{style:{fontSize:"0.9rem",opacity:.9},children:"Availability Rate"})]})]})]})]})}),t==="employees"&&u.jsxs("div",{className:"card",children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"1rem"},children:[u.jsx("h2",{children:"Employees Management"}),u.jsx("button",{onClick:()=>U("employee"),className:"btn btn-primary",children:"Add Employee"})]}),u.jsx("div",{className:"employees-table",children:u.jsxs("table",{children:[u.jsx("thead",{children:u.jsxs("tr",{children:[u.jsx("th",{children:"Employee ID"}),u.jsx("th",{children:"Name"}),u.jsx("th",{children:"Department"}),u.jsx("th",{children:"Email"}),u.jsx("th",{children:"Books Taken"}),u.jsx("th",{children:"Status"}),u.jsx("th",{children:"Actions"})]})}),u.jsx("tbody",{children:i.map(w=>u.jsxs("tr",{children:[u.jsx("td",{children:w.employee_id||w.id}),u.jsx("td",{className:"employee-name-cell",children:u.jsx("strong",{onClick:()=>K(w),style:{cursor:"pointer",color:"#1e40af",fontWeight:"bold",textDecoration:"underline"},children:w.name})}),u.jsx("td",{children:w.department}),u.jsx("td",{children:w.email||"N/A"}),u.jsxs("td",{children:[w.books_taken||0," books"]}),u.jsx("td",{children:u.jsx("span",{className:"status-badge available",children:"Active"})}),u.jsxs("td",{children:[u.jsx("button",{onClick:()=>U("employee",w),className:"btn btn-secondary",style:{marginRight:"0.5rem"},children:"Edit"}),u.jsx("button",{onClick:()=>j("employee",w.id),className:"btn btn-danger",children:"Delete"})]})]},w.id))})]})})]}),t==="books"&&u.jsxs("div",{className:"card",children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"1rem"},children:[u.jsx("h2",{children:"Books Management"}),u.jsx("button",{onClick:()=>U("book"),className:"btn btn-primary",children:"Add Book"})]}),u.jsx("div",{className:"books-table",children:u.jsxs("table",{children:[u.jsx("thead",{children:u.jsxs("tr",{children:[u.jsx("th",{children:"Book ID"}),u.jsx("th",{children:"Title"}),u.jsx("th",{children:"Author"}),u.jsx("th",{children:"Genre"}),u.jsx("th",{children:"Language"}),u.jsx("th",{children:"Rack"}),u.jsx("th",{children:"Status"}),u.jsx("th",{children:"Taken By"}),u.jsx("th",{children:"Actions"})]})}),u.jsx("tbody",{children:o.map(w=>u.jsxs("tr",{children:[u.jsx("td",{children:w.id}),u.jsx("td",{className:"book-title",children:w.title}),u.jsx("td",{className:"book-author",children:w.author||"N/A"}),u.jsx("td",{className:"book-genre",children:w.genre||"N/A"}),u.jsx("td",{children:w.language||"English"}),u.jsx("td",{children:w.rack_no||"N/A"}),u.jsx("td",{children:u.jsx("span",{className:`status-badge ${w.status==="available"?"available":"taken"}`,children:w.status})}),u.jsx("td",{children:w.taken_by||"-"}),u.jsxs("td",{children:[u.jsx("button",{onClick:()=>U("book",w),className:"btn btn-secondary",style:{marginRight:"0.5rem"},children:"Edit"}),u.jsx("button",{onClick:()=>j("book",w.id),className:"btn btn-danger",children:"Delete"})]})]},w.id))})]})})]}),v&&u.jsx(Rj,{type:x,item:k,onSubmit:D,onClose:z}),C&&S&&u.jsx(Oj,{employee:S,onClose:ee})]})},Rj=({type:t,item:e,onSubmit:n,onClose:s})=>{const[i,r]=P.useState(e||(t==="employee"?{username:"",dob:"",name:"",department:""}:{title:"",author:"",isbn:"",status:"available"})),o=l=>{r({...i,[l.target.name]:l.target.value})},a=l=>{l.preventDefault(),n(i)};return u.jsx("div",{className:"modal-overlay",children:u.jsxs("div",{className:"modal",children:[u.jsxs("div",{className:"modal-header",children:[u.jsx("h3",{className:"modal-title",children:e?`Edit ${t}`:`Add ${t}`}),u.jsx("button",{onClick:s,className:"close-btn",children:"×"})]}),u.jsxs("form",{onSubmit:a,children:[t==="employee"?u.jsxs(u.Fragment,{children:[u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"Username"}),u.jsx("input",{type:"text",name:"username",value:i.username,onChange:o,className:"form-input",required:!0})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"Name"}),u.jsx("input",{type:"text",name:"name",value:i.name,onChange:o,className:"form-input",required:!0})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"Department"}),u.jsx("input",{type:"text",name:"department",value:i.department,onChange:o,className:"form-input",required:!0})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"Date of Birth"}),u.jsx("input",{type:"date",name:"dob",value:i.dob,onChange:o,className:"form-input",required:!0})]})]}):u.jsxs(u.Fragment,{children:[u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"Title"}),u.jsx("input",{type:"text",name:"title",value:i.title,onChange:o,className:"form-input",required:!0})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"Author"}),u.jsx("input",{type:"text",name:"author",value:i.author||"",onChange:o,className:"form-input"})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"ISBN"}),u.jsx("input",{type:"text",name:"isbn",value:i.isbn||"",onChange:o,className:"form-input"})]}),u.jsxs("div",{className:"form-group",children:[u.jsx("label",{className:"form-label",children:"Status"}),u.jsxs("select",{name:"status",value:i.status,onChange:o,className:"form-input",required:!0,children:[u.jsx("option",{value:"available",children:"Available"}),u.jsx("option",{value:"unavailable",children:"Unavailable"})]})]})]}),u.jsxs("div",{className:"form-actions",children:[u.jsx("button",{type:"button",onClick:s,className:"btn btn-secondary",children:"Cancel"}),u.jsx("button",{type:"submit",className:"btn btn-primary",children:e?"Update":"Create"})]})]})]})})},Oj=({employee:t,onClose:e})=>u.jsx("div",{className:"modal-overlay",children:u.jsxs("div",{className:"modal",style:{maxWidth:"600px"},children:[u.jsxs("div",{className:"modal-header",children:[u.jsx("h3",{className:"modal-title",children:"👤 Employee Details"}),u.jsx("button",{onClick:e,className:"close-btn",children:"×"})]}),u.jsxs("div",{className:"modal-body",children:[u.jsxs("div",{className:"employee-details-grid",style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"1rem",marginBottom:"1rem"},children:[u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Employee ID:"}),u.jsx("p",{children:t.emp_id||t.id})]}),u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Name:"}),u.jsx("p",{style:{fontWeight:"bold",color:"#000"},children:t.name})]}),u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Username:"}),u.jsx("p",{children:t.username})]}),u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Department:"}),u.jsx("p",{children:t.department})]}),u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Email:"}),u.jsx("p",{children:t.mail||t.email||"N/A"})]}),u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Date of Birth:"}),u.jsx("p",{children:t.dob})]}),u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Phone:"}),u.jsx("p",{children:t.phone||"N/A"})]}),u.jsxs("div",{className:"detail-item",children:[u.jsx("strong",{children:"Books Taken:"}),u.jsx("p",{children:t.books_taken||0})]})]}),u.jsxs("div",{className:"employee-stats",style:{background:"#f8f9fa",padding:"1rem",borderRadius:"8px",marginTop:"1rem"},children:[u.jsx("h4",{style:{margin:"0 0 0.5rem 0",color:"#495057"},children:"📊 Activity Summary"}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:"1rem"},children:[u.jsxs("div",{style:{textAlign:"center"},children:[u.jsx("div",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#007bff"},children:t.books_taken||0}),u.jsx("div",{style:{fontSize:"0.9rem",color:"#6c757d"},children:"Books Taken"})]}),u.jsxs("div",{style:{textAlign:"center"},children:[u.jsx("div",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#28a745"},children:t.books_returned||0}),u.jsx("div",{style:{fontSize:"0.9rem",color:"#6c757d"},children:"Books Returned"})]}),u.jsxs("div",{style:{textAlign:"center"},children:[u.jsx("div",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#dc3545"},children:t.overdue_books||0}),u.jsx("div",{style:{fontSize:"0.9rem",color:"#6c757d"},children:"Overdue Books"})]})]})]})]}),u.jsx("div",{className:"modal-footer",children:u.jsx("button",{onClick:e,className:"btn btn-secondary",children:"Close"})})]})});fr.register(Xo,qo,co,pi,ty,sy,Zg,js,Pi);const Dj=()=>{const[t,e]=P.useState(null),[n,s]=P.useState(null),[i,r]=P.useState(null),[o,a]=P.useState(null),[l,c]=P.useState(null),[d,h]=P.useState([]),[f,p]=P.useState(!0),[m,y]=P.useState("overview");P.useEffect(()=>{v();const _=setInterval(v,3e4);return()=>clearInterval(_)},[]);const v=async()=>{try{p(!0);const[_,C,N,S,L,R]=await Promise.all([fetch("http://localhost:5000/api/admin/real-time-stats"),fetch("http://localhost:5000/api/admin/charts/monthly-trends"),fetch("http://localhost:5000/api/admin/charts/genre-distribution"),fetch("http://localhost:5000/api/admin/charts/language-distribution"),fetch("http://localhost:5000/api/admin/charts/daily-activity"),fetch("http://localhost:5000/api/admin/overdue-books")]),[I,M,H,U,z,K]=await Promise.all([_.json(),C.json(),N.json(),S.json(),L.json(),R.json()]);e(I.stats),s(M.data),r(H.data),a(U.data),c(z.data),h(K.overdue_books||[])}catch(_){console.error("Error fetching dashboard data:",_)}finally{p(!1)}},g=n?{labels:n.months,datasets:[{label:"Books Taken",data:n.books_taken,borderColor:"#3b82f6",backgroundColor:"rgba(59, 130, 246, 0.1)",tension:.4,fill:!0},{label:"Books Returned",data:n.books_returned,borderColor:"#10b981",backgroundColor:"rgba(16, 185, 129, 0.1)",tension:.4,fill:!0}]}:null,x=i?{labels:i.labels,datasets:[{data:i.data,backgroundColor:["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#ec4899","#06b6d4","#84cc16","#f97316","#6366f1"],borderWidth:2,borderColor:"#ffffff"}]}:null,b=o?{labels:o.labels,datasets:[{data:o.data,backgroundColor:["#3b82f6","#ef4444"],borderWidth:3,borderColor:"#ffffff"}]}:null,k=l?{labels:l.days,datasets:[{label:"Daily Activity",data:l.activity,backgroundColor:"rgba(59, 130, 246, 0.8)",borderColor:"#3b82f6",borderWidth:2,borderRadius:8}]}:null;return f?u.jsx("div",{className:"advanced-admin-dashboard",children:u.jsxs("div",{className:"loading-container",children:[u.jsx("div",{className:"loading-spinner"}),u.jsx("p",{children:"Loading Advanced Analytics..."})]})}):u.jsxs("div",{className:"advanced-admin-dashboard",children:[u.jsxs("div",{className:"dashboard-header",children:[u.jsx("h1",{children:"📊 Advanced Admin Dashboard"}),u.jsxs("div",{className:"header-stats",children:[u.jsxs("div",{className:"stat-badge",children:[u.jsx("span",{className:"stat-number",children:(t==null?void 0:t.total_books)||0}),u.jsx("span",{className:"stat-label",children:"Total Books"})]}),u.jsxs("div",{className:"stat-badge",children:[u.jsx("span",{className:"stat-number",children:(t==null?void 0:t.taken_books)||0}),u.jsx("span",{className:"stat-label",children:"Books Taken"})]}),u.jsxs("div",{className:"stat-badge",children:[u.jsx("span",{className:"stat-number",children:(t==null?void 0:t.active_readers)||0}),u.jsx("span",{className:"stat-label",children:"Active Readers"})]}),u.jsxs("div",{className:"stat-badge",children:[u.jsx("span",{className:"stat-number",children:d.length}),u.jsx("span",{className:"stat-label",children:"Overdue Books"})]})]})]}),u.jsxs("div",{className:"dashboard-tabs",children:[u.jsx("button",{className:`tab-button ${m==="overview"?"active":""}`,onClick:()=>y("overview"),children:"📈 Overview"}),u.jsx("button",{className:`tab-button ${m==="analytics"?"active":""}`,onClick:()=>y("analytics"),children:"📊 Analytics"}),u.jsx("button",{className:`tab-button ${m==="overdue"?"active":""}`,onClick:()=>y("overdue"),children:"⚠️ Overdue Books"})]}),m==="overview"&&u.jsx("div",{className:"overview-tab",children:u.jsxs("div",{className:"stats-grid",children:[u.jsxs("div",{className:"stat-card",children:[u.jsx("h3",{children:"📚 Library Collection"}),u.jsxs("div",{className:"stat-details",children:[u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"Total Books:"}),u.jsx("span",{className:"stat-value",children:(t==null?void 0:t.total_books)||0})]}),u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"English Books:"}),u.jsx("span",{className:"stat-value",children:(t==null?void 0:t.english_books)||0})]}),u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"Tamil Books:"}),u.jsx("span",{className:"stat-value",children:(t==null?void 0:t.tamil_books)||0})]}),u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"Available:"}),u.jsx("span",{className:"stat-value available",children:(t==null?void 0:t.available_books)||0})]}),u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"Currently Taken:"}),u.jsx("span",{className:"stat-value taken",children:(t==null?void 0:t.taken_books)||0})]})]})]}),u.jsxs("div",{className:"stat-card",children:[u.jsx("h3",{children:"👥 Employee Statistics"}),u.jsxs("div",{className:"stat-details",children:[u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"Total Employees:"}),u.jsx("span",{className:"stat-value",children:(t==null?void 0:t.total_employees)||0})]}),u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"Active Readers:"}),u.jsx("span",{className:"stat-value",children:(t==null?void 0:t.active_readers)||0})]}),u.jsxs("div",{className:"stat-row",children:[u.jsx("span",{children:"Reading Rate:"}),u.jsx("span",{className:"stat-value",children:t!=null&&t.total_employees?(t.active_readers/t.total_employees*100).toFixed(1)+"%":"0%"})]})]})]}),u.jsxs("div",{className:"chart-card",children:[u.jsx("h3",{children:"📊 Language Distribution"}),b&&u.jsx("div",{className:"chart-container small",children:u.jsx(Fn,{data:b,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}})})]})]})}),m==="analytics"&&u.jsx("div",{className:"analytics-tab",children:u.jsxs("div",{className:"charts-grid",children:[u.jsxs("div",{className:"chart-card large",children:[u.jsx("h3",{children:"📈 Monthly Trends"}),g&&u.jsx("div",{className:"chart-container",children:u.jsx(zw,{data:g,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!0,text:"Book Borrowing Trends (Last 12 Months)"}},scales:{y:{beginAtZero:!0}}}})})]}),u.jsxs("div",{className:"chart-card",children:[u.jsx("h3",{children:"📚 Genre Distribution"}),x&&u.jsx("div",{className:"chart-container",children:u.jsx(Fn,{data:x,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}})})]}),u.jsxs("div",{className:"chart-card",children:[u.jsx("h3",{children:"📅 Daily Activity (Last 7 Days)"}),k&&u.jsx("div",{className:"chart-container",children:u.jsx(ly,{data:k,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0}}}})})]})]})}),m==="overdue"&&u.jsxs("div",{className:"overdue-tab",children:[u.jsxs("div",{className:"overdue-header",children:[u.jsxs("h3",{children:["⚠️ Overdue Books (",d.length,")"]}),u.jsx("button",{className:"refresh-btn",onClick:v,children:"🔄 Refresh"})]}),d.length===0?u.jsx("div",{className:"no-overdue",children:u.jsx("p",{children:"🎉 No overdue books! All books are returned on time."})}):u.jsx("div",{className:"overdue-table",children:u.jsxs("table",{children:[u.jsx("thead",{children:u.jsxs("tr",{children:[u.jsx("th",{children:"Book"}),u.jsx("th",{children:"Employee"}),u.jsx("th",{children:"Department"}),u.jsx("th",{children:"Taken Date"}),u.jsx("th",{children:"Days Overdue"}),u.jsx("th",{children:"Contact"})]})}),u.jsx("tbody",{children:d.map((_,C)=>u.jsxs("tr",{children:[u.jsx("td",{children:u.jsxs("div",{className:"book-info",children:[u.jsx("strong",{children:_.title}),u.jsxs("small",{children:["by ",_.author]})]})}),u.jsx("td",{children:u.jsxs("div",{className:"employee-info",children:[u.jsx("strong",{children:_.employee.name}),u.jsxs("small",{children:["ID: ",_.employee.emp_id]})]})}),u.jsx("td",{children:_.employee.department}),u.jsx("td",{children:new Date(_.taken_date).toLocaleDateString()}),u.jsx("td",{children:u.jsxs("span",{className:"overdue-days",children:[_.days_overdue," days"]})}),u.jsx("td",{children:u.jsx("a",{href:`mailto:${_.employee.email}`,className:"contact-btn",children:"📧 Email"})})]},C))})]})})]})]})};function Aj(){return u.jsx("div",{className:"App",children:u.jsxs(Zv,{children:[u.jsx(ds,{path:"/",element:u.jsx(Gv,{to:"/login",replace:!0})}),u.jsx(ds,{path:"/login",element:u.jsx(ib,{})}),u.jsx(ds,{path:"/employee",element:u.jsx(lb,{})}),u.jsx(ds,{path:"/admin",element:u.jsx(Tj,{})}),u.jsx(ds,{path:"/admin/advanced",element:u.jsx(Dj,{})})]})})}bl.createRoot(document.getElementById("root")).render(u.jsx(ta.StrictMode,{children:u.jsx(nb,{children:u.jsx(Aj,{})})}));
//# sourceMappingURL=index-e55071ad.js.map
