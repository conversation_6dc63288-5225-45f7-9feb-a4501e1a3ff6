{"version": 3, "sources": ["../../react-chartjs-2/src/utils.ts", "../../react-chartjs-2/src/chart.tsx", "../../react-chartjs-2/src/typedCharts.tsx"], "sourcesContent": ["import type { MouseEvent } from 'react';\nimport type {\n  ChartType,\n  ChartData,\n  DefaultDataPoint,\n  ChartDataset,\n  ChartOptions,\n  Chart,\n} from 'chart.js';\n\nimport type { ForwardedRef } from './types.js';\n\nconst defaultDatasetIdKey = 'label';\n\nexport function reforwardRef<T>(ref: ForwardedRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref) {\n    ref.current = value;\n  }\n}\n\nexport function setOptions<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(chart: Chart<TType, TData, TLabel>, nextOptions: ChartOptions<TType>) {\n  const options = chart.options;\n\n  if (options && nextOptions) {\n    Object.assign(options, nextOptions);\n  }\n}\n\nexport function setLabels<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextLabels: TLabel[] | undefined\n) {\n  currentData.labels = nextLabels;\n}\n\nexport function setDatasets<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextDatasets: ChartDataset<TType, TData>[],\n  datasetIdKey = defaultDatasetIdKey\n) {\n  const addedDatasets: ChartDataset<TType, TData>[] = [];\n\n  currentData.datasets = nextDatasets.map(\n    (nextDataset: Record<string, unknown>) => {\n      // given the new set, find it's current match\n      const currentDataset = currentData.datasets.find(\n        (dataset: Record<string, unknown>) =>\n          dataset[datasetIdKey] === nextDataset[datasetIdKey]\n      );\n\n      // There is no original to update, so simply add new one\n      if (\n        !currentDataset ||\n        !nextDataset.data ||\n        addedDatasets.includes(currentDataset)\n      ) {\n        return { ...nextDataset } as ChartDataset<TType, TData>;\n      }\n\n      addedDatasets.push(currentDataset);\n\n      Object.assign(currentDataset, nextDataset);\n\n      return currentDataset;\n    }\n  );\n}\n\nexport function cloneData<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(data: ChartData<TType, TData, TLabel>, datasetIdKey = defaultDatasetIdKey) {\n  const nextData: ChartData<TType, TData, TLabel> = {\n    labels: [],\n    datasets: [],\n  };\n\n  setLabels(nextData, data.labels);\n  setDatasets(nextData, data.datasets, datasetIdKey);\n\n  return nextData;\n}\n\n/**\n * Get dataset from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getDatasetAtEvent(\n  chart: Chart,\n  event: MouseEvent<HTMLCanvasElement>\n) {\n  return chart.getElementsAtEventForMode(\n    event.nativeEvent,\n    'dataset',\n    { intersect: true },\n    false\n  );\n}\n\n/**\n * Get single dataset element from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementAtEvent(\n  chart: Chart,\n  event: MouseEvent<HTMLCanvasElement>\n) {\n  return chart.getElementsAtEventForMode(\n    event.nativeEvent,\n    'nearest',\n    { intersect: true },\n    false\n  );\n}\n\n/**\n * Get all dataset elements from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementsAtEvent(\n  chart: Chart,\n  event: MouseEvent<HTMLCanvasElement>\n) {\n  return chart.getElementsAtEventForMode(\n    event.nativeEvent,\n    'index',\n    { intersect: true },\n    false\n  );\n}\n", "import React, { useEffect, useRef, forwardRef } from 'react';\nimport { Chart as ChartJS } from 'chart.js';\nimport type { ChartType, DefaultDataPoint } from 'chart.js';\n\nimport type { ForwardedRef, ChartProps, BaseChartComponent } from './types.js';\nimport {\n  reforwardRef,\n  cloneData,\n  setOptions,\n  setLabels,\n  setDatasets,\n} from './utils.js';\n\nfunction ChartComponent<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(\n  props: ChartProps<TType, TData, TLabel>,\n  ref: ForwardedRef<ChartJS<TType, TData, TLabel>>\n) {\n  const {\n    height = 150,\n    width = 300,\n    redraw = false,\n    datasetIdKey,\n    type,\n    data,\n    options,\n    plugins = [],\n    fallbackContent,\n    updateMode,\n    ...canvasProps\n  } = props;\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const chartRef = useRef<ChartJS<TType, TData, TLabel> | null>(null);\n\n  const renderChart = () => {\n    if (!canvasRef.current) return;\n\n    chartRef.current = new ChartJS(canvasRef.current, {\n      type,\n      data: cloneData(data, datasetIdKey),\n      options: options && { ...options },\n      plugins,\n    });\n\n    reforwardRef(ref, chartRef.current);\n  };\n\n  const destroyChart = () => {\n    reforwardRef(ref, null);\n\n    if (chartRef.current) {\n      chartRef.current.destroy();\n      chartRef.current = null;\n    }\n  };\n\n  useEffect(() => {\n    if (!redraw && chartRef.current && options) {\n      setOptions(chartRef.current, options);\n    }\n  }, [redraw, options]);\n\n  useEffect(() => {\n    if (!redraw && chartRef.current) {\n      setLabels(chartRef.current.config.data, data.labels);\n    }\n  }, [redraw, data.labels]);\n\n  useEffect(() => {\n    if (!redraw && chartRef.current && data.datasets) {\n      setDatasets(chartRef.current.config.data, data.datasets, datasetIdKey);\n    }\n  }, [redraw, data.datasets]);\n\n  useEffect(() => {\n    if (!chartRef.current) return;\n\n    if (redraw) {\n      destroyChart();\n      setTimeout(renderChart);\n    } else {\n      chartRef.current.update(updateMode);\n    }\n  }, [redraw, options, data.labels, data.datasets, updateMode]);\n\n  useEffect(() => {\n    if (!chartRef.current) return;\n\n    destroyChart();\n    setTimeout(renderChart);\n  }, [type]);\n\n  useEffect(() => {\n    renderChart();\n\n    return () => destroyChart();\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      role='img'\n      height={height}\n      width={width}\n      {...canvasProps}\n    >\n      {fallbackContent}\n    </canvas>\n  );\n}\n\nexport const Chart = forwardRef(ChartComponent) as BaseChartComponent;\n", "import React, { forwardRef } from 'react';\nimport {\n  Chart as <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Radar<PERSON><PERSON>roller,\n  <PERSON><PERSON>ut<PERSON><PERSON>roller,\n  PolarAreaController,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON>roller,\n  ScatterController,\n} from 'chart.js';\nimport type { ChartType, ChartComponentLike } from 'chart.js';\n\nimport type {\n  ChartProps,\n  ChartJSOrUndefined,\n  TypedChartComponent,\n} from './types.js';\nimport { Chart } from './chart.js';\n\nfunction createTypedChart<T extends ChartType>(\n  type: T,\n  registerables: ChartComponentLike\n) {\n  ChartJS.register(registerables);\n\n  return forwardRef<ChartJSOrUndefined<T>, Omit<ChartProps<T>, 'type'>>(\n    (props, ref) => <Chart {...props} ref={ref} type={type} />\n  ) as TypedChartComponent<T>;\n}\n\nexport const Line = /* #__PURE__ */ createTypedChart('line', LineController);\n\nexport const Bar = /* #__PURE__ */ createTypedChart('bar', BarController);\n\nexport const Radar = /* #__PURE__ */ createTypedChart('radar', RadarController);\n\nexport const Doughnut = /* #__PURE__ */ createTypedChart(\n  'doughnut',\n  DoughnutController\n);\n\nexport const PolarArea = /* #__PURE__ */ createTypedChart(\n  'polarArea',\n  PolarAreaController\n);\n\nexport const Bubble = /* #__PURE__ */ createTypedChart(\n  'bubble',\n  BubbleController\n);\n\nexport const Pie = /* #__PURE__ */ createTypedChart('pie', PieController);\n\nexport const Scatter = /* #__PURE__ */ createTypedChart(\n  'scatter',\n  ScatterController\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAYA,IAAMA,sBAAsB;AAErB,SAASC,aAAgBC,KAAsBC,OAAQ;AAC5D,MAAI,OAAOD,QAAQ,YAAY;AAC7BA,QAAIC,KAAAA;EACN,WAAWD,KAAK;AACdA,QAAIE,UAAUD;EAChB;AACF;AAEO,SAASE,WAIdC,OAAoCC,aAAgC;AACpE,QAAMC,UAAUF,MAAME;AAEtB,MAAIA,WAAWD,aAAa;AAC1BE,WAAOC,OAAOF,SAASD,WAAAA;EACzB;AACF;AAEO,SAASI,UAKdC,aACAC,YAAgC;AAEhCD,cAAYE,SAASD;AACvB;AAEO,SAASE,YAKdH,aACAI,cAA0C;AAC1CC,MAAAA,eAAAA,UAAejB,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAAA;AAEf,QAAMkB,gBAA8C,CAAA;AAEpDN,cAAYO,WAAWH,aAAaI,IAClC,CAACC,gBAAAA;AAEC,UAAMC,iBAAiBV,YAAYO,SAASI,KAC1C,CAACC,YACCA,QAAQP,YAAAA,MAAkBI,YAAYJ,YAAa,CAAA;AAIvD,QACE,CAACK,kBACD,CAACD,YAAYI,QACbP,cAAcQ,SAASJ,cACvB,GAAA;AACA,aAAO;QAAE,GAAGD;MAAY;IAC1B;AAEAH,kBAAcS,KAAKL,cAAAA;AAEnBb,WAAOC,OAAOY,gBAAgBD,WAAAA;AAE9B,WAAOC;EACT,CAAA;AAEJ;AAEO,SAASM,UAIdH,MAAqC;AAAER,MAAAA,eAAAA,UAAejB,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAAA;AACtD,QAAM6B,WAA4C;IAChDf,QAAQ,CAAA;IACRK,UAAU,CAAA;EACZ;AAEAR,YAAUkB,UAAUJ,KAAKX,MAAM;AAC/BC,cAAYc,UAAUJ,KAAKN,UAAUF,YAAAA;AAErC,SAAOY;AACT;AAQO,SAASC,kBACdxB,OACAyB,OAAoC;AAEpC,SAAOzB,MAAM0B,0BACXD,MAAME,aACN,WACA;IAAEC,WAAW;KACb,KAAA;AAEJ;AAQO,SAASC,kBACd7B,OACAyB,OAAoC;AAEpC,SAAOzB,MAAM0B,0BACXD,MAAME,aACN,WACA;IAAEC,WAAW;KACb,KAAA;AAEJ;AAQO,SAASE,mBACd9B,OACAyB,OAAoC;AAEpC,SAAOzB,MAAM0B,0BACXD,MAAME,aACN,SACA;IAAEC,WAAW;KACb,KAAA;AAEJ;ACzIA,SAASG,eAKPC,OACApC,KAAgD;AAEhD,QAAM,EACJqC,SAAS,KACTC,QAAQ,KACRC,SAAS,OACTxB,cACAyB,MACAjB,MACAjB,SACAmC,UAAU,CAAA,GACVC,iBACAC,YACA,GAAGC,YAAAA,IACDR;AACJ,QAAMS,gBAAYC,qBAA0B,IAAA;AAC5C,QAAMC,eAAWD,qBAA6C,IAAA;AAE9D,QAAME,cAAc,MAAA;AAClB,QAAI,CAACH,UAAU3C;AAAS;AAExB6C,aAAS7C,UAAU,IAAI+C,MAAQJ,UAAU3C,SAAS;MAChDsC;MACAjB,MAAMG,UAAUH,MAAMR,YAAAA;MACtBT,SAASA,WAAW;QAAE,GAAGA;MAAQ;MACjCmC;IACF,CAAA;AAEA1C,iBAAaC,KAAK+C,SAAS7C,OAAO;EACpC;AAEA,QAAMgD,eAAe,MAAA;AACnBnD,iBAAaC,KAAK,IAAA;AAElB,QAAI+C,SAAS7C,SAAS;AACpB6C,eAAS7C,QAAQiD,QAAO;AACxBJ,eAAS7C,UAAU;IACrB;EACF;AAEAkD,8BAAU,MAAA;AACR,QAAI,CAACb,UAAUQ,SAAS7C,WAAWI,SAAS;AAC1CH,iBAAW4C,SAAS7C,SAASI,OAAAA;IAC/B;KACC;IAACiC;IAAQjC;EAAQ,CAAA;AAEpB8C,8BAAU,MAAA;AACR,QAAI,CAACb,UAAUQ,SAAS7C,SAAS;AAC/BO,gBAAUsC,SAAS7C,QAAQmD,OAAO9B,MAAMA,KAAKX,MAAM;IACrD;KACC;IAAC2B;IAAQhB,KAAKX;EAAO,CAAA;AAExBwC,8BAAU,MAAA;AACR,QAAI,CAACb,UAAUQ,SAAS7C,WAAWqB,KAAKN,UAAU;AAChDJ,kBAAYkC,SAAS7C,QAAQmD,OAAO9B,MAAMA,KAAKN,UAAUF,YAAAA;IAC3D;KACC;IAACwB;IAAQhB,KAAKN;EAAS,CAAA;AAE1BmC,8BAAU,MAAA;AACR,QAAI,CAACL,SAAS7C;AAAS;AAEvB,QAAIqC,QAAQ;AACVW,mBAAAA;AACAI,iBAAWN,WAAAA;WACN;AACLD,eAAS7C,QAAQqD,OAAOZ,UAAAA;IAC1B;KACC;IAACJ;IAAQjC;IAASiB,KAAKX;IAAQW,KAAKN;IAAU0B;EAAW,CAAA;AAE5DS,8BAAU,MAAA;AACR,QAAI,CAACL,SAAS7C;AAAS;AAEvBgD,iBAAAA;AACAI,eAAWN,WAAAA;KACV;IAACR;EAAK,CAAA;AAETY,8BAAU,MAAA;AACRJ,gBAAAA;AAEA,WAAO,MAAME,aAAAA;EACf,GAAG,CAAA,CAAE;AAEL,SACE,aAAAM,QAACC,cAAAA,UAAAA;IACCzD,KAAK6C;IACLa,MAAK;IACLrB;IACAC;IACC,GAAGM;EAEHF,GAAAA,eAAAA;AAGP;AAEO,IAAMiB,aAAQC,yBAAWzB,cAAsC;AC7FtE,SAAS0B,iBACPrB,MACAsB,eAAiC;AAEjCb,QAAQc,SAASD,aAAAA;AAEjB,aAAOF,yBACL,CAACxB,OAAOpC,QAAQ,aAAAwD,QAACG,cAAAA,QAAAA;IAAO,GAAGvB;IAAOpC;IAAUwC;;AAEhD;IAEawB,OAAuBH,iBAAiB,QAAQI,cAAgB;IAEhEC,MAAsBL,iBAAiB,OAAOM,aAAe;IAE7DC,QAAwBP,iBAAiB,SAASQ,eAAiB;IAEnEC,WAA2BT,iBACtC,YACAU,kBACA;IAEWC,YAA4BX,iBACvC,aACAY,mBACA;IAEWC,SAAyBb,iBACpC,UACAc,gBACA;IAEWC,MAAsBf,iBAAiB,OAAOgB,aAAe;IAE7DC,UAA0BjB,iBACrC,WACAkB,iBACA;", "names": ["defaultDatasetIdKey", "reforwardRef", "ref", "value", "current", "setOptions", "chart", "nextOptions", "options", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "currentData", "<PERSON><PERSON><PERSON><PERSON>", "labels", "setDatasets", "nextDatasets", "datasetIdKey", "addedDatasets", "datasets", "map", "nextDataset", "currentDataset", "find", "dataset", "data", "includes", "push", "cloneData", "nextData", "getDatasetAtEvent", "event", "getElementsAtEventForMode", "nativeEvent", "intersect", "getElementAtEvent", "getElementsAtEvent", "ChartComponent", "props", "height", "width", "redraw", "type", "plugins", "fallback<PERSON><PERSON><PERSON>", "updateMode", "canvasProps", "canvasRef", "useRef", "chartRef", "<PERSON><PERSON><PERSON>", "ChartJS", "destroy<PERSON>hart", "destroy", "useEffect", "config", "setTimeout", "update", "React", "canvas", "role", "Chart", "forwardRef", "createTypedChart", "registerables", "register", "Line", "LineController", "Bar", "BarController", "Radar", "RadarController", "Doughnut", "DoughnutController", "PolarArea", "PolarAreaController", "Bubble", "BubbleController", "Pie", "PieController", "<PERSON><PERSON><PERSON>", "ScatterController"]}