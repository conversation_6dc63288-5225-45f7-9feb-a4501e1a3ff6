<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>404 - Page Not Found | Smart Library</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif; 
      text-align: center; 
      margin: 0;
      padding: 0;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      padding: 3rem 2rem;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      max-width: 500px;
      width: 90%;
    }
    
    h1 { 
      font-size: 8rem; 
      color: #e74c3c; 
      margin: 0;
      font-weight: 900;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
      line-height: 1;
    }
    
    p { 
      font-size: 1.5rem; 
      color: #2c3e50;
      margin: 1rem 0 2rem 0;
      font-weight: 500;
    }
    
    .actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    a {
      padding: 12px 24px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      display: inline-block;
    }
    
    .home-link {
      background: #3498db;
      color: white;
    }
    
    .home-link:hover {
      background: #2980b9;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }
    
    .login-link {
      background: #2ecc71;
      color: white;
    }
    
    .login-link:hover {
      background: #27ae60;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
    }
    
    .details {
      color: #7f8c8d;
      font-size: 1rem;
      margin-top: 2rem;
      line-height: 1.6;
    }
    
    @media (max-width: 768px) {
      h1 { font-size: 6rem; }
      p { font-size: 1.2rem; }
      .actions { flex-direction: column; align-items: center; }
      a { width: 200px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>404</h1>
    <p>Sorry, the page you are looking for was not found.</p>
    <div class="actions">
      <a href="/" class="home-link">Go Home</a>
      <a href="/login" class="login-link">Go to Login</a>
    </div>
    <div class="details">
      <p>The page might have been moved, deleted, or you entered the wrong URL.</p>
    </div>
  </div>
</body>
</html>
