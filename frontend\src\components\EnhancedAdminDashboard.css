/* Enhanced Admin Dashboard - Blue Theme */
.enhanced-admin-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 20%, #2563eb 40%, #3b82f6 60%, #60a5fa 80%, #93c5fd 100%);
  background-size: 400% 400%;
  animation: blueGradientShift 20s ease infinite;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Container and Dashboard Header */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: transparent;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(30px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-title {
  color: white !important;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

/* Header Actions */
.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.header-actions .btn {
  font-weight: 700;
  padding: 14px 28px;
  border-radius: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border: none;
  font-size: 16px;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.header-actions .btn.btn-success {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: 2px solid #38a169;
}
.header-actions .btn.btn-success:hover {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  transform: translateY(-3px);
}

.header-actions .btn.btn-info {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: 2px solid #3182ce;
}
.header-actions .btn.btn-info:hover {
  background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
  transform: translateY(-3px);
}

.header-actions .btn.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2px solid #764ba2;
}
.header-actions .btn.btn-primary:hover {
  background: linear-gradient(135deg, #764ba2 0%, #553c9a 100%);
  transform: translateY(-3px);
}

.header-actions .btn.btn-secondary {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  color: white;
  border: 2px solid #718096;
}
.header-actions .btn.btn-secondary:hover {
  background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
  transform: translateY(-3px);
}

@keyframes blueGradientShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 0%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}

/* Admin Header */
.admin-header {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(30px);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.admin-details h2 {
  color: white !important;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
}

.admin-details p {
  color: rgba(255, 255, 255, 0.8) !important;
  margin: 0;
  font-size: 0.875rem;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white !important;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Navigation Tabs */
.admin-nav {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 1rem 2rem;
  display: flex;
  gap: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-tab {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white !important;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.875rem;
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.nav-tab.active {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  border-color: #1e40af;
  box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

/* Admin Content */
.admin-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Overview Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 40px rgba(30, 64, 175, 0.2);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 800;
  color: #1e40af !important;
  margin: 0 0 0.5rem 0;
}

.stat-content p {
  color: #000000 !important;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.stat-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-breakdown span {
  color: #64748b !important;
  font-size: 0.75rem;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(226, 232, 240, 0.5);
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 4px;
  transition: width 0.6s ease;
}

.progress-fill.taken {
  background: linear-gradient(90deg, #1e40af 0%, #2563eb 100%);
}

/* Department Trends */
.department-trends {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.department-trends h3 {
  color: #1e40af !important;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0 0 1.5rem 0;
}

.trends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.trend-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.trend-card h4 {
  color: #1e40af !important;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.trend-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.trend-stats span {
  color: #000000 !important;
  font-size: 0.875rem;
  font-weight: 500;
}

.trend-progress {
  width: 100%;
  height: 6px;
  background: rgba(226, 232, 240, 0.5);
  border-radius: 3px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.trend-fill {
  height: 100%;
  background: linear-gradient(90deg, #1e40af 0%, #2563eb 100%);
  border-radius: 3px;
  transition: width 0.6s ease;
}

.percentage {
  color: #2563eb !important;
  font-weight: 700;
}

/* Books Management */
.admin-books {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.books-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.books-header h3 {
  color: #1e40af !important;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
}

.language-select {
  background: white;
  border: 2px solid rgba(30, 64, 175, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: #1e40af !important;
  font-weight: 600;
}

.books-table {
  overflow-x: auto;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.1);
  margin-top: 1rem;
}

.books-table table {
  width: 100%;
  min-width: 1400px; /* Ensure minimum width for proper alignment */
  border-collapse: collapse;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  table-layout: fixed; /* Fixed table layout for consistent column widths */
}

.books-table th {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  color: white !important;
  padding: 1.25rem 1rem;
  text-align: left;
  font-weight: 700;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

/* Fixed column widths for books table */
.books-table th:nth-child(1) { width: 100px; } /* Book ID */
.books-table th:nth-child(2) { width: 300px; } /* Title */
.books-table th:nth-child(3) { width: 200px; } /* Author */
.books-table th:nth-child(4) { width: 150px; } /* Genre */
.books-table th:nth-child(5) { width: 100px; } /* Language */
.books-table th:nth-child(6) { width: 100px; } /* Rack */
.books-table th:nth-child(7) { width: 120px; } /* Status */
.books-table th:nth-child(8) { width: 200px; } /* Taken By */
.books-table th:nth-child(9) { width: 130px; } /* Actions */

.books-table td {
  padding: 1.25rem 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  color: #000000 !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  vertical-align: top;
}

/* Specific styling for book table columns */
.books-table td:nth-child(1) {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  text-align: center;
  font-size: 0.875rem;
}

.books-table td:nth-child(2) {
  font-weight: 700;
  color: #1e40af !important;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.books-table td:nth-child(3) {
  color: #64748b !important;
  font-style: italic;
}

.books-table td:nth-child(4) {
  font-size: 0.875rem;
  color: #059669 !important;
  font-weight: 600;
}

.books-table td:nth-child(6) {
  font-family: 'Courier New', monospace;
  text-align: center;
  font-weight: 600;
}

.book-title {
  font-weight: 600;
  color: #1e40af !important;
}

.language-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.language-badge.english {
  background: rgba(16, 185, 129, 0.1);
  color: #059669 !important;
}

.language-badge.tamil {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626 !important;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.available {
  background: rgba(16, 185, 129, 0.1);
  color: #059669 !important;
}

.status-badge.taken {
  background: rgba(37, 99, 235, 0.1);
  color: #2563eb !important;
}

.action-btn {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  color: white !important;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

/* Feedback Section */
.admin-feedback {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.admin-feedback h3 {
  color: #1e40af !important;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0 0 1.5rem 0;
}

.feedback-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid rgba(30, 64, 175, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.employee-name {
  color: #1e40af !important;
  font-weight: 700;
}

.feedback-date {
  color: #64748b !important;
  font-size: 0.875rem;
}

.feedback-content p {
  color: #000000 !important;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.feedback-actions {
  display: flex;
  gap: 0.5rem;
}

.no-feedback {
  text-align: center;
  padding: 3rem;
  color: #64748b !important;
}

/* Real-time Status Header */
.real-time-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 15px;
  padding: 1rem 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e40af !important;
  font-weight: 600;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.last-updated {
  color: #64748b !important;
  font-size: 0.875rem;
}

/* Premium Stat Cards */
.stat-card.premium {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px solid rgba(30, 64, 175, 0.1);
  box-shadow:
    0 15px 35px rgba(30, 64, 175, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.stat-card.warning {
  border-left: 6px solid #f59e0b;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.stat-card.danger {
  border-left: 6px solid #ef4444;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.utilization-rate, .availability-rate, .reader-percentage {
  margin-top: 0.5rem;
}

.utilization-rate span, .availability-rate span, .reader-percentage span {
  color: #2563eb !important;
  font-weight: 700;
  font-size: 0.875rem;
  background: rgba(37, 99, 235, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.language-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin: 0.5rem 0;
}

.lang-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lang-label {
  color: #64748b !important;
  font-size: 0.75rem;
  font-weight: 600;
}

.lang-count {
  color: #1e40af !important;
  font-weight: 700;
  background: rgba(30, 64, 175, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
}

.urgency-indicator, .overdue-indicator {
  margin-top: 0.5rem;
}

.urgent, .critical {
  color: #dc2626 !important;
  font-weight: 700;
  font-size: 0.75rem;
  animation: warningPulse 2s infinite;
}

.good, .excellent {
  color: #059669 !important;
  font-weight: 700;
  font-size: 0.75rem;
}

/* Department Analytics */
.department-analytics {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h3 {
  color: #1e40af !important;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
}

.analytics-summary, .popularity-summary {
  color: #64748b !important;
  font-size: 0.875rem;
  font-weight: 500;
}

.department-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.department-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(30, 64, 175, 0.1);
  transition: all 0.3s ease;
}

.department-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.15);
}

.dept-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.dept-header h4 {
  color: #1e40af !important;
  font-weight: 700;
  margin: 0;
}

.books-count {
  color: #2563eb !important;
  font-weight: 700;
  font-size: 0.75rem;
  background: rgba(37, 99, 235, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.dept-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  color: #64748b !important;
  font-size: 0.875rem;
  font-weight: 500;
}

.metric-value {
  color: #1e40af !important;
  font-weight: 700;
}

.dept-progress .progress-bar {
  height: 8px;
  background: rgba(226, 232, 240, 0.5);
  border-radius: 4px;
}

.progress-fill.dept {
  background: linear-gradient(90deg, #1e40af 0%, #2563eb 100%);
}

.progress-fill.available {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

/* Popular Books Section */
.popular-books-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.popular-books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.popular-book-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(30, 64, 175, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.popular-book-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(30, 64, 175, 0.15);
}

.book-rank {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  color: white !important;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
}

.book-info h5 {
  color: #1e40af !important;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.book-info p {
  color: #64748b !important;
  margin: 0 0 0.5rem 0;
  font-size: 0.75rem;
}

.book-meta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.language-tag {
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 8px;
}

.language-tag.english {
  background: rgba(16, 185, 129, 0.1);
  color: #059669 !important;
}

.language-tag.tamil {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626 !important;
}

.times-taken {
  color: #2563eb !important;
  font-size: 0.625rem;
  font-weight: 600;
  background: rgba(37, 99, 235, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 8px;
}

/* Enhanced Loading */
.loading {
  text-align: center;
  padding: 4rem;
  color: white !important;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading h3 {
  color: white !important;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.loading p {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 1rem;
  margin: 0;
}

/* Employee Management Styles */
.admin-employees {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.employees-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.employees-header h3 {
  color: #1e40af !important;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
}

.employees-summary {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.employees-summary span {
  color: #64748b !important;
  font-size: 0.875rem;
  font-weight: 600;
  background: rgba(100, 116, 139, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 12px;
}

.employees-table {
  overflow-x: auto;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.1);
  margin-top: 1rem;
}

.employees-table table {
  width: 100%;
  min-width: 1200px; /* Ensure minimum width for proper alignment */
  border-collapse: collapse;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  table-layout: fixed; /* Fixed table layout for consistent column widths */
}

.employees-table th {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  color: white !important;
  padding: 1.25rem 1rem;
  text-align: left;
  font-weight: 700;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

/* Fixed column widths for proper alignment */
.employees-table th:nth-child(1) { width: 120px; } /* Employee ID */
.employees-table th:nth-child(2) { width: 200px; } /* Name */
.employees-table th:nth-child(3) { width: 150px; } /* Department */
.employees-table th:nth-child(4) { width: 200px; } /* Email */
.employees-table th:nth-child(5) { width: 250px; } /* Books Taken */
.employees-table th:nth-child(6) { width: 120px; } /* Status */
.employees-table th:nth-child(7) { width: 150px; } /* Actions */

.employees-table td {
  padding: 1.25rem 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  color: #000000 !important;
  vertical-align: top;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Specific column styling */
.employees-table td:nth-child(1) {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  text-align: center;
}

.employees-table td:nth-child(4) {
  font-size: 0.875rem;
  color: #2563eb !important;
}

.employees-table td:nth-child(5) {
  max-width: 250px;
  overflow: hidden;
}

.employee-name-cell strong {
  color: #1e40af !important;
  font-weight: 700;
  font-size: 1rem;
}

.books-taken-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.books-count {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  color: white !important;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 700;
  font-size: 0.875rem;
  display: inline-block;
  width: fit-content;
}

.books-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.book-item {
  background: rgba(248, 250, 252, 0.8);
  padding: 0.5rem;
  border-radius: 8px;
  border-left: 3px solid #2563eb;
}

.book-title {
  color: #1e40af !important;
  font-weight: 600;
  font-size: 0.75rem;
  display: block;
}

.due-date {
  color: #64748b !important;
  font-size: 0.625rem;
  font-weight: 500;
}

.due-date.overdue {
  color: #dc2626 !important;
  font-weight: 700;
}

.employee-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.employee-status.active {
  background: rgba(16, 185, 129, 0.1);
  color: #059669 !important;
}

.employee-status.inactive {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b !important;
}

.employee-status.overdue {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626 !important;
  animation: warningPulse 2s infinite;
}

/* Enhanced Feedback Styles */
.admin-feedback {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(30, 64, 175, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.feedback-header h3 {
  color: #1e40af !important;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
}

.feedback-summary span {
  color: #64748b !important;
  font-size: 0.875rem;
  font-weight: 600;
  background: rgba(100, 116, 139, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 12px;
}

.no-feedback {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b !important;
}

.no-feedback-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-feedback h4 {
  color: #1e40af !important;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.feedback-item {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(30, 64, 175, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feedback-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.15);
}

.feedback-employee {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.employee-name {
  color: #1e40af !important;
  font-weight: 700;
  font-size: 1rem;
}

.employee-id {
  color: #64748b !important;
  font-size: 0.75rem;
  font-weight: 500;
}

.feedback-date {
  color: #64748b !important;
  font-size: 0.875rem;
  font-weight: 500;
}

.feedback-content p {
  color: #000000 !important;
  line-height: 1.6;
  margin: 1rem 0;
  font-size: 0.95rem;
}

.feedback-type {
  margin: 1rem 0;
}

.feedback-tag {
  padding: 0.375rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feedback-tag.suggestion {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb !important;
}

.feedback-tag.complaint {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626 !important;
}

.feedback-tag.general {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b !important;
}

.feedback-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.action-btn.reply {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.action-btn.mark-read {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.action-btn.priority {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.action-btn.view {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.action-btn.edit {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .admin-nav {
    padding: 1rem;
    flex-wrap: wrap;
  }

  .admin-content {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .department-grid, .popular-books-grid {
    grid-template-columns: 1fr;
  }

  .books-header, .employees-header, .feedback-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .employees-summary {
    flex-direction: column;
    align-items: stretch;
  }

  .employees-table {
    font-size: 0.875rem;
  }

  .employees-table th,
  .employees-table td {
    padding: 0.75rem 0.5rem;
  }

  .feedback-actions {
    flex-wrap: wrap;
  }
}
