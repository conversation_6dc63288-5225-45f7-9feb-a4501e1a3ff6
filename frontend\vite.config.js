import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import fs from 'fs'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  },
  configureServer(server) {
    server.middlewares.use((req, res, next) => {
      // Only handle GET requests that are not for static files or API
      if (
        req.method === 'GET' &&
        !req.url.startsWith('/api') &&
        !req.url.includes('.') &&
        req.url !== '/'
      ) {
        const notFoundPath = path.resolve(__dirname, 'public/404.html')
        if (fs.existsSync(notFoundPath)) {
          res.statusCode = 404
          res.setHeader('Content-Type', 'text/html')
          res.end(fs.readFileSync(notFoundPath))
          return
        }
      }
      next()
    })
  }
})
